package com.desaysv.workserver.data_structure;


import org.apache.commons.collections4.queue.CircularFifoQueue;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

public class CircularFifoBlockingQueue<E> extends CircularFifoQueue<E> {
    private final Condition notEmpty;
    final ReentrantLock lock;


    public CircularFifoBlockingQueue(int capacity) {
        super(capacity);
        lock = new ReentrantLock();
        notEmpty = lock.newCondition();
    }

    @Override
    public boolean add(E element) {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            boolean b = super.add(element);
            notEmpty.signal();
            return b;
        } finally {
            lock.unlock();
        }
    }

    public E take() throws InterruptedException {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            while (size() == 0)
                notEmpty.await();
            return poll();
        } finally {
            lock.unlock();
        }
    }
}