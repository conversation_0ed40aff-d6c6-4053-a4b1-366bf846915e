package com.desaysv.workserver.utils;

import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ExceptionUtils {

    public static String getExceptionString(Throwable e) {
        //读取异常栈信息
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(arrayOutputStream));
        //通过ByteArray转换输入输出流
        BufferedReader fr = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(arrayOutputStream.toByteArray())));
        String str;
        StringBuilder exceptionStr = new StringBuilder();
        try {
            while ((str = fr.readLine()) != null) {
                exceptionStr.append(str).append("\n");
            }
            //关闭流
            fr.close();
        } catch (IOException ex) {
            return ex.toString();
        }
        return exceptionStr.toString();
    }

    public static List<StackTraceElement> getStackTrace() {
        List<StackTraceElement> list = getStackTrace(0);
        return list.subList(1, list.size());
    }

    public static String getStackTrace(Throwable e) {
        return org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(e);
    }

    public static List<StackTraceElement> getStackTrace(int layer) {
        List<StackTraceElement> stackTraceElementList = Arrays.stream(Thread.currentThread().getStackTrace()).collect(Collectors.toList());
        List<StackTraceElement> subList = stackTraceElementList.subList(2, stackTraceElementList.size());
        if (layer > 0) {
            return subList.subList(0, layer);
        } else {
            return subList;
        }
    }


}
