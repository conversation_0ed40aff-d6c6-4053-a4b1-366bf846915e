package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件工具类
 */
@Slf4j
public class FileUtils {

    //\/:*?"<>|
    public static String replaceSpecialChar(String fileName) {
        return fileName.replaceAll("\\\\", "{backslash}")
                .replaceAll("/", "{slash}")
                .replaceAll(":", "{colon}")
                .replaceAll("\\*", "{asterisk}")
                .replaceAll("\\?", "{question}")
                .replaceAll("\"", "{double_quotation}")
                .replaceAll("<", "{less_than}")
                .replaceAll(">", "{more_than}")
                .replaceAll("\\|", "{vertical_line}");
    }

    public static String readStringFromFile(File file) throws IOException {
        return readStringFromFile(file, StandardCharsets.UTF_8.name());
    }

    public static String readStringFromFile(File file, String charsetName) throws IOException {
        FileInputStream fin = new FileInputStream(file);
        InputStreamReader reader = new InputStreamReader(fin, charsetName);
        BufferedReader buffReader = new BufferedReader(reader);
        String buf;
        StringBuilder sb = new StringBuilder();
        while ((buf = buffReader.readLine()) != null) {
            sb.append(buf).append("\n");
        }
        buffReader.close();
        return sb.toString();
    }

    public static String getFileNameByDate(String fileName) {
        return String.format("%s_%s", fileName, StrUtils.simpleDateFileName());
    }

    /**
     * 拷贝文件夹所有文件到指定目录
     *
     * @param source      源文件夹
     * @param destination 目标文件夹
     * @throws IOException
     */
    public static void copyFolder(File source, File destination) throws IOException {
        File[] files = source.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            org.apache.commons.io.FileUtils.copyFileToDirectory(file, destination);
        }
    }

    /**
     * 更新文件夹所有文件到指定目录
     *
     * @param dir         源文件夹
     * @param destination 目标文件夹
     * @throws IOException
     */
    public static void copyFolderWhenLastModified(File dir, File destination) throws IOException {
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            copyFileToDirectoryWhenLastModified(file, destination);
        }
    }

    public static void copyFileInJar(String filePathInJar, File destFolder) throws IOException {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePathInJar);
        assert inputStream != null;
        org.apache.commons.io.FileUtils.copyToFile(inputStream, destFolder);
        log.info("拷贝{}到{}", filePathInJar, destFolder);
    }

    public static void copyFileToDirectoryWhenLastModified(File file, File destination) throws IOException {
        File dstFile = new File(destination, file.getName());
        boolean noCopy = dstFile.exists() && file.lastModified() >= dstFile.lastModified();
        if (!noCopy) {
            org.apache.commons.io.FileUtils.copyFileToDirectory(file, destination);
            log.info("拷贝{}到{}", file, destination);
        }
    }

    public static boolean copyResourceToDirectoryWhenLastModified(Resource resource, File destination) throws IOException {
        String filename = resource.getFilename();
        assert filename != null;
        File dstFile = new File(destination, filename);
        boolean noCopy = dstFile.exists() && dstFile.lastModified() >= resource.lastModified();
        if (!noCopy) {
            if (!StrUtils.isEmpty(filename)) {
                log.info("拷贝{}到{}", filename, destination);
                org.apache.commons.io.FileUtils.copyToFile(resource.getInputStream(), dstFile);
            }
            return true;
        }
        return false;
    }

    public static boolean deleteAllFile(File file) {
        return deleteAllFile(file, false);
    }

    public static boolean deleteAllFile(File file, boolean isIncludeRoot) {
        // 如果dir对应的文件不存在，则退出
        if (!file.exists()) {
            return false;
        }

        if (file.isFile()) {
            return file.delete();
        }

        File[] fileList = file.listFiles();
        boolean res = true;
        assert fileList != null;
        for (File f : fileList) {
            if (f.isFile()) {
                res = res && f.delete();
            } else if (f.isDirectory()) {
                res = res && deleteAllFile(f, true);
            }
        }

        if (isIncludeRoot) {
            res = res && file.delete();
        }

        return res;
    }


    public static boolean writeFileFromString(String path, String data) {
        return writeFileFromString(path, data, false);
    }

    public static boolean writeFileFromString(String path, String data, boolean append) {
        return writeFileFromString(new File(path), data, append);
    }

    public static boolean writeFileFromString(File file, String data) {
        return writeFileFromString(file, data, false);
    }

    public static boolean writeFileFromString(File file, String data, boolean append) {
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            if (data != null) {
                OutputStreamWriter writer = new OutputStreamWriter(
                        new FileOutputStream(file, append), StandardCharsets.UTF_8);
                writer.write(data);
                writer.flush();
                writer.close();
            }
            return true;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public static String readFileToString(String path) {
        return readFileToString(new File(path));
    }

    public static String readFileToString(File file) {
        if (!file.exists() || !file.isFile()) {
            log.error("文件不存在或不是文件：{}", file.getAbsolutePath());
            return null;
        }
        StringBuilder sb = new StringBuilder();
        try (FileInputStream fileInputStream = new FileInputStream(file);
             BufferedReader reader = new BufferedReader(new InputStreamReader(fileInputStream, StandardCharsets.UTF_8))) {
            int ch;
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
        } catch (IOException e) {
            log.error("读取文件时出错: {}", file.getAbsolutePath(), e);
        }
        return sb.toString();
    }

    public static List<String> findFiles(String directoryPath, String prefix, String suffix) {
        List<String> matchingFiles = new ArrayList<>();
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            log.info("无效目录: {}", directoryPath);
            return matchingFiles;
        }
        File[] files = directory.listFiles((dir, name) -> name.startsWith(prefix) && name.endsWith(suffix));
        if (files != null) {
            for (File file : files) {
                matchingFiles.add(file.getAbsolutePath());
            }
        }
        return matchingFiles;
    }


    public static String findAppFilePath(String directoryPath) {
        String appFilePath = null;
        List<String> appMatchingFiles = findFiles(directoryPath, "App", ".s19");
        for (String filePath : appMatchingFiles) {
            appFilePath = filePath;
        }
        return appFilePath;
    }


    public static String findFlashDriverFilePath(String directoryPath) {
        String flashDriverFilePath = null;
        List<String> flashDriverMatchingFiles = findFiles(directoryPath, "FlashDriver", ".s19");
        for (String filePath : flashDriverMatchingFiles) {
            flashDriverFilePath = filePath;
        }
        return flashDriverFilePath;
    }


    //写一个函数判断字符串是否能符合文件名命名规则
    public static boolean isValidFileName(String fileName) {
        String regex = "^[a-zA-Z0-9_-]{1,30}$";
        return fileName.matches(regex);
    }
}
