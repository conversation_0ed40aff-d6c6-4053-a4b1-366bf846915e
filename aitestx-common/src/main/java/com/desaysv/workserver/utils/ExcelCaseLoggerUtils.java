package com.desaysv.workserver.utils;

import ch.qos.logback.classic.Logger;
import lombok.Getter;
import org.slf4j.LoggerFactory;

import java.io.File;

public class ExcelCaseLoggerUtils {
    @Getter
    private static final Logger logger = (Logger) LoggerFactory.getLogger(ActionLogUtils.class);

    public static void setAppender(String tag, File folder) {
        logger.detachAndStopAllAppenders();
        logger.addAppender(ActionLogUtils.createFileAppender(FileUtils.replaceSpecialChar(tag), folder));
    }

    public static void removeFileAppender() {
        logger.detachAndStopAllAppenders();
    }


}
