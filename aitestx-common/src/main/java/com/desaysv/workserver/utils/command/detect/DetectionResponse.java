package com.desaysv.workserver.utils.command.detect;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class DetectionResponse {
    private List<Detection> detections;
    @JsonProperty("detection_count")
    private int detectionCount;
    @JsonProperty("original_shape")
    private int[] originalShape;

}
