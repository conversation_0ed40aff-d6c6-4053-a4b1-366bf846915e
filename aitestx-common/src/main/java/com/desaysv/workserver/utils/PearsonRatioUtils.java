package com.desaysv.workserver.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 皮尔逊相关系数工具类
 */
public class PearsonRatioUtils {

    public static double getPearsonByDim(double[] ratingOne, double[] ratingTwo) {
        return getPearsonByDim(Arrays.stream(ratingOne).boxed().collect(Collectors.toList()),
                Arrays.stream(ratingTwo).boxed().collect(Collectors.toList()));
    }

    /**
     * 相关系数的值介于–1与+1之间，即–1≤r≤+1。其性质如下：
     * 当r>0时，表示两变量正相关，r<0时，两变量为负相关。
     * 当|r|=1时，表示两变量为完全线性相关，即为函数关系。
     * 当r=0时，表示两变量间无线性相关关系。
     * 当0<|r|<1时，表示两变量存在一定程度的线性相关。
     * 且|r|越接近1，两变量间线性关系越密切；
     * |r|越接近于0，表示两变量的线性相关越弱。
     * 一般可按三级划分：|r|<0.4为低度线性相关；
     * 0.4≤|r|<0.7为显著性相关；0.7≤|r|<1为高度线性相关。
     *
     * @param x
     * @param y
     * @return
     */
    public static double getPearsonByDim(List<Double> x, List<Double> y) {
//        try {
        if (x.size() != y.size()) {//两个变量的观测值是成对的，每对观测值之间相互独立。
            if (x.size() > y.size()) {//保留小的处理大
                List<Double> temp = x;
                x = new ArrayList<>();
                for (int i = 0; i < y.size(); i++) {
                    x.add(temp.get(i));
                }
            } else {
                List<Double> temp = y;
                y = new ArrayList<>();
                for (int i = 0; i < x.size(); i++) {
                    y.add(temp.get(i));
                }
            }
        }
        double sim;//最后的皮尔逊相关度系数
        double commonItemsLen = x.size();//操作数的个数
        double oneSum = 0D;//第一个相关数的和
        double twoSum = 0D;//第二个相关数的和
        double oneSqSum = 0D;//第一个相关数的平方和
        double twoSqSum = 0D;//第二个相关数的平方和
        double oneTwoSum = 0D;//两个相关数的乘积和
        for (int i = 0; i < x.size(); i++) {//计算
            double oneTemp = x.get(i);
            double twoTemp = y.get(i);
            //求和
            oneSum += oneTemp;
            twoSum += twoTemp;
            oneSqSum += Math.pow(oneTemp, 2);
            twoSqSum += Math.pow(twoTemp, 2);
            oneTwoSum += oneTemp * twoTemp;
        }
        double num = (commonItemsLen * oneTwoSum) - (oneSum * twoSum);
        double den = Math.sqrt((commonItemsLen * oneSqSum - Math.pow(oneSum, 2)) * (commonItemsLen * twoSqSum - Math.pow(twoSum, 2)));
        sim = (den == 0) ? 1 : num / den;
        return sim;
//        } catch (Exception e) {
//            return 0.0;
//        }
    }

    public static double getPearsonCorrelationScore(List<Double> x, List<Double> y) {
        if (x.size() != y.size())
            throw new RuntimeException("数据不正确！");
        double[] xData = new double[x.size()];
        double[] yData = new double[x.size()];
        for (int i = 0; i < x.size(); i++) {
            xData[i] = x.get(i);
            yData[i] = y.get(i);
        }
        return getPearsonCorrelationScore(xData, yData);
    }

    public static double getPearsonCorrelationScore(double[] xData, double[] yData) {
        if (xData.length != yData.length)
            throw new RuntimeException("数据不正确！");
        double xMeans;
        double yMeans;
        double numerator;// 求解皮尔逊的分子
        double denominator;// 求解皮尔逊系数的分母

        double result;
        // 拿到两个数据的平均值
        xMeans = getMeans(xData);
        yMeans = getMeans(yData);
        // 计算皮尔逊系数的分子
        numerator = generateNumerator(xData, xMeans, yData, yMeans);
        // 计算皮尔逊系数的分母
        denominator = generateDenomiator(xData, xMeans, yData, yMeans);
        // 计算皮尔逊系数
        result = numerator / denominator;
        return result;
    }

    /**
     * 计算分子
     *
     * @param xData
     * @param xMeans
     * @param yData
     * @param yMeans
     * @return
     */
    private static double generateNumerator(double[] xData, double xMeans, double[] yData, double yMeans) {
        double numerator = 0.0;
        for (int i = 0; i < xData.length; i++) {
            numerator += (xData[i] - xMeans) * (yData[i] - yMeans);
        }
        return numerator;
    }

    /**
     * 生成分母
     *
     * @param yMeans
     * @param yData
     * @param xMeans
     * @param xData
     * @return 分母
     */
    private static double generateDenomiator(double[] xData, double xMeans, double[] yData, double yMeans) {
        double xSum = 0.0;
        for (int i = 0; i < xData.length; i++) {
            xSum += (xData[i] - xMeans) * (xData[i] - xMeans);
        }
        double ySum = 0.0;
        for (int i = 0; i < yData.length; i++) {
            ySum += (yData[i] - yMeans) * (yData[i] - yMeans);
        }
        return Math.sqrt(xSum) * Math.sqrt(ySum);
    }

    /**
     * 根据给定的数据集进行平均值计算
     *
     * @param
     * @return 给定数据集的平均值
     */
    private static double getMeans(double[] datas) {
        double sum = 0.0;
        for (int i = 0; i < datas.length; i++) {
            sum += datas[i];
        }
        return sum / datas.length;
    }
}