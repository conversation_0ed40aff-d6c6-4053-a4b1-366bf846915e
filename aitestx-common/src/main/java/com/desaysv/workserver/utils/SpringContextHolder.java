package com.desaysv.workserver.utils;

import lombok.Getter;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Objects;

/**
 * Spring的环境获取
 */
@Component
@Lazy
public class SpringContextHolder implements ApplicationContextAware {

    @Getter
    private static ApplicationContext applicationContext;

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (SpringContextHolder.applicationContext == null) {
            SpringContextHolder.applicationContext = applicationContext;
        }
    }

    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    public static Object getBeanAndExecute(String name, String methodName, Class<?>[] parameterTypes, Object[] args) throws InvocationTargetException, IllegalAccessException {
        Object o = getApplicationContext().getBean(name);
        Objects.requireNonNull(ReflectionUtils.findMethod(o.getClass(), methodName, parameterTypes)).invoke(o, args);
        return o;
    }

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return getApplicationContext() == null ? null : getApplicationContext().getBean(name, clazz);
    }

    public static String getProfile() {
        return getApplicationContext().getEnvironment().getActiveProfiles()[0];
    }

    public static boolean isDevEnvironment() {
//        return applicationContext.getEnvironment().getActiveProfiles().length > 0 &&
//                applicationContext.getEnvironment().getActiveProfiles()[0].equals("dev");
        return getProfile().equals("dev");
    }

    public static String getEnvironmentProperty(String key) {
        return getApplicationContext().getEnvironment().getProperty(key);
    }


}
