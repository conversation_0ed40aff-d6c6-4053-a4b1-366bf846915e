package com.desaysv.workserver.utils;

import com.desaysv.workserver.exceptions.ReflectMethodsNotFoundException;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.util.ReflectionUtils;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.net.JarURLConnection;
import java.net.URL;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;

/**
 * 反射工具类
 * TODO:合并@see org.springframework.util.ReflectionUtils
 */
@Slf4j
public class ReflectUtils {

    @Data
    public static class MethodReflection implements Comparable<MethodReflection> {

        private String methodName;
        private Class<?> returnType;
        private Class<?>[] paramTypes;
        private Class<?>[] genericType;
        private Method method;

        @Override
        public int compareTo(MethodReflection methodReflection) {
            return methodReflection.getParamTypes().length - paramTypes.length;
        }


        @Override
        public String toString() {
            return String.format("MethodReflection(methodName=%s, paramTypes=%s, returnType=%s)",
                    methodName,
                    Arrays.stream(paramTypes).map(Class::getSimpleName).collect(Collectors.toList()),
                    returnType.getSimpleName());
        }

    }

    /**
     * 定义类集合（用于存放所有加载的类）
     */
    private static final Set<Class<?>> CLASS_SET;

    static {
        //指定加载包路径
        CLASS_SET = getClassSet("com.desaysv.workserver");
    }

    public static boolean getMethod(Method method, Class<?> parameterType, String parameterName) {
        for (Parameter parameter : method.getParameters()) {
            if (parameter.getName().equals(parameterName) && parameter.getType() == parameterType) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取类加载器
     *
     * @return
     */
    public static ClassLoader getClassLoader() {
        return Thread.currentThread().getContextClassLoader();
    }

    static class A {
        private int name;
        private int age;
        private int kkk;
    }

    @SuppressWarnings("unchecked")
    public static <T> Map<String, T> getStaticProperties(Class<?> clazz) throws Exception {
        Map<String, T> map = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            int mod = field.getModifiers();
            if (Modifier.isPrivate(mod)) {
                continue;
            }
            if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                map.put(field.getName(), (T) getStaticProperty(clazz, field.getName()));
            }
        }
        return map;
    }

    public static Object getStaticProperty(Class<?> clazz, String fieldName)
            throws Exception {
        Field field = clazz.getField(fieldName);
        return field.get(clazz);
    }

    /**
     * 将对象转换为map
     *
     * @param bean 对象
     * @param <T>  对象类型
     * @return 对象类型
     */
    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = Maps.newHashMap();
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key.toString(), beanMap.get(key));
            }
        }
        return map;
    }

    public static String[] getJsonAliasValues(Class<?> clazz) {
        List<String> jsonAliasValues = new ArrayList<>();
        for (Field field : ReflectUtils.getDeclaredFieldIncludingSuper(clazz)) {
            Annotation[] annotations = field.getDeclaredAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation.annotationType() == JsonAlias.class) {
                    try {
                        Method method = annotation.annotationType().getMethod("value");
                        String[] values = (String[]) method.invoke(annotation);
                        jsonAliasValues.addAll(Arrays.asList(values));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
        return jsonAliasValues.toArray(new String[0]);
    }

    public static String[] getFieldNamesIncludingSuper(Object object) {
        return getFieldNamesIncludingSuper(object.getClass());
    }

    public static String[] getFieldNamesIncludingSuper(Class<?> clazz) {
        Field[] fields = getDeclaredFieldIncludingSuper(clazz);
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    public static Field[] getDeclaredFieldIncludingSuper(Object object) {
        Class<?> clazz = object.getClass();
        return getDeclaredFieldIncludingSuper(clazz);
    }

    public static Field[] getDeclaredFieldIncludingSuper(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        for (; clazz != null && clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    public static String[] getFiledNames(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    public static List<Object> getFieldValues(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<Object> fieldValues = new ArrayList<>();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                if (Modifier.isStatic(field.getModifiers()))
                    fieldValues.add(field.get(clazz));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return fieldValues;
    }

    public static String toElegantString(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        StringBuilder sb = new StringBuilder();
        try {
            sb.append("\n");
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                Object value = field.get(o);
                sb.append(String.format("%s=%s,%n", name, value));
            }
            sb.deleteCharAt(sb.lastIndexOf(","));
            return sb.toString();
        } catch (IllegalAccessException e) {
            return String.valueOf(o);
        }
    }

    public static Object getFieldValueByName(Object o, String fieldName) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter);
            return method.invoke(o);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static Object getFiledType(String fieldName, Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (Objects.equals(fieldName, field.getName())) {
                return field.getType();
            }
        }
        return null;
    }

    public static Object getPropertyValue(Object obj, String propertyName) throws IllegalAccessException {
        Class<?> clazz = obj.getClass();
        Field field;
        if ((field = getField(clazz, propertyName)) == null)
            return null;
        field.setAccessible(true);
        return field.get(clazz);
    }

    public static Field getField(Class<?> clazz, String propertyName) {
        if (clazz == null)
            return null;
        try {
            return clazz.getDeclaredField(propertyName);
        } catch (NoSuchFieldException e) {
            return getField(clazz.getSuperclass(), propertyName);
        }
    }


    /**
     * 加载类
     *
     * @param className     类全限定名称
     * @param isInitialized 是否在加载完成后执行静态代码块
     * @return
     */
    public static Class<?> loadClass(String className, boolean isInitialized) {
        Class<?> cls;
        try {
            cls = Class.forName(className, isInitialized, getClassLoader());
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        return cls;
    }

    public static Class<?> loadClass(String className) {
        return loadClass(className, true);
    }

    /**
     * 获取指定包下所有类
     *
     * @param packageName
     * @return
     */
    public static Set<Class<?>> getClassSet(String packageName) {
        Set<Class<?>> classSet = new HashSet<>();
        try {
            Enumeration<URL> urls = getClassLoader().getResources(packageName.replace(".", "/"));
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) {
                        String packagePath = url.getPath().replace("%20", "");
                        addClass(classSet, packagePath, packageName);
                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    if (jarEntryName.endsWith(".class")) {
                                        String className = jarEntryName.substring(0, jarEntryName.lastIndexOf(".")).replaceAll("/", ".");
                                        doAddClass(classSet, className);
                                    }
                                }
                            }
                        }
                    }
                }
            }


        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return classSet;
    }

    private static void doAddClass(Set<Class<?>> classSet, String className) {
        Class<?> cls = loadClass(className, false);
        classSet.add(cls);
    }

    private static void addClass(Set<Class<?>> classSet, String packagePath, String packageName) {
        final File[] files = new File(packagePath).listFiles(new FileFilter() {
            @Override
            public boolean accept(File file) {
                return (file.isFile() && file.getName().endsWith(".class")) || file.isDirectory();
            }
        });
        assert files != null;
        for (File file : files) {
            String fileName = file.getName();
            if (file.isFile()) {
                String className = fileName.substring(0, fileName.lastIndexOf("."));
                if (StringUtils.isNotEmpty(packageName)) {
                    className = packageName + "." + className;
                }
                doAddClass(classSet, className);
            } else {
                String subPackagePath = fileName;
                if (StringUtils.isNotEmpty(packagePath)) {
                    subPackagePath = packagePath + "/" + subPackagePath;
                }
                String subPackageName = fileName;
                if (StringUtils.isNotEmpty(packageName)) {
                    subPackageName = packageName + "." + subPackageName;
                }
                addClass(classSet, subPackagePath, subPackageName);
            }
        }
    }


    public static Set<Class<?>> getClassSet() {
        return CLASS_SET;
    }

    /**
     * 获取应用包名下某父类（或接口）的所有子类（或实现类）
     *
     * @param superClass
     * @return
     */
    public static Set<Class<?>> getClassSetBySuper(Class<?> superClass) {
        Set<Class<?>> classSet = new HashSet<>();
        for (Class<?> cls : CLASS_SET) {
            if (superClass.isAssignableFrom(cls) && !superClass.equals(cls)) {
                classSet.add(cls);
            }
        }
        return classSet;
    }

    /**
     * 获取应用包名下带有某注解的类
     *
     * @param annotationClass
     * @return
     */
    public static Set<Class<?>> getClassSetByAnnotation(Class<? extends Annotation> annotationClass) {
        Set<Class<?>> classSet = new HashSet<>();
        for (Class<?> cls : CLASS_SET) {
            if (cls.isAnnotationPresent(annotationClass)) {
                classSet.add(cls);
            }
        }
        return classSet;
    }

    private static Method getInterfaces(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        Class<?>[] interfaces = clazz.getInterfaces();
        for (Class<?> intf : interfaces) {
            try {
                // 处理接口的方法
                return intf.getDeclaredMethod(methodName, parameterTypes);
            } catch (NoSuchMethodException e) {
                // 这里什么都不需要做
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredMethod
     *
     * @param object         : 子类对象
     * @param methodName     : 父类中的方法名
     * @param parameterTypes : 父类中的方法参数类型
     * @return 父类中的方法对象
     */
    @Deprecated
    public static Method getDeclaredMethod(Object object, String methodName, Class<?>... parameterTypes) {
        Method method;

        for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                method = clazz.getDeclaredMethod(methodName, parameterTypes);
                return method;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
                // 获取接口的方法
                Method intfMethod = getInterfaces(clazz, methodName, parameterTypes);
                if (intfMethod != null) {
                    return intfMethod;
                }
            }
        }
        return null;
    }

    /**
     * 判断对象是否有方法签名
     *
     * @param object         : 子类对象
     * @param methodName     : 父类中的方法名
     * @param parameterTypes : 父类中的方法参数类型
     * @return
     */
    public static boolean hasMethod(Object object,
                                    String methodName,
                                    Class<?>[] parameterTypes) {
        return ReflectionUtils.findMethod(object.getClass(), methodName, parameterTypes) != null;
    }

    public static boolean hasEmptyParamMethod(Object object, String methodName) {
        return ReflectionUtils.findMethod(object.getClass(), methodName) != null;
    }

    /**
     * 直接调用对象方法, 而忽略修饰符(private, protected, default)
     *
     * @param object         : 子类对象
     * @param methodName     : 父类中的方法名
     * @param parameterTypes : 父类中的方法参数类型
     * @param parameters     : 父类中的方法参数
     * @return 父类中方法的执行结果
     */
    public static Object invokeMethod(Object object,
                                      String methodName,
                                      Class<?>[] parameterTypes,
                                      Object[] parameters) throws ReflectMethodsNotFoundException {
        //根据 对象、方法名和对应的方法参数 通过反射 调用上面的方法获取 Method 对象
        Method method = ReflectionUtils.findMethod(object.getClass(), methodName, parameterTypes);
        if (method == null) {
            throw new ReflectMethodsNotFoundException();
        }
        //抑制Java对方法进行检查,主要是针对私有方法而言
        method.setAccessible(true);
        //调用object 的 method 所代表的方法，其方法的参数是 parameters
        try {
            return method.invoke(object, parameters);
        } catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException e) {
            throw new ReflectMethodsNotFoundException(e);
        }
    }

    public static Object invokeEmptyParamMethod(Object object, String methodName) throws ReflectMethodsNotFoundException {
        Method method = ReflectionUtils.findMethod(object.getClass(), methodName);
        if (method == null) {
            throw new ReflectMethodsNotFoundException();
        }
        //抑制Java对方法进行检查,主要是针对私有方法而言
        method.setAccessible(true);
        //调用object 的 method 所代表的方法，其方法的参数是 parameters
        try {
            return method.invoke(object);
        } catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException e) {
            throw new ReflectMethodsNotFoundException(e);
        }
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredField
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @return 父类中的属性对象
     */
    public static Field getDeclaredField(Object object, String fieldName) {
        Field field;
        Class<?> clazz = object.getClass();

        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                field = clazz.getDeclaredField(fieldName);
                return field;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }


    /**
     * 直接设置对象属性值, 忽略 private/protected 修饰符, 也不经过 setter
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @param value     : 将要设置的值
     */
    public static void setFieldValue(Object object, String fieldName, Object value) {
        //根据 对象和属性名通过反射 调用上面的方法获取Field对象
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            //抑制Java对其的检查
            field.setAccessible(true);

            try {
                //将 object 中 field 所代表的值 设置为 value
                field.set(object, value);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 直接读取对象的属性值, 忽略 private/protected 修饰符, 也不经过 getter
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @return : 父类中的属性值
     */
    public static Object getFieldValue(Object object, String fieldName) {
        //根据 对象和属性名通过反射 调用上面的方法获取Field对象
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            //抑制Java对其的检查
            field.setAccessible(true);

            try {
                //获取 object 中 field 所代表的属性值
                return field.get(object);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    public static List<MethodReflection> getReflectionByMethodName(Object object, String methodName) {
        // 要获取类的信息，首先要获取类的类型
        Class<?> clazz = object.getClass();
        return getReflectionByMethodName(clazz, methodName);
    }

    public static List<MethodReflection> getReflectionByMethodName(Class<?> clazz, String methodName) {
        List<MethodReflection> methodReflectionList = new ArrayList<>();
        //不访问protected方法
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                MethodReflection methodReflection = new MethodReflection();
                methodReflection.setMethodName(method.getName());
                Class<?> returnType = method.getReturnType();
                methodReflection.setReturnType(returnType);
                // 获取参数类型
                Class<?>[] paramTypes = method.getParameterTypes();
                methodReflection.setParamTypes(paramTypes);

                // 获取泛型参数类型
                Type[] genericParamTypes = method.getGenericParameterTypes();
                List<Class<?>> classList = new ArrayList<>();
                for (Type type : genericParamTypes) {
                    if (type instanceof ParameterizedType) {
                        Type[] actualTypeArgs = ((ParameterizedType) type).getActualTypeArguments();
                        if (actualTypeArgs.length > 0 && actualTypeArgs[0] instanceof Class) {
                            classList.add((Class<?>) actualTypeArgs[0]);
                        }
                    }
                }
                methodReflection.setGenericType(classList.toArray(new Class<?>[]{}));
                methodReflection.setMethod(method);
                methodReflectionList.add(methodReflection);
            }
        }
        Collections.sort(methodReflectionList);
//        Collections.sort(methodReflectionList, new Comparator<MethodReflection>() {
//            @Override
//            public int compare(MethodReflection o1, MethodReflection o2) {
//                if (o1.getParamTypes().length == 0 || o2.getParamTypes().length == 0) {
//                    return 0;
//                }
//                return ClassUtil.isSimpleValueType(o1.getParamTypes()[0]) || byte[].class.isAssignableFrom(o1.getParamTypes()[0]) ? 1 : -1;
//            }
//        });
        return methodReflectionList;
    }


}