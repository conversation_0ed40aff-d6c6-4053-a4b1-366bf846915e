package com.desaysv.workserver.utils;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.FileAppender;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Paths;


public class ActionLogUtils {
    public static FileAppender<ILoggingEvent> createFileAppender(String tagName, File folder) {
        return createFileAppender(tagName, folder.getAbsolutePath());
    }

    // 创建 FileAppender
    public static FileAppender<ILoggingEvent> createFileAppender(String tagName, String folder) {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        FileAppender<ILoggingEvent> appender = new FileAppender<>();
        appender.setName(tagName);
        appender.setFile(Paths.get(folder, tagName + ".log").toFile().getAbsolutePath());
        // 设置 FileAppender 的编码器和上下文
        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
//        encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n");
        encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n");
        encoder.setContext(context);
        encoder.start();

        appender.setEncoder(encoder);
        appender.setAppend(true);
        appender.setContext(context);
        appender.start();

        return appender;
    }


}