package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Slf4j
public class ThreadSafeFileUtils {

    private static final Map<String, ReadWriteLock> FILE_LOCKS = new ConcurrentHashMap<>();

    // 获取文件锁
    private static ReadWriteLock getFileLock(String filePath) {
        return FILE_LOCKS.computeIfAbsent(filePath, path -> new ReentrantReadWriteLock());
    }

    // 获取文件锁（File对象版本）
    private static ReadWriteLock getFileLock(File file) {
        return getFileLock(file.getAbsolutePath());
    }

    /**
     * 从锁映射中移除文件锁
     */
    private static void removeFileLock(String filePath) {
        FILE_LOCKS.remove(filePath);
    }

    /**
     * 从锁映射中移除文件锁（File对象版本）
     */
    private static void removeFileLock(File file) {
        removeFileLock(file.getAbsolutePath());
    }

    // === 以下是与FileUtils同名的线程安全方法 ===

    public static String readFileToString(String path) {
        return readFileToString(new File(path));
    }

    public static String readFileToString(File file) {
        if (!file.exists()) {
            log.error("文件不存在: {}", file.getAbsolutePath());
            return null;
        }
        ReadWriteLock rwLock = getFileLock(file);
        rwLock.readLock().lock();
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r");
             FileChannel channel = randomAccessFile.getChannel()) {

            long fileSize = channel.size();
            ByteBuffer buffer = ByteBuffer.allocate((int) fileSize);
            channel.read(buffer);
            buffer.flip();

            return new String(buffer.array(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取文件失败: {}", file.getAbsolutePath(), e);
            return null;
        } finally {
            rwLock.readLock().unlock();
        }
    }

    public static boolean writeFileFromString(String path, String data) {
        return writeFileFromString(path, data, false);
    }

    public static boolean writeFileFromString(String path, String data, boolean append) {
        return writeFileFromString(new File(path), data, append);
    }

    public static boolean writeFileFromString(File file, String data) {
        return writeFileFromString(file, data, false);
    }

    public static boolean writeFileFromString(File file, String data, boolean append) {
        ReadWriteLock rwLock = getFileLock(file);
        rwLock.writeLock().lock();
        try {
            Path filePath = file.toPath();
            if (filePath.getParent() != null) {
                Files.createDirectories(filePath.getParent());
            }

            try (FileChannel channel = FileChannel.open(filePath,
                    StandardOpenOption.CREATE,
                    StandardOpenOption.WRITE,
                    append ? StandardOpenOption.APPEND : StandardOpenOption.TRUNCATE_EXISTING)) {

                ByteBuffer buffer = ByteBuffer.wrap(data.getBytes(StandardCharsets.UTF_8));
                while (buffer.hasRemaining()) {
                    channel.write(buffer);
                }
                channel.force(true); // 强制写入磁盘
                return true;
            }
        } catch (IOException e) {
            log.error("写入文件失败: {}", file.getAbsolutePath(), e);
            return false;
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的删除文件方法
     */
    public static boolean deleteAllFile(File file) {
        return deleteAllFile(file, false);
    }

    /**
     * 线程安全的删除文件方法，可选择是否包含根目录
     */
    public static boolean deleteAllFile(File file, boolean isIncludeRoot) {
        if (!file.exists()) {
            return false;
        }

        String filePath = file.getAbsolutePath();
        ReadWriteLock rwLock = getFileLock(filePath);
        rwLock.writeLock().lock();
        try {
            boolean result;

            if (file.isFile()) {
                result = FileUtils.deleteAllFile(file, isIncludeRoot);
                // 如果成功删除文件，从映射中移除锁
                if (result) {
                    removeFileLock(filePath);
                }
                return result;
            }

            // 处理目录的情况
            File[] fileList = file.listFiles();
            boolean res = true;
            if (fileList != null) {
                for (File f : fileList) {
                    if (f.isFile()) {
                        // 获取文件的锁并删除
                        ReadWriteLock childLock = getFileLock(f);
                        childLock.writeLock().lock();
                        try {
                            boolean deleted = f.delete();
                            if (deleted) {
                                removeFileLock(f);
                            }
                            res = res && deleted;
                        } finally {
                            childLock.writeLock().unlock();
                        }
                    } else if (f.isDirectory()) {
                        // 递归删除子目录
                        res = res && deleteAllFile(f, true);
                    }
                }
            }

            // 如果需要删除根目录
            if (isIncludeRoot) {
                boolean deleted = file.delete();
                if (deleted) {
                    removeFileLock(filePath);
                }
                res = res && deleted;
            }

            return res;
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的删除单个文件方法
     *
     * @param file 要删除的文件
     * @return 删除是否成功
     */
    public static boolean deleteFile(File file) {
        if (!file.exists()) {
            log.info("文件不存在，无需删除: {}", file.getAbsolutePath());
            return false;
        }

        if (file.isDirectory()) {
            log.error("不能用deleteFile删除目录: {}", file.getAbsolutePath());
            return false;
        }

        ReadWriteLock rwLock = getFileLock(file);
        rwLock.writeLock().lock();
        try {
            Path path = file.toPath();
            Files.deleteIfExists(path);
            // 删除成功后，从映射中移除锁
            removeFileLock(file);
            log.info("删除文件成功: {}", file.getAbsolutePath());
            return true;
        } catch (IOException e) {
            log.error("删除文件失败: {}", file.getAbsolutePath(), e);
            return false;
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的删除单个文件方法
     *
     * @param filePath 要删除的文件路径
     * @return 删除是否成功
     */
    public static boolean deleteFile(String filePath) {
        return deleteFile(new File(filePath));
    }

    /**
     * 线程安全的文件重命名方法
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 重命名是否成功
     */
    public static boolean renameFile(File sourceFile, File targetFile) {
        if (!sourceFile.exists()) {
            log.error("源文件不存在: {}", sourceFile.getAbsolutePath());
            return false;
        }

        // 确保目标文件的父目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            try {
                Files.createDirectories(parentDir.toPath());
            } catch (IOException e) {
                log.error("无法创建目标目录: {}", parentDir.getAbsolutePath(), e);
                return false;
            }
        }

        // 按照路径字典序排序锁的获取顺序，防止死锁
        String sourcePath = sourceFile.getAbsolutePath();
        String targetPath = targetFile.getAbsolutePath();

        ReadWriteLock firstLock;
        ReadWriteLock secondLock;
        boolean isSourceFirst = sourcePath.compareTo(targetPath) < 0;

        if (isSourceFirst) {
            firstLock = getFileLock(sourceFile);
            secondLock = getFileLock(targetFile);
        } else {
            firstLock = getFileLock(targetFile);
            secondLock = getFileLock(sourceFile);
        }

        // 按顺序获取锁
        firstLock.writeLock().lock();
        try {
            secondLock.writeLock().lock();
            try {
                try {
                    Files.move(sourceFile.toPath(), targetFile.toPath(),
                            StandardCopyOption.REPLACE_EXISTING);
                    // 重命名成功后，移除源文件的锁
                    removeFileLock(sourceFile);
                    return true;
                } catch (IOException e) {
                    log.error("重命名文件失败: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath(), e);
                    return false;
                }
            } finally {
                secondLock.writeLock().unlock();
            }
        } finally {
            firstLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的文件复制方法 - 复制到目录
     *
     * @param sourceFile 源文件
     * @param targetDir  目标目录
     * @return 复制是否成功
     */
    public static boolean copyFileToDirectory(File sourceFile, File targetDir) {
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            log.error("源文件不存在或不是文件: {}", sourceFile.getAbsolutePath());
            return false;
        }

        // 确保目标目录存在
        try {
            if (!targetDir.exists()) {
                Files.createDirectories(targetDir.toPath());
            }
        } catch (IOException e) {
            log.error("无法创建目标目录: {}", targetDir.getAbsolutePath(), e);
            return false;
        }

        File targetFile = new File(targetDir, sourceFile.getName());

        // 源文件获取读锁，目标文件获取写锁
        ReadWriteLock sourceLock = getFileLock(sourceFile);
        ReadWriteLock targetLock = getFileLock(targetFile);

        // 按照路径字典序排序锁的获取顺序，防止死锁
        String sourcePath = sourceFile.getAbsolutePath();
        String targetPath = targetFile.getAbsolutePath();

        boolean isSourceFirst = sourcePath.compareTo(targetPath) < 0;
        ReadWriteLock firstLock = isSourceFirst ? sourceLock : targetLock;
        ReadWriteLock secondLock = isSourceFirst ? targetLock : sourceLock;

        // 先获取第一个锁
        firstLock.writeLock().lock();
        try {
            // 再获取第二个锁
            secondLock.writeLock().lock();
            try {
                try {
                    Files.copy(sourceFile.toPath(), targetFile.toPath(),
                            StandardCopyOption.REPLACE_EXISTING);
                    return true;
                } catch (IOException e) {
                    log.error("复制文件失败: {} -> {}", sourceFile.getAbsolutePath(), targetDir.getAbsolutePath(), e);
                    return false;
                }
            } finally {
                secondLock.writeLock().unlock();
            }
        } finally {
            firstLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的文件复制方法 - 复制到文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 复制是否成功
     */
    public static boolean copyFile(File sourceFile, File targetFile) {
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            log.error("源文件不存在或不是文件: {}", sourceFile.getAbsolutePath());
            return false;
        }

        // 确保目标文件的父目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            try {
                Files.createDirectories(parentDir.toPath());
            } catch (IOException e) {
                log.error("无法创建目标目录: {}", parentDir.getAbsolutePath(), e);
                return false;
            }
        }

        // 源文件获取读锁，目标文件获取写锁
        ReadWriteLock sourceLock = getFileLock(sourceFile);
        ReadWriteLock targetLock = getFileLock(targetFile);

        // 按照路径字典序排序锁的获取顺序，防止死锁
        String sourcePath = sourceFile.getAbsolutePath();
        String targetPath = targetFile.getAbsolutePath();

        boolean isSourceFirst = sourcePath.compareTo(targetPath) < 0;
        ReadWriteLock firstLock = isSourceFirst ? sourceLock : targetLock;
        ReadWriteLock secondLock = isSourceFirst ? targetLock : sourceLock;

        // 先获取第一个锁
        firstLock.writeLock().lock();
        try {
            // 再获取第二个锁
            secondLock.writeLock().lock();
            try {
                try {
                    Files.copy(sourceFile.toPath(), targetFile.toPath(),
                            StandardCopyOption.REPLACE_EXISTING);
                    return true;
                } catch (IOException e) {
                    log.error("复制文件失败: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath(), e);
                    return false;
                }
            } finally {
                secondLock.writeLock().unlock();
            }
        } finally {
            firstLock.writeLock().unlock();
        }
    }

    /**
     * 线程安全的文件复制方法 - 基于路径
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 复制是否成功
     */
    public static boolean copyFile(String sourcePath, String targetPath) {
        return copyFile(new File(sourcePath), new File(targetPath));
    }

}
