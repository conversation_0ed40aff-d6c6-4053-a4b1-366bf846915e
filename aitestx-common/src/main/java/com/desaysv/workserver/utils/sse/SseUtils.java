package com.desaysv.workserver.utils.sse;

import com.desaysv.workserver.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE工具类
 */
@Slf4j
public class SseUtils {
    // 订阅表
    private static final Map<String, SseEmitter> subscribeMap = new ConcurrentHashMap<>();
    private static final String DEFAULT_SUBSCRIBE_ID = "default";

    /**
     * 创建并配置新的SSE发射器
     */
    private static SseEmitter createEmitter(String subscribeId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);

        // 使用方法引用简化代码
        emitter.onCompletion(() -> removeSubscription(subscribeId));
        emitter.onTimeout(() -> {
            emitter.complete();
            removeSubscription(subscribeId);
        });
        emitter.onError(throwable -> {
            log.error("SSE异常,消息ID: {}，{}", subscribeId, throwable.getMessage());
            emitter.completeWithError(throwable);
            removeSubscription(subscribeId);
        });
        return emitter;
    }

    /**
     * 移除订阅
     */
    private static void removeSubscription(String subscribeId) {
        subscribeMap.remove(subscribeId);
        log.debug("移除SSE消息ID:{}", subscribeId);
    }

    /**
     * 添加订阅
     */
    public static  SseEmitter addSub(String subscribeId) {
        if (subscribeId == null || subscribeId.isEmpty()) {
            log.warn("SSE尝试发布一个空的消息ID或消息");
            // 返回或创建默认的SseEmitter
            return subscribeMap.computeIfAbsent(DEFAULT_SUBSCRIBE_ID, key -> {
                log.debug("创建默认SSE消息: {}", key);
                return createEmitter(key);
            });
        }
        return subscribeMap.computeIfAbsent(subscribeId, key -> {
            log.info("创建SSE消息: {}", key);
            return createEmitter(key);
        });
    }

    /**
     * 异步发送消息
     */
    public static void asyncPubMsg(String subscribeId, Object msg) {
        CompletableFuture.runAsync(() -> pubMsg(subscribeId, msg))
                .exceptionally(e -> {
                    log.error("SSE异步消息{}通知异常: {}", subscribeId, e.getMessage());
                    return null;
                });
    }


    /**
     * 发送消息 - 如果订阅不存在则自动创建
     */
    public static void pubMsg(String subscribeId, Object msg) {
        if (subscribeId == null || msg == null) {
            log.warn("SSE尝试发布一个空的消息ID或消息");
            return;
        }

        // 如果订阅不存在，自动创建
        SseEmitter emitter = Optional.ofNullable(subscribeMap.get(subscribeId))
                .orElseGet(() -> addSub(subscribeId));

        if (emitter != null) {
            try {
                emitter.send(msg, MediaType.APPLICATION_JSON);
                log.debug("发送SSE消息ID: {}成功", subscribeId);
            } catch (Exception e) {
                log.error("发送SSE消息ID: {}失败: {}", subscribeId, e.getMessage());
                emitter.completeWithError(e);
            }
        }
    }



    /**
     * 输出运行日志
     */
    public static void outputRunLog(String runLog) {
        asyncPubMsg(SseConstants.RUN_LOG, String.format("%s %s", DateUtils.getNow(true), runLog));
    }

    /**
     * 关闭订阅
     */
    public static synchronized void closeSub(String subscribeId) {
        Optional.ofNullable(subscribeMap.get(subscribeId))
                .ifPresent(emitter -> {
                    try {
                        emitter.complete();
                        removeSubscription(subscribeId);
                        log.info("成功关闭SSE消息ID: {}", subscribeId);
                    } catch (Exception e) {
                        log.error("错误关闭SSE消息ID {}: {}", subscribeId, e.getMessage());
                        emitter.completeWithError(e);
                    }
                });
    }

}
