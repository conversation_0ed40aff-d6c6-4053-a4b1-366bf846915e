package com.desaysv.workserver.utils;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.indexer.FloatIndexer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Scalar;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.bytedeco.opencv.global.opencv_imgproc.*;

@Slf4j
public class ImageUtils {

    public static void main(String[] args) {
        System.out.println(ImageUtils.splitTemplateName("a&bc|de"));
    }

    /**
     * 产生随机颜色的图像
     *
     * @param width  图像宽度
     * @param height 图像高度
     * @return
     */
    public static BufferedImage generateRandomColorImage(int width, int height) {
        // 创建一个指定宽高的BufferedImage
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        // 获取Graphics2D对象
        Graphics2D g2d = image.createGraphics();

        // 生成一个随机颜色
        Random random = new Random();
        Color randomColor = new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256));

        // 设置颜色并填充整个图像
        g2d.setColor(randomColor);
        g2d.fillRect(0, 0, width, height);

        // 释放资源
        g2d.dispose();

        return image;
    }

    public static void createBlackImage(String targetFileName) {
        BufferedImage blackImage = new BufferedImage(1920, 1080, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = blackImage.createGraphics();
        g2d.setColor(Color.BLACK);
        g2d.fillRect(0, 0, blackImage.getWidth(), blackImage.getHeight());
        g2d.dispose();
        try {
            ImageIO.write(blackImage, "png", new File(targetFileName));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 分离图像模板名称
     *
     * @param templateName 图像模板名称
     * @return
     */
    public static List<String> splitTemplateName(String templateName) {
        List<String> result = new ArrayList<>();
        Matcher matcher = Pattern.compile("([^&|]+|[&|])").matcher(templateName);
        while (matcher.find()) {
            result.add(matcher.group());
        }
        return result;
    }

    /**
     * 获取mat的字节数组
     *
     * @param mat Mat对象
     * @return
     */
    public static byte[] getRawImageData(Mat mat) {
        // 获取图像字节数
        int imageSize = (int) (mat.total() * mat.elemSize());
        // 创建字节数组
        byte[] imageData = new byte[imageSize];
        // 将图像数据复制到字节数组
        mat.data().get(imageData);
        return imageData;
    }

    /**
     * 裁剪BufferedImage图像
     *
     * @param bufferedImage BufferedImage图像
     * @param rect          矩形框
     * @return
     */
    public static Frame crop(BufferedImage bufferedImage, Rect rect) {
        try (Frame frame = Java2DFrameUtils.toFrame(bufferedImage)) {
            return crop(frame, rect);
        }
    }

    /**
     * 裁剪Frame图像
     *
     * @param frame Frame图像
     * @param rect  矩形框
     * @return
     */
    public static Frame crop(Frame frame, Rect rect) {
        if (frame == null || rect == null) {
            throw new IllegalArgumentException("Frame或ROI不能为空");
        }
        try (Mat frame2Mat = Java2DFrameUtils.toMat(frame)) {
            if (frame2Mat.empty() || !frame2Mat.isContinuous()) {
                throw new IllegalStateException("无效的图像数据");
            }
            // Verify if the rect is within the frame boundaries
            if (rect.x() < 0 || rect.y() < 0 ||
                    rect.x() + rect.width() > frame2Mat.cols() ||
                    rect.y() + rect.height() > frame2Mat.rows()) {
                throw new IllegalArgumentException("ROI超出范围");
            }
            try (Mat croppedMat = new Mat(frame2Mat, rect)) { //正确释放Mat
                // 将croppedMat转换为帧对象
                return Java2DFrameUtils.toFrame(croppedMat);
            }
        }
    }

    /**
     * 产生随机颜色
     *
     * @return
     */
    public static Scalar randColor() {
        int b, g, r;
        b = ThreadLocalRandom.current().nextInt(0, 255 + 1);
        g = ThreadLocalRandom.current().nextInt(0, 255 + 1);
        r = ThreadLocalRandom.current().nextInt(0, 255 + 1);
        return new Scalar(b, g, r, 0);
    }

    /**
     * 获取阈值以上的矩阵点
     *
     * @param m Mat对象
     * @param t 阈值
     * @return
     */
    public static List<Point> getPointsFromMatAboveThreshold(Mat m, float t) {
        List<Point> matches = new ArrayList<>();
        FloatIndexer indexer = m.createIndexer();
        for (int y = 0; y < m.rows(); y++) {
            for (int x = 0; x < m.cols(); x++) {
                if (indexer.get(y, x) > t) {
//                    System.out.println("(" + x + "," + y + ") = " + indexer.get(y, x));
                    matches.add(new Point(x, y));
                }
            }
        }
        return matches;
    }

    /**
     * 打印图像格式
     *
     * @param frame Frame图像
     */
    public static void printFrame(Frame frame) {
        log.debug("\nimageChannels:{}\nimageDepth:{}\nimageHeight:{}\nimageWidth:{}\nimageStride:{}\nsampleRate:{}\n",
                frame.imageChannels, frame.imageDepth, frame.imageHeight,
                frame.imageWidth, frame.imageStride, frame.sampleRate);
    }

    /**
     * 写入Frame图像
     *
     * @param frame    Frame图像
     * @param filePath 文件路径
     * @return
     * @throws IOException
     */
    public static boolean writeFrame(Frame frame, String filePath) throws IOException {
        return ImageIO.write(Java2DFrameUtils.toBufferedImage(frame), "png", new File(filePath));
    }

    /**
     * 应用二值化
     *
     * @param mat Mat对象
     * @return
     */
    public static Mat applyBinarization(Mat mat) {
        //新建Mat存储
        try (Mat targetImage = new Mat()) {
//        threshold(mat, targetImage,  127, 255, THRESH_BINARY | THRESH_OTSU);
            // 使用高斯加权平均
            adaptiveThreshold(mat, targetImage, 255, ADAPTIVE_THRESH_GAUSSIAN_C, THRESH_BINARY, 21, 5);
            return targetImage;
        }
    }

    /**
     * 保存Mat对象
     *
     * @param originalMat Mat对象
     * @param fileName    文件名
     */
    public static void saveMat(Mat originalMat, String fileName) {
        BufferedImage image;
        try {
            image = Java2DFrameUtils.toBufferedImage(originalMat);
            ImageIO.write(image, "png", new File(fileName));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Data
    public static class MatGroup {
        private Mat refMat;
        private Mat targetMat;
    }

    public static MatGroup adjustFrame(Frame referenceImage, Frame targetImage) {
        MatGroup matGroup = new MatGroup();
        // 复制目标图像以进行调整
        Mat referenceClonedImage = Java2DFrameUtils.toMat(referenceImage.clone());
        Mat targetClonedImage = Java2DFrameUtils.toMat(targetImage.clone());

        opencv_imgproc.cvtColor(referenceClonedImage, referenceClonedImage, opencv_imgproc.COLOR_BGR2GRAY);
        opencv_imgproc.cvtColor(targetClonedImage, targetClonedImage, opencv_imgproc.COLOR_BGR2GRAY);

        // 将图像转换为灰度图像并均衡化直方图
        opencv_imgproc.equalizeHist(referenceClonedImage, referenceClonedImage);
        opencv_imgproc.equalizeHist(targetClonedImage, targetClonedImage);

        // 计算参考图像和目标图像的均值
        double referenceMean = opencv_core.mean(referenceClonedImage).get(0);
        double targetMean = opencv_core.mean(targetClonedImage).get(0);

        double ratio = referenceMean / targetMean;
        opencv_core.multiply(targetClonedImage, ratio);

        matGroup.setRefMat(referenceClonedImage);
        matGroup.setTargetMat(targetClonedImage);
        return matGroup;
    }

    /**
     * 在图像上绘制虚线矩形
     *
     * @param mat        目标图像矩阵
     * @param rect       要绘制的矩形
     * @param color      矩形线条颜色
     * @param thickness  线条粗细
     * @param dashLength 虚线段长度
     * @param gapLength  虚线间隔长度
     */
    public static void drawDashedRectangle(Mat mat, Rect rect, Scalar color, int thickness, int dashLength, int gapLength) {
        // 校验输入参数
        if (mat == null || rect == null || color == null) {
            return;
        }

        int lineType = LINE_AA;

        // 绘制上边线
        for (int x = rect.x(); x < rect.x() + rect.width(); x += dashLength + gapLength) {
            int endX = Math.min(x + dashLength, rect.x() + rect.width());
            opencv_imgproc.line(mat, new Point(x, rect.y()), new Point(endX, rect.y()), color, thickness, lineType, 0);
        }

        // 绘制下边线
        for (int x = rect.x(); x < rect.x() + rect.width(); x += dashLength + gapLength) {
            int endX = Math.min(x + dashLength, rect.x() + rect.width());
            opencv_imgproc.line(mat, new Point(x, rect.y() + rect.height()), new Point(endX, rect.y() + rect.height()), color, thickness, lineType, 0);
        }

        // 绘制左边线
        for (int y = rect.y(); y < rect.y() + rect.height(); y += dashLength + gapLength) {
            int endY = Math.min(y + dashLength, rect.y() + rect.height());
            opencv_imgproc.line(mat, new Point(rect.x(), y), new Point(rect.x(), endY), color, thickness, lineType, 0);
        }

        // 绘制右边线
        for (int y = rect.y(); y < rect.y() + rect.height(); y += dashLength + gapLength) {
            int endY = Math.min(y + dashLength, rect.y() + rect.height());
            opencv_imgproc.line(mat, new Point(rect.x() + rect.width(), y), new Point(rect.x() + rect.width(), endY), color, thickness, lineType, 0);
        }
    }

    public static boolean isSolidColorImage(Mat image) {
        return isSolidColorImage(image, 6);
    }

    public static boolean isSolidColorImage(Mat image, double threshold) {
        // 创建一个副本以避免修改原始图像
        try (Mat workingImage = image.clone();
             // 计算均值和标准差
             Mat mean = new Mat();
             Mat stddev = new Mat();
        ) {

            // 如果是彩色图像则转换为灰度图
            if (workingImage.channels() > 1) {
                opencv_imgproc.cvtColor(workingImage, workingImage, opencv_imgproc.COLOR_BGR2GRAY);
            }

            opencv_core.meanStdDev(workingImage, mean, stddev);

            // 获取标准差值
            double standardDeviation = stddev.createIndexer().getDouble();
            return standardDeviation < threshold;
        }
    }

}
