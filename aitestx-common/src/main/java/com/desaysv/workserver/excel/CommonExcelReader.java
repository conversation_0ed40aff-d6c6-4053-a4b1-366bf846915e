package com.desaysv.workserver.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import lombok.Data;
import org.apache.poi.openxml4j.util.ZipSecureFile;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

@Data
public class CommonExcelReader {

    private static final Map<ExcelEntity, CommonExcelReader> excelReaderMap = new HashMap<>();

    private ExcelReaderBuilder excelReaderBuilder;

    private ExcelListener excelListener;

    private ExcelEntity excelEntity;

    private CommonExcelReader() {

    }

    public static CommonExcelReader getExcelReader(ExcelEntity excelEntity) {
        CommonExcelReader commonExcelReader = excelReaderMap.get(excelEntity);
        if (commonExcelReader == null) {
//            File excelFile = new File(excelEntity.getTemplateFilePath());   //mark by haiyu, 重启应用要加载源文件
            File excelFile = new File(excelEntity.isReloadProject() ? excelEntity.getTemplateFilePath() : excelEntity.getOriginalFilePath());
            if (excelFile.exists()) {
                commonExcelReader = new CommonExcelReader();
                ExcelListener excelListener = new ExcelListener();
                ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(excelFile, excelListener);
                commonExcelReader.setExcelListener(excelListener);
                commonExcelReader.setExcelReaderBuilder(excelReaderBuilder);
                commonExcelReader.setExcelEntity(excelEntity);
                excelReaderMap.put(excelEntity, commonExcelReader);
            }
        }
//        System.out.println("commonExcelReader:" + commonExcelReader);
        return commonExcelReader;
    }

    public static void adjustZipRatio() {
        // 设置最小压缩比为 0.001
        ZipSecureFile.setMinInflateRatio(0.001);
        ZipSecureFile.setMaxFileCount(10000);//允许最多条目数
    }

    public void read(int headRowNumber, String sheetName) {
        adjustZipRatio();
//        .readCache(new Ehcache())
        excelReaderBuilder.registerConverter(new SafeStringConverter())
                .registerReadListener(new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                })
                .ignoreEmptyRow(Boolean.FALSE)
                .headRowNumber(headRowNumber)
                .sheet(sheetName)
                .doRead();
    }

    public Map<String, ExcelSheetTable> getExcelSheetMap() {
        return excelListener.getExcelTableMap();
    }

    public ExcelSheetTable getExcelSheetTable(String sheetName) {
        return excelListener.getExcelTableMap().get(sheetName);
    }

}
