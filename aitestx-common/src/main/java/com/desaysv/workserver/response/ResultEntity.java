package com.desaysv.workserver.response;

import lombok.Data;

@Data
public class ResultEntity<T> {
    public static final Integer SUCCESS = 0;
    public static final Integer FAIL = -1;
    protected Integer code = 0;
    protected String message = "";
    protected T data;

    public ResultEntity() {

    }

    public ResultEntity(T data) {
        this.setData(data);
    }

    public ResultEntity(Integer code) {
        this.setCode(code);
    }

    public ResultEntity(Integer code, String message) {
        this.setCode(code);
        this.setMessage(message);
    }

    public ResultEntity(T data, Integer code, String message) {
        this.setData(data);
        this.setCode(code);
        this.setMessage(message);
    }

    public boolean isOk() {
        return code.equals(SUCCESS);
    }

    public static <T> ResultEntity<T> ok() {
        return ok("请求成功", null);
    }

    public static <T> ResultEntity<T> data(T data) {
        return ok("请求成功", data);
    }

    public static ResultEntity<String> ok(String message) {
        return ok(message, null);
    }

    public static <T> ResultEntity<T> ok(T data) {
        return ok("请求成功", data);
    }

    public static <T> ResultEntity<T> ok(String message, T data) {
        ResultEntity<T> body = new ResultEntity<>();
        body.setCode(SUCCESS);
        body.setMessage(message);
        body.setData(data);
        return body;
    }

    public static <T> ResultEntity<T> fail(String message, T data) {
        ResultEntity<T> body = new ResultEntity<>();
        body.setCode(FAIL);
        body.setMessage(message);
        body.setData(data);
        return body;
    }

    public static <T> ResultEntity<T> fail(String message) {
        return fail(message, null);
    }

    public static <T> ResultEntity<T> fail(T data) {
        return fail("请求失败", data);
    }

    public static <T> ResultEntity<T> fail() {
        return fail("请求失败", null);
    }
}