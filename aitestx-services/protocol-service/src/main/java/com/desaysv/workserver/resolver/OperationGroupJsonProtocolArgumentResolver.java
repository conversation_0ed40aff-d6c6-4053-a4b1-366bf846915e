package com.desaysv.workserver.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.OperationGroupJsonProtocol;
import com.desaysv.workserver.base.operation.base.OperationGroupJsonObject;
import com.desaysv.workserver.protocol.OperationGroupProtocolFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 13:54
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Component
//no lazy
public class OperationGroupJsonProtocolArgumentResolver extends JsonProtocolArgumentResolver {

    @Autowired
    private OperationGroupProtocolFactory operationGroupProtocolFactory;

    public OperationGroupJsonProtocolArgumentResolver() {
        addResolver(this);
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(OperationGroupJsonProtocol.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        OperationGroupJsonObject operationGroupJsonObject = JSONObject.parseObject(getRequestString(nativeWebRequest), OperationGroupJsonObject.class);
        return operationGroupProtocolFactory.product(operationGroupJsonObject);
    }
}
