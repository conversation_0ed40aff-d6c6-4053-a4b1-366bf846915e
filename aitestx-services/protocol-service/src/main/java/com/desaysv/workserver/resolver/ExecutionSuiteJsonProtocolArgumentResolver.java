package com.desaysv.workserver.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.ExecutionSuiteJsonProtocol;
import com.desaysv.workserver.base.execution.ExecutionSuiteJsonObject;
import com.desaysv.workserver.protocol.ExecutionSuiteProtocolFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-22 18:18
 * @description :
 * @modified By :
 * @since : 2022-3-22
 */
@Component
//no lazy
public class ExecutionSuiteJsonProtocolArgumentResolver extends JsonProtocolArgumentResolver {

    @Autowired
    private ExecutionSuiteProtocolFactory executionSuiteProtocolFactory;

    public ExecutionSuiteJsonProtocolArgumentResolver() {
        addResolver(this);
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(ExecutionSuiteJsonProtocol.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, @Nullable ModelAndViewContainer mvcContainer, NativeWebRequest nativeWebRequest, @Nullable WebDataBinderFactory binderFactory) throws Exception {
        ExecutionSuiteJsonObject executionSuiteJsonObject = JSONObject.parseObject(getRequestString(nativeWebRequest), ExecutionSuiteJsonObject.class);
        return executionSuiteProtocolFactory.product(executionSuiteJsonObject);
    }
}
