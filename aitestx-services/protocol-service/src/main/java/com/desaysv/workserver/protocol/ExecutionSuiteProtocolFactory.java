package com.desaysv.workserver.protocol;


import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.operation.base.*;
import com.desaysv.workserver.base.execution.ExecutionSuiteJsonObject;
import com.desaysv.workserver.base.suite.ExecutionSuite;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 13:57
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@Component
@Slf4j
@Lazy
public class ExecutionSuiteProtocolFactory implements ProtocolFactory {

    @Autowired
    private ExecutionProtocolFactory executionProtocolFactory;

    @Override
    public JsonAction product(ActionJsonObject actionJsonObject) {
        ExecutionSuiteJsonObject executionSuiteJsonObject = (ExecutionSuiteJsonObject) actionJsonObject;

        ExecutionSuite executionSuite = new ExecutionSuite();
        executionSuite.setTestCycle(executionSuiteJsonObject.getTestCycle());
        executionSuite.setDebugModeEnabled(executionSuiteJsonObject.isDebugModeEnabled());
        executionSuite.setClientInfo(executionSuiteJsonObject.getClientInfo());

        List<ExecutionJsonObject> executionJsonObjects = executionSuiteJsonObject.getExecutionList();
        List<Execution> executionList = new ArrayList<>();
        for (ExecutionJsonObject executionJsonObject : executionJsonObjects) {
            Execution execution = executionProtocolFactory.product(executionJsonObject);
            executionList.add(execution);
        }
        executionSuite.setExecutionList(executionList);
        return executionSuite;
    }


}
