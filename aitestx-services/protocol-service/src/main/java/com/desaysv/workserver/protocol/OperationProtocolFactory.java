package com.desaysv.workserver.protocol;

import com.desaysv.workserver.base.execution.ExecutionUtils;
import com.desaysv.workserver.base.operation.base.*;
import com.desaysv.workserver.factory.WidgetOperationFactory;
import com.desaysv.workserver.operation.factory.DeviceOperationFactory;
import com.desaysv.workserver.operation.factory.ImageOperationFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 14:02
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Component
@Slf4j
@Lazy
public class OperationProtocolFactory implements ProtocolFactory {

    @Autowired
    private DeviceOperationFactory deviceOperationFactory;

    @Autowired
    private ImageOperationFactory imageOperationFactory;

    @Autowired
    private WidgetOperationFactory widgetOperationFactory;

    @Autowired
    private CommonOperationFactory commonOperationFactory;


    @Override
    public Operation product(ActionJsonObject actionJsonObject) {
        OperationJsonObject operationJsonObject = (OperationJsonObject) actionJsonObject;
        OperationAbstractFactory operationAbstractFactory;
        int operationType = ExecutionUtils.checkOperationType(operationJsonObject);
        operationJsonObject.setOperationType(operationType);
        if (operationType == OperationType.DEVICE) {
            operationAbstractFactory = deviceOperationFactory;
        } else if (operationType == OperationType.IMAGE) {
            operationAbstractFactory = imageOperationFactory;
        } else if (operationType == OperationType.WIDGET) {
            operationAbstractFactory = widgetOperationFactory;
        } else {
            operationAbstractFactory = commonOperationFactory;
        }
        return operationAbstractFactory.createOperation(operationJsonObject);
    }

    public List<Operation> product(List<OperationJsonObject> operationJsonObjects) {
        List<Operation> operationList = new ArrayList<>();
        for (OperationJsonObject actionJsonObject : operationJsonObjects) {
            operationList.add(product(actionJsonObject));
        }
        return operationList;
    }
}
