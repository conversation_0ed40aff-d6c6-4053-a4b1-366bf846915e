package com.desaysv.workserver;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.model.TestCase;
import com.desaysv.workserver.model.TestResult;
import com.desaysv.workserver.model.TestSuite;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

//no lazy
@Slf4j
@Component
public class TestCaseListener implements TestProcessListener {

    @Autowired
    private TestSuiteService testSuiteService;

    @Autowired
    private TestResultService testResultService;

    @Autowired
    private TestCaseService testCaseService;

    private TestSuite testSuite;

    private final Set<Integer> alreadyTestCycleSet;

    public TestCaseListener() {
        TestProcessManager.addTestProcessListener(this);
        alreadyTestCycleSet = new HashSet<>();
    }

    private TestSuite createTestSuite(String projectName) {
        TestSuite testSuite = new TestSuite();
        String testSuiteName = String.format("%s_%s", projectName, StrUtils.getTimeStamp());
        String uuid = StrUtils.getSaltMD5(testSuiteName, StrUtils.generateUUID());
        testSuite.setUuid(uuid);
        testSuite.setTestProjectName(projectName);
        testSuite.setTestSuiteName(testSuiteName);
        testSuite.setBeginTestTime(new Date());
        testSuite.setEndTestTime(testSuite.getBeginTestTime());
        testSuite = testSuiteService.insertTestSuiteIfNotExist(testSuite);
        return testSuite;
    }

    private TestSuite finishTestSuite(String uuid) {
        TestSuite testSuite = testSuiteService.findTestSuiteByUUID(uuid);
        if (testSuite != null) {
            testSuite.setEndTestTime(new Date());
            testSuiteService.updateTestSuite(testSuite);
        }
        return testSuite;
    }

    private TestCase updateTestCase(TestSuite testSuite, ExecutionContext executionContext, int currentTestCycle) {
        return updateTestCase(testSuite, executionContext, currentTestCycle, false);
    }

    private TestCase updateTestCase(TestSuite testSuite, ExecutionContext executionContext, int currentTestCycle, boolean isFailed) {
        if (testSuite == null) {
            return null;
        }
        String testSuiteUUID = testSuite.getUuid();
        TestSuite dbTestSuite = testSuiteService.findTestSuiteByUUID(testSuiteUUID);
        if (dbTestSuite == null) {
            log.warn(String.format("无效测试集合id:%s", testSuiteUUID));
            return null;
        }
        String moduleName = executionContext.getOperationContext().getCaseInfo().getModuleName();
        String testcaseName = executionContext.getOperationContext().getCaseInfo().getCaseName();
        TestCase testCase = testCaseService.findTestCase(testSuiteUUID, moduleName, testcaseName);
        if (testCase == null) {
            //新建测试用例
            testCase = new TestCase();
            String testCaseUUID = StrUtils.getMd5String(
                    executionContext.getProjectName() + "_" + moduleName + "_" + testcaseName);
            testCase.setTestcaseUUID(testCaseUUID);
            testCase.setBeginTestTime(new Date());
            testCase.setEndTestTime(testCase.getBeginTestTime());
            testCase.setTestSuiteUUID(testSuiteUUID);
            testCase.setPass(true);
            testCase.setTesting(true);
            testCase.setModuleName(moduleName);
            testCase.setTestCaseName(testcaseName);

            TestResult testResult = new TestResult();
            testResult.setSumCycle(executionContext.getTestCycle());
            testResult.setTestCycle(0);
            testResult.setFailCycle(0);
            testResult.setUuid(StrUtils.getSaltMD5(testResult.toString(), StrUtils.generateUUID()));

            testResultService.insertTestResult(testResult);
            log.debug("保存测试结果:{}", testResult);

            testCase.setTestResultUUID(testResult.getUuid());
            testCaseService.insertTestCase(testCase);
            log.debug("保存测试用例:{}", testCase);

            testCase = testCaseService.findTestCase(testSuiteUUID, moduleName, testcaseName);
        }

        TestResult testResult = testResultService.findTestResultByUUID(testCase.getTestResultUUID());
        testResult.setTestCycle(currentTestCycle);
        if (isFailed) {
            testResult.setFailCycle(testResult.getFailCycle() + 1);
            testCase.setPass(false);
        }
        testResultService.updateTestResult(testResult);

        testCase.setEndTestTime(new Date());
        testCaseService.updateTestCase(testCase);

        //更新测试套件测试结束时间
        testSuite.setEndTestTime(testCase.getEndTestTime());
        testSuiteService.updateTestSuite(testSuite);
        return testCase;
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        if (executionContext.isDebugModeEnabled() || executionContext.getProjectName() == null) {
            return;
        }
        //创建测试集合
        testSuite = createTestSuite(executionContext.getProjectName());
        log.debug("创建测试集合:{}", testSuite);
    }

    @Override
    public void testcaseStart(ExecutionContext executionContext) {
        if (executionContext.isDebugModeEnabled() || testSuite == null) {
            return;
        }
        //清空已测试循环计数
        alreadyTestCycleSet.clear();
    }

    @Override
    public void testing(ExecutionContext executionContext, int currentTestCycle) {
        if (executionContext.isDebugModeEnabled() || testSuite == null) {
            return;
        }
        updateTestCase(testSuite, executionContext, currentTestCycle);
    }

    @Override
    public void testFailed(ExecutionContext executionContext, int currentTestCycle) {
        if (executionContext.isDebugModeEnabled() || testSuite == null) {
            return;
        }
        if (!alreadyTestCycleSet.contains(currentTestCycle)) {
            updateTestCase(testSuite, executionContext, currentTestCycle, true);
            alreadyTestCycleSet.add(currentTestCycle);
        }
    }

    @Override
    public void testCaseComplete(ExecutionContext executionContext, boolean isFailed) {
        if (executionContext.isDebugModeEnabled() || testSuite == null) {
            return;
        }
        String testSuiteUUID = testSuite.getUuid();
        testSuite = finishTestSuite(testSuiteUUID);
        if (testSuite == null) {
            log.warn(String.format("无效测试集合UUID:%s", testSuiteUUID));
        } else {
            String moduleName = executionContext.getOperationContext().getCaseInfo().getModuleName();
            String testcaseName = executionContext.getOperationContext().getCaseInfo().getCaseName();
            TestCase testCase = testCaseService.findTestCase(testSuiteUUID, moduleName, testcaseName);
            testCase.setTesting(false);
            testCase.setEndTestTime(new Date());
            testCase.setPass(!isFailed);
            testCaseService.updateTestCase(testCase);
        }
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        if (executionContext.isDebugModeEnabled() || testSuite == null) {
            return;
        }
        String testSuiteUUID = testSuite.getUuid();
        testSuite = finishTestSuite(testSuiteUUID);
        if (testSuite == null) {
            log.warn(String.format("无效测试集合UUID:%s", testSuiteUUID));
        } else {
            //更新测试套件测试结束时间
            testSuite.setEndTestTime(new Date());
            testSuiteService.updateTestSuite(testSuite);
            log.debug("结束测试集合:{}", testSuite);
        }
    }

}
