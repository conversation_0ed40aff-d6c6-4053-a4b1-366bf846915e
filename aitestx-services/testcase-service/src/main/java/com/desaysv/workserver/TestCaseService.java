package com.desaysv.workserver;

import com.desaysv.workserver.mapper.TestCaseMapper;
import com.desaysv.workserver.mapper.TestResultMapper;
import com.desaysv.workserver.model.TestCase;
import com.desaysv.workserver.model.TestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@Lazy
public class TestCaseService {

    @Autowired
    private TestCaseMapper testCaseMapper;

    @Autowired
    private TestResultMapper testResultMapper;

    public void insertTestCase(TestCase testCase) {
        testCaseMapper.insert(testCase);
    }

    public TestCase findTestCase(String testSuiteUUID, String moduleName, String testCaseName) {
        return testCaseMapper.selectByUniqueRecord(testSuiteUUID, moduleName, testCaseName);
    }

    public TestResult findTestCaseResult(String testCaseUUID) {
        TestCase newlyTestCase = testCaseMapper.selectByTestCaseUUID(testCaseUUID);
        if (newlyTestCase != null) {
            TestResult testResult = testResultMapper.selectByTestResultUUID(newlyTestCase.getTestResultUUID());
            if (testResult != null) {
                return testResult;
//                TestCaseReport testCaseReport = new TestCaseReport();
//                testCaseReport.setTestCycle(testResult.getTestCycle());
//                testCaseReport.setFailCycle(testResult.getFailCycle());
//                testCaseReport.setSumCycle(testResult.getSumCycle());
//                return testCaseReport;
            }
        }
        return new TestResult();
    }

    public Integer updateTestCase(TestCase testCase) {
        return testCaseMapper.updateByPrimaryKey(testCase);
    }

    //TODO: 后续改为通过testcaseUUID获取次数
    public int getTotalTestCycles(String testProjectName, String moduleName, String testCaseName) {
        return testCaseMapper.selectTotalTestCycles(testProjectName, moduleName, testCaseName);
    }
}
