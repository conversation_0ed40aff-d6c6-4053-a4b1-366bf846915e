package com.desaysv.workserver.service;

import com.desaysv.workserver.excel.ExcelSheetTable;
import com.desaysv.workserver.model.ExcelCaseModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ExcelCaseService {
    void insertExcelCaseTable(Map<String, ExcelSheetTable> testCaseMap);

    Integer insertExcelCaseTable(List<ExcelCaseModel> excelCaseModelList);

    Integer updateExcelCase(ExcelCaseModel excelCaseModel);

    Integer clearMapColumnData(String tableName);

    Integer batchUpdateExcelCase(List<ExcelCaseModel> excelCaseModel);

    List<ExcelCaseModel> findExcelCaseByTableName(@Param("tableName") String tableName);

    ExcelCaseModel findRowCaseInfoByUuid(@Param("uuid") String uuid);

    Integer deleteByTableName(@Param("tableName") String tableName);

}
