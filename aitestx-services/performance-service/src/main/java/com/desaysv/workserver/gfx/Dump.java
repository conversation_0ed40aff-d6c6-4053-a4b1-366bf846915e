package com.desaysv.workserver.gfx;

import lombok.Data;

import java.util.LinkedList;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-23 14:16
 * @description :
 * @modified By :
 * @since : 2022-5-23
 */
@Data
public class Dump {

    private LinkedList<DumpItem> dumpItems = new LinkedList<>();
    private int framesCount = 0;
    private double avgDraw = 0.0;
    private double avgProcess = 0.0;
    private double avgExecute = 0.0;
    private double avgPerFrame = 0.0;
    private double avgMaxFrames = 0.0;

    public Dump(LinkedList<DumpItem> dump) {
        if (!dump.isEmpty()) {
            double[] avg = new double[] {0.0, 0.0, 0.0, 0.0};

            for (DumpItem item: dump) {
                avg[0] += item.draw;
                avg[1] += item.process;
                avg[2] += item.execute;
                avg[3] += item.draw + item.process + item.execute;
            }

            dumpItems = dump;
            framesCount = dump.size();
            avgDraw = avg[0] / dump.size();
            avgProcess = avg[1] / dump.size();
            avgExecute = avg[2] / dump.size();
            avgPerFrame = avg[3] / dump.size();
            avgMaxFrames = 60 * 16 / avgPerFrame;
        }
    }


}
