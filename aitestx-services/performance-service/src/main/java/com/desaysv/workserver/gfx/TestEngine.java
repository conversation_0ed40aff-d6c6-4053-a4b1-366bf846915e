package com.desaysv.workserver.gfx;

import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-23 14:21
 * @description :
 * @modified By :
 * @since : 2022-5-23
 */
public class TestEngine {

    public static void main(String[] args) {
        String packageName = "com.autonavi.amapauto";
        new TestEngine().run(packageName);
//        if (args.length != 1) {
//            System.err.println("You must specify one arg with package name of application");
//        } else {
//            new TestEngine().run(args[0]);
//        }
    }

    public void run(String packageName) {
        GfxInfoReader reader = new GfxInfoReader("", packageName);
        Timer t = new Timer();
        t.scheduleAtFixedRate(new TimerTaskImpl(reader), 0L, 5000L);
    }

    static class TimerTaskImpl extends TimerTask {

        private final GfxInfoReader mReader;

        TimerTaskImpl(GfxInfoReader reader) {
            mReader = reader;
        }

        @Override
        public void run() {
            try {
                Dump dump = mReader.read();
                System.out.println(dump);
                String msg = String.format(
                        "Time: %d\nFrames: %d\n" +
                                "Draw: %.2f ms\nProcess: %.2f ms\nExecute: %.2f ms\n" +
                                "Per frame: %.2f ms\nMax frames: %d\n\n",
                        System.currentTimeMillis(),
                        dump.getFramesCount(),
                        dump.getAvgDraw(), dump.getAvgProcess(), dump.getAvgExecute(),
                        dump.getAvgPerFrame(), (int) dump.getAvgMaxFrames());
                System.out.println(msg);
            } catch (Exception ex) {
                System.err.println(ex);
            }
        }
    }
}
