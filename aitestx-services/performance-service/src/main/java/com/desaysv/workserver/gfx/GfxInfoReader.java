package com.desaysv.workserver.gfx;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.LinkedList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-23 14:10
 * @description :
 * @modified By :
 * @since : 2022-5-23
 */
public class GfxInfoReader {

    private final String deviceId;
    private final String appPackage;

    public GfxInfoReader(String deviceId, String appPackage) {
        this.deviceId = deviceId;
        this.appPackage = appPackage;
    }

    public Dump read() throws IOException, ParseException {
        String cmd = String.format("adb shell dumpsys gfxinfo %s", appPackage);

        InputStream is = Runtime.getRuntime().exec(cmd).getInputStream();
        String body = readFromStream(is);
        is.close();

        LinkedList<DumpItem> items = new LinkedList<>();
        Pattern p = Pattern.compile("(\\s+[0-9,.]{4})(\\s+[0-9,.]{4})(\\s+[0-9,.]{4})", Pattern.MULTILINE);
        System.out.println("body:" + body);
        Matcher m = p.matcher(body);
        DecimalFormat df = new DecimalFormat();
        while (m.find()) {
            items.add(new DumpItem(
                    df.parse(m.group(1).trim()).doubleValue(),
                    df.parse(m.group(2).trim()).doubleValue(),
                    df.parse(m.group(3).trim()).doubleValue()));
        }

        return new Dump(items);

    }

    protected String readFromStream(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] bytes = new byte[4096];
        for (int len; (len = is.read(bytes)) > 0; ) {
            baos.write(bytes, 0, len);
        }
        return new String(baos.toByteArray(), StandardCharsets.UTF_8);
    }
}
