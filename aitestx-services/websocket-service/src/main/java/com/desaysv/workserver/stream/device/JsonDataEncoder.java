package com.desaysv.workserver.stream.device;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

/**
 * websocket JSON数据解码
 */
@Slf4j
public class JsonDataEncoder implements Encoder.Text<Object> {

    @Override
    public String encode(Object o) {
        try {
            return JSONObject.toJSONString(o);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void init(EndpointConfig endpointConfig) {

    }

    @Override
    public void destroy() {

    }
}
