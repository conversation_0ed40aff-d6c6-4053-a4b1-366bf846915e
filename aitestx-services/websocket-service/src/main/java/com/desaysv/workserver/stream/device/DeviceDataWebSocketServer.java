package com.desaysv.workserver.stream.device;

import com.desaysv.workserver.WebSocketServer;
import com.desaysv.workserver.stream.config.WebSocketConfigurator;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Session;
import javax.websocket.server.PathParam;
import java.util.Map;

/**
 * 设备数据WebSocket分发
 */
@Slf4j
public class DeviceDataWebSocketServer extends WebSocketServer {

    private String monitorType;

    public void onOpen(Session session,
                       @PathParam("monitorType") String monitorType,
                       @PathParam("deviceAliasName") String deviceAliasName) {
        super.onOpen(session);
        session.setMaxIdleTimeout(0);
        Map<String, Object> userProperties = session.getUserProperties();
        String ipAddr = (String) userProperties.get(WebSocketConfigurator.IP_ADDR);
        Integer ipPort = (Integer) userProperties.get(WebSocketConfigurator.IP_PORT);
        log.info("From websocket:{}:{}->{}/{}", ipAddr, ipPort, monitorType, deviceAliasName);
        this.monitorType = monitorType;
        registerWebSocketServer(monitorType, this);
    }

    @Override
    public void onClose(Session session) {
        unregisterWebSocketServer(monitorType, this);
        super.onClose(session);
    }


}