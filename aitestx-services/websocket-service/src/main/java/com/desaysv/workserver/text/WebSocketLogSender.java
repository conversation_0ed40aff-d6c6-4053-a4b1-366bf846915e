package com.desaysv.workserver.text;

import com.desaysv.workserver.entity.LogMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.websocket.Session;

@Slf4j
public class WebSocketLogSender implements LogSender {
    @Override
    public void sendLogMessage(LogMessage message) {
        // 同步发送的实现
        sendLogMessage(message, false);
    }

    public static void sendLogMessage(LogMessage message, boolean async) {
        try {
            if (LogMonitorWebSocketServer.isClientConnected() && StringUtils.isNotBlank(message.getMessage())) {
                final Session session = LogMonitorWebSocketServer.getSharedSession();
                if (session != null) {
                    synchronized (session) {
                        if (session.isOpen() && !Thread.currentThread().isInterrupted()) {
                            if (async) {
                                session.getAsyncRemote().sendObject(message);
                            } else {
                                session.getBasicRemote().sendObject(message);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("日志发送错误:{}", e.getMessage());
        }
    }
}