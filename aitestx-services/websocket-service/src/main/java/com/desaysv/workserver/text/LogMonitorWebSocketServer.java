package com.desaysv.workserver.text;

import com.desaysv.workserver.WebSocketServer;
import com.desaysv.workserver.entity.LogMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/8/4 16:10
 * @description :
 * @modified By :
 * @since : 2023/8/4
 **/
@ServerEndpoint(value = "/monitor/printLog", encoders = {LogMessage.class})
@Slf4j
@Component
@Lazy
public class LogMonitorWebSocketServer extends WebSocketServer {
    private final static Map<String, Session> clientSessionMap = new ConcurrentHashMap<>();
    private final static String PING_STRING = "0x123";
    private final static String PONG_STRING = "0x456";
    private static boolean connectLogWebSocketClient = false;
    private static String sharedSessionId;

    @OnOpen
    public void onOpen(Session session) {
        sharedSessionId = session.getId();
        clientSessionMap.put(sharedSessionId, session);
        connectLogWebSocketClient = true;
        super.onOpen(session);
    }

    @OnClose
    public void onClose(Session session) {
        clientSessionMap.remove(sharedSessionId);
        connectLogWebSocketClient = false;
        super.onClose(session);
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        //log.info("LogMonitorWebSocketServer from {}'s message:{}", session, message);
        if (PING_STRING.equals(message)) {
            if (session != null) {
                try {
                    if (session.isOpen() && !Thread.currentThread().isInterrupted()) {
                        session.getAsyncRemote().sendObject(new LogMessage("info", PONG_STRING));
                    }
                } catch (Exception ex) {
                    log.warn("Error processing message from {}: {}", session.getId(), ex.getMessage(), ex);
                }
            }
        }
    }

    @OnError
    public void onError(Session session, Throwable error) {
        super.onError(session, error);
    }

    public static Session getSharedSession() {
        return clientSessionMap.get(sharedSessionId);
    }

    public static boolean isClientConnected() {
        return connectLogWebSocketClient;
    }

}
