package com.desaysv.workserver.service;

import com.desaysv.workserver.mapper.TemplatePictureMapper;
import com.desaysv.workserver.mapper.roi.PercentTemplateRoiMapper;
import com.desaysv.workserver.model.TemplatePicture;
import com.desaysv.workserver.model.roi.PercentTemplateRoi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 18:39
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
@Service
@Lazy
public class TemplateRoiServiceImpl implements TemplateRoiService {

    @Autowired
    private PercentTemplateRoiMapper percentTemplateRoiMapper;

    @Autowired
    private TemplatePictureMapper templatePictureMapper;

    @Override
    public PercentTemplateRoi insertOrUpdate(PercentTemplateRoi percentTemplateRoi) {
        PercentTemplateRoi roi = percentTemplateRoiMapper.selectByTemplatePictureUUID(percentTemplateRoi.getTemplatePictureUUID());
        Integer affectedRows;
        if (roi != null) {
            //更新Roi
            percentTemplateRoiMapper.updateByTemplatePictureUUID(percentTemplateRoi);
        } else {
            //增加Roi到数据库
            percentTemplateRoiMapper.insert(percentTemplateRoi);
        }
        roi = percentTemplateRoiMapper.selectByTemplatePictureUUID(percentTemplateRoi.getTemplatePictureUUID());
        return roi;
    }

    @Override
    public PercentTemplateRoi getPercentRoiByTemplateUUID(String templateUUID) {
        return percentTemplateRoiMapper.selectByTemplatePictureUUID(templateUUID);
    }

    @Override
    public PercentTemplateRoi getPercentRoiByTemplateName(String projectName, String deviceUniqueCode, String templateName) {
        TemplatePicture templatePicture = templatePictureMapper.selectByName(templateName, deviceUniqueCode, projectName);
        if (templatePicture != null) {
            return percentTemplateRoiMapper.selectByTemplatePictureUUID(templatePicture.getTemplatePictureUUID());
        }
        return null;
    }

    @Override
    public PercentTemplateRoi getAllPercentRoiByTemplateName(String projectName, String deviceUniqueCode) {

        return null;
    }
}
