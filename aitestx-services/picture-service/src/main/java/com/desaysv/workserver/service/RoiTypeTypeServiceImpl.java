package com.desaysv.workserver.service;

import com.desaysv.workserver.mapper.roi.RoiTypeMapper;
import com.desaysv.workserver.model.roi.RoiType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 18:43
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Service
@Slf4j
//no lazy
public class RoiTypeTypeServiceImpl implements RoiTypeService {
    private static final String RECT_TYPE_NAME = "rect";
    private static final String CIRCLE_TYPE_NAME = "circle";

    private static final List<String> typeNameList = new ArrayList<>(Arrays.asList(RECT_TYPE_NAME, CIRCLE_TYPE_NAME));
    private RoiTypeMapper roiTypeMapper;

    @Autowired
    public void setRoiTypeMapper(RoiTypeMapper roiTypeMapper) {
        this.roiTypeMapper = roiTypeMapper;
    }

    @Override
    @Async
    public void initDB() {
//        log.info("roiTypeMapper初始化db");
        for (String typeName : typeNameList) {
            RoiType roiType = roiTypeMapper.selectByName(typeName);
            if (roiType == null) {
                roiType = new RoiType();
                roiType.setName(typeName);
                roiTypeMapper.insert(roiType);
            }
        }
    }

    @Override
    public RoiType getRectRoiType() {
        return roiTypeMapper.selectByName(RECT_TYPE_NAME);
    }

    @Override
    public RoiType getCircleRoiType() {
        return roiTypeMapper.selectByName(CIRCLE_TYPE_NAME);
    }

}
