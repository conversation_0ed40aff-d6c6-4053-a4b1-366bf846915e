package com.desaysv.workserver.service;

import com.desaysv.workserver.mapper.OriginalPictureMapper;
import com.desaysv.workserver.model.OriginalPicture;
import com.desaysv.workserver.utils.StrUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 18:44
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
@Service
@Lazy
public class OriginalPictureServiceImpl implements OriginalPictureService {

    @Autowired
    private OriginalPictureMapper originalPictureMapper;

    @Override
    public OriginalPicture insertOrUpdate(OriginalPicture originalPicture) {
        OriginalPicture picture = originalPictureMapper.selectByDimension(originalPicture);
        if (picture != null) {
            //更新原图
            originalPictureMapper.updateByOriginalPictureUUID(picture);
        } else {
            //保存原图到数据库
            String originalPictureUUID = StrUtils.getSaltMD5(originalPicture.toString(), StrUtils.generateUUID());
            originalPicture.setUuid(originalPictureUUID);
            originalPictureMapper.insert(originalPicture);
            picture = originalPictureMapper.selectByOriginalPictureUUID(originalPicture.getUuid());
        }
        return picture;
    }
}
