package com.desaysv.workserver.context;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.chain.VisionAlgorithmHandleChain;
import com.desaysv.workserver.entity.VisionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 13:20
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
@Service
@Slf4j
@Lazy
public class AlgorithmInvoker {

    @Autowired
    private VisionAlgorithmHandleChain visionAlgorithmHandleChain;


    //调用策略类中的算法
    public VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm, VisionEventHandler visionEventHandler) throws OperationFailNotification {
        visionAlgorithmHandleChain.reset();
        return visionAlgorithmHandleChain.handleAlgorithm(visionAlgorithm, visionEventHandler);
    }

}
