package com.desaysv.workserver.handler;

import com.desaysv.workserver.algorithm.composites.IntelligentTemplateMatching;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.algorithm.color.ColorMatching;
import com.desaysv.workserver.algorithm.feature.FeatureMatching;
import com.desaysv.workserver.algorithm.strict.DynamicStrictTemplateMatching;
import com.desaysv.workserver.algorithm.strict.PerPixelTemplateMatching;
import com.desaysv.workserver.algorithm.template_match.CvTemplateMatching;
import com.desaysv.workserver.algorithm.strict.StrictTemplateMatching;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.chain.IVisionAlgorithmHandleChain;
import com.desaysv.workserver.chain.IVisionAlgorithmHandler;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 模板匹配算法处理
 */
@Service
@Lazy
public class TemplateMatchAlgorithmHandler implements IVisionAlgorithmHandler {

    @Autowired
    private CvTemplateMatching cvTemplateMatching;

    @Autowired
    private StrictTemplateMatching strictTemplateMatching;

    @Autowired
    private PerPixelTemplateMatching perPixelTemplateMatching;

    @Autowired
    private ColorMatching colorMatching;

    @Autowired
    private FeatureMatching featureMatching;

    @Autowired
    private DynamicStrictTemplateMatching dynamicStrictTemplateMatching;

    @Autowired
    private IntelligentTemplateMatching intelligentTemplateMatching;

    @Override
    public VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm,
                                        VisionEventHandler visionEventHandler,
                                        IVisionAlgorithmHandleChain handleChain) throws OperationFailNotification {
        VisionResult visionResult;
        //TODO：使用设计模式修改if
        if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.strictTemplateMatching)) {
            //精准匹配
            visionResult = strictTemplateMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.cvTemplateMatching)) {
            //模糊匹配
            visionResult = cvTemplateMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.perPixelTemplateMatching)) {
            //像素匹配
            visionResult = perPixelTemplateMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.colorMatching)) {
            //颜色匹配
            visionResult = colorMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equals(AlgorithmSets.featureMatching)) {
            //特征匹配
            visionResult = featureMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equals(AlgorithmSets.dynamicStrictTemplateMatching)) {
            //动态匹配
            visionResult = dynamicStrictTemplateMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else if (visionAlgorithm.getAlgorithmName().equals(AlgorithmSets.intelligentTemplateMatching)) {
            //智能匹配
            visionResult = intelligentTemplateMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else {
            visionResult = handleChain.handleAlgorithm(visionAlgorithm, visionEventHandler);
        }
        return visionResult;
    }

}
