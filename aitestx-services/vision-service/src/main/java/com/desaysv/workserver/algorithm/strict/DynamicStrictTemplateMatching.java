package com.desaysv.workserver.algorithm.strict;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.StructuralSimilarityUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@Lazy
public class DynamicStrictTemplateMatching extends StrictTemplateMatching {

    @Override
    public String getFriendlyName() {
        return "动态匹配";
    }

    @Override
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        VisionEventHandler visionEventHandler = visionAlgorithm.getVisionEventHandler();
        VisionResult visionResult = new VisionResult();
        long startMills = System.currentTimeMillis();
        while ((System.currentTimeMillis() - startMills) * 1000 >= visionAlgorithm.getRecognizedDuration()) {
            if (Thread.currentThread().isInterrupted()) {
                break;
            }
            try {
                Frame firstFrame = ImageUtils.crop(visionEventHandler.captureLiveFrame(), visionAlgorithm.getRoiRect());
                log.info("visionAlgorithm->firstFrame:{}", firstFrame);
                TimeUnit.MILLISECONDS.sleep((long) (visionAlgorithm.getRecognizedInterval() * 1000));
                Frame secondFrame = ImageUtils.crop(visionEventHandler.captureLiveFrame(), visionAlgorithm.getRoiRect());
                log.info("visionAlgorithm->secondFrame:{}", secondFrame);
                double similarity = StructuralSimilarityUtils.getSSIM(firstFrame, secondFrame, visionAlgorithm.isColorMatchEnabled());
                visionResult.setScore(similarity);
                visionResult.setBestMatch(Rectangle.fromRect(visionAlgorithm.getRoiRect()));
                if (visionAlgorithm.isOnlyTestSimilarity()) {
                    break;
                }
            } catch (FrameGrabberException | InterruptedException e) {
                visionResult.setPassed(false);
                visionResult.setMessage(ExceptionUtils.getExceptionString(e));
                return visionResult;
            }
        }
        return visionResult;
    }

    public static void main(String[] args) {
        // 表达式字符串
        String expression = "false || true && false";

        // 创建一个ScriptEngineManager
        ScriptEngineManager manager = new ScriptEngineManager();

        // 获取JavaScript引擎实例
        ScriptEngine engine = manager.getEngineByName("JavaScript");

        try {
            // 评估表达式
            Boolean result = (Boolean) engine.eval(expression);

            // 输出结果
            System.out.println("Result of '" + expression + "' is: " + result);
        } catch (ScriptException e) {
            log.error(e.getMessage(), e);
        }
    }
}
