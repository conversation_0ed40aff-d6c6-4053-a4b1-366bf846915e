package com.desaysv.workserver.utils;

import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.bytedeco.opencv.opencv_core.Size;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

import static com.desaysv.workserver.utils.HistogramUtils.calculateLocalHSVHistogramSimilarity;
import static org.bytedeco.opencv.global.opencv_core.CV_32F;
import static org.bytedeco.opencv.global.opencv_imgproc.*;
import static org.bytedeco.opencv.global.opencv_imgproc.Canny;

@Slf4j
public class OptionalImageUtils {
    private static final int BLUR_SIZE = 5;
    private static final int CANNY_THRESHOLD1 = 50;
    private static final int CANNY_THRESHOLD2 = 150;
    private static final Size GUASSIAN_SIZE = new Size(11, 11);

    private File saveFrameToFile(Frame frame, String fileName) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile(fileName, ".png");
            // 将 Frame 转换为 BufferedImage
            try (Java2DFrameConverter converter = new Java2DFrameConverter()) {
                BufferedImage bufferedImage = converter.getBufferedImage(frame);
                // 将 BufferedImage 保存为文件
                ImageIO.write(bufferedImage, "png", tempFile);
            }
            return tempFile;
        } catch (IOException e) {
            log.error("Error saving frame to file", e);
            return null;
        }
    }

    public static double compareImages(String effectImage, String realImage, double similarity) {
        // 调用比较服务
        String url = "http://*************:8001/compare-images";
        try {
            // 使用effectImage、realImage和similarity发起POST请求
            URL postUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) postUrl.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("accept", "application/json");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=----Boundary123456789");

            try (OutputStream outputStream = conn.getOutputStream()) {
                String boundary = "----Boundary123456789";

                // 写入ui图片部分
                String uiPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"ui\"; filename=\"" + new File(effectImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(uiPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(effectImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入real图片部分
                String realPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"real\"; filename=\"" + new File(realImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(realPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(realImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入param部分
                String paramPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"param\"\r\n\r\n" +
                        similarity + "\r\n" +
                        "--" + boundary + "--\r\n";
                outputStream.write(paramPart.getBytes(StandardCharsets.UTF_8));
            }

            // 读取响应
            int responseCode = conn.getResponseCode();
            log.info("responseCode：{}", responseCode);
            if (responseCode != HttpURLConnection.HTTP_OK) {
                return -1;
            }

            try (java.util.Scanner scanner = new java.util.Scanner(conn.getInputStream())) {
                String response = scanner.useDelimiter("\\A").next();
                log.info("匹配结果{}", response);
                // 使用 Gson 解析 JSON 响应
                JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
                // 提取 min_similarity 值
                return jsonObject.get("min_similarity").getAsDouble();  // 返回min_similarity 值
            } catch (Exception e) {
                return -1;
            }
        } catch (Exception e) {
            return -1;
        }
    }

    public static double getSSIMByProcessing(Mat originalMat,
                                             Mat templateMat,
                                             boolean enableBinarization,
                                             boolean isColorMatchEnabled,
                                             boolean enableCanny) {
        if (!isColorMatchEnabled && originalMat.channels() != 1 && templateMat.channels() != 1) {
            log.info("图片转换为灰度进行匹配");
            cvtColor(originalMat, originalMat, COLOR_BGR2GRAY);
            cvtColor(templateMat, templateMat, COLOR_BGR2GRAY);
        } else {
            log.info("图片不转换为灰度，保持原色彩空间进行匹配");
            // 确保图像是三通道的BGR格式，然后再转换到YCrCb颜色空间
            if (originalMat.channels() == 1) {
                cvtColor(originalMat, originalMat, COLOR_GRAY2BGR);
            }
            if (templateMat.channels() == 1) {
                cvtColor(templateMat, templateMat, COLOR_GRAY2BGR);
            }
            cvtColor(originalMat, originalMat, COLOR_BGR2YCrCb);
            cvtColor(templateMat, templateMat, COLOR_BGR2YCrCb);
        }

        // Step 1: Apply Gaussian Blur
        Size size = new Size(BLUR_SIZE, BLUR_SIZE); // 使用定义的BLUR_SIZE常量
        GaussianBlur(originalMat, originalMat, size, 0);
        GaussianBlur(templateMat, templateMat, size, 0);

        if (enableBinarization) {
            // 使用自适应阈值进行二值化
            log.info("应用图片二值化算法");
            threshold(originalMat, originalMat, 0, 255, THRESH_BINARY | THRESH_OTSU);
            threshold(templateMat, templateMat, 0, 255, THRESH_BINARY | THRESH_OTSU);
        }
        if (enableCanny) {
            // 使用边缘信息计算SSIM
            try (Mat edgesOriginal = new Mat(); Mat edgesTemplate = new Mat()) {
                Canny(originalMat, edgesOriginal, CANNY_THRESHOLD1, CANNY_THRESHOLD2);
                Canny(templateMat, edgesTemplate, CANNY_THRESHOLD1, CANNY_THRESHOLD2);
                return StructuralSimilarityUtils.getSSIM(edgesOriginal, edgesTemplate);
            }
        } else {
            return StructuralSimilarityUtils.getSSIM(originalMat, templateMat);
        }
    }


    protected VisionResult applyTemplateMatching(VisionAlgorithm visionAlgorithm, Frame firstFrame, Frame secondFrame) {
        if (visionAlgorithm.getRoiRect() == null) {
            throw new IllegalArgumentException("ROI信息缺失");
        }

        VisionResult visionResult = new VisionResult();
        boolean enableBinarization = visionAlgorithm.getAlgorithmConfig().isEnableBinarization();

        Mat originalMat = Java2DFrameUtils.toMat(firstFrame);
        Mat templateMat = Java2DFrameUtils.toMat(secondFrame);
        BufferedImage bufferedImage1;
        try (Java2DFrameConverter converter1 = new Java2DFrameConverter()) {
            bufferedImage1 = converter1.getBufferedImage(firstFrame);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("转换第1帧失败:%s", e.getMessage()));
        }
        BufferedImage bufferedImage2;
        try (Java2DFrameConverter converter2 = new Java2DFrameConverter()) {
            bufferedImage2 = converter2.getBufferedImage(secondFrame);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("转换第2帧失败:%s", e.getMessage()));
        }
        // 保存 Frame 为文件并获取路径
        File firstFrameFile = saveFrameToFile(firstFrame, "firstFrame.png");
        File secondFrameFile = saveFrameToFile(secondFrame, "secondFrame.png");
        if (firstFrameFile == null || secondFrameFile == null) {
            throw new IllegalArgumentException("保存帧为文件失败");
        }
        // 获取文件路径
        String firstFramePath = firstFrameFile.getAbsolutePath();
        String secondFramePath = secondFrameFile.getAbsolutePath();
        boolean isColorMatchEnabled = visionAlgorithm.isColorMatchEnabled();

        double similarity3 = compareImages(firstFramePath, secondFramePath, 0.65);
        log.info("compareImages相似度: {}", similarity3);
        double similarity2 = calculateLocalHSVHistogramSimilarity(bufferedImage1, bufferedImage2);
        log.info("直方图相似度: {}", similarity2);
        double similarity0 = getSSIMByProcessing(originalMat, templateMat, enableBinarization, isColorMatchEnabled, false);
        log.info("非边缘ssim相似度: {}", similarity0);
        double similarity1 = getSSIMByProcessing(originalMat, templateMat, enableBinarization, isColorMatchEnabled, true);
        log.info("边缘ssim相似度: {}", similarity1);

        double similarity;
        // 根据条件设置最终相似度
        if (similarity3 > 0 && similarity3 < 0.8) {
            similarity = similarity3;
        } else {
            if (similarity2 < 0.4) {
                similarity = similarity2;
            } else {
                if (similarity1 > 0.8 && similarity2 > 0.8) {
                    similarity = similarity1;
                } else if (similarity1 > 0.8 && similarity2 < 0.8 && similarity3 != 1 && similarity1 != 1) {
                    similarity = similarity3;
                } else {
                    similarity = Math.min(similarity1, similarity2);
                }
            }
        }
        log.info("综合相似度: {}", similarity);
        // 删除临时文件
        firstFrameFile.delete();
        secondFrameFile.delete();

        visionResult.setScore(similarity);
        visionResult.setBestMatch(Rectangle.fromRect(visionAlgorithm.getRoiRect()));
        return visionResult;
    }

    /**
     * 简单ssim（基于RGB空间）
     *
     * @param mat1
     * @param mat2
     * @return ssim分数
     */
    @Deprecated
    public static double getSimpleSSIM(Mat mat1, Mat mat2) {
        return getSimpleSSIM(Java2DFrameUtils.toBufferedImage(mat1), Java2DFrameUtils.toBufferedImage(mat2));
    }

    /**
     * 简单ssim（基于RGB空间）
     *
     * @param image1
     * @param image2
     * @return ssim分数
     */
    @Deprecated
    public static double getSimpleSSIM(BufferedImage image1, BufferedImage image2) {
        double ssim = 0;
        try {
            // 设置参数
            double C1 = 6.5025, C2 = 58.5225;
            int width = image1.getWidth();
            int height = image1.getHeight();
            int[] rgb1 = new int[width * height];
            int[] rgb2 = new int[width * height];
            // 获取像素信息
            image1.getRGB(0, 0, width, height, rgb1, 0, width);
            image2.getRGB(0, 0, width, height, rgb2, 0, width);

            // 计算平均亮度
            double avg1 = 0, avg2 = 0;
            for (int i = 0; i < width * height; i++) {
                int r = (rgb1[i] >> 16) & 0xff;
                int g = (rgb1[i] >> 8) & 0xff;
                int b = (rgb1[i]) & 0xff;
                avg1 += (0.299 * r + 0.587 * g + 0.114 * b);
                r = (rgb2[i] >> 16) & 0xff;
                g = (rgb2[i] >> 8) & 0xff;
                b = (rgb2[i]) & 0xff;
                avg2 += (0.299 * r + 0.587 * g + 0.114 * b);
            }
            avg1 /= (width * height);
            avg2 /= (width * height);

            // 计算标准差和协方差
            double var1 = 0, var2 = 0, cov = 0;
            for (int i = 0; i < width * height; i++) {
                int r = (rgb1[i] >> 16) & 0xff;
                int g = (rgb1[i] >> 8) & 0xff;
                int b = (rgb1[i]) & 0xff;
                double Y1 = 0.299 * r + 0.587 * g + 0.114 * b;
                r = (rgb2[i] >> 16) & 0xff;
                g = (rgb2[i] >> 8) & 0xff;
                b = (rgb2[i]) & 0xff;
                double Y2 = 0.299 * r + 0.587 * g + 0.114 * b;
                var1 += (Y1 - avg1) * (Y1 - avg1);
                var2 += (Y2 - avg2) * (Y2 - avg2);
                cov += (Y1 - avg1) * (Y2 - avg2);
            }

            // 计算SSIM值
            var1 /= (width * height - 1);
            var2 /= (width * height - 1);
            cov /= (width * height - 1);
            ssim = (2 * avg1 * avg2 + C1) * (2 * cov + C2) / ((avg1 * avg1 + avg2 * avg2 + C1) * (var1 + var2 + C2));
            ssim = ssim > 0 ? ssim : 0;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ssim;
    }

    /**
     * 计算两个Mat的PSNR分数
     *
     * @param mat1
     * @param mat2
     * @return ssim分数
     */
    private static double getPSNR(Mat mat1, Mat mat2) {
        Mat s1 = new Mat();
        opencv_core.absdiff(mat1, mat2, s1);             // |mat1 - mat2|
        s1.convertTo(s1, CV_32F);        // cannot make a square on 8 bits
        s1 = s1.mul(s1).asMat();                     // |mat1 - mat2|^2

        Scalar s = opencv_core.sumElems(s1);         // sum elements per channel

        double sse = s.get(0) + s.get(1) + s.get(2); // sum channels

        if (sse <= 1e-10) {                          // for small values return zero
            return 0;
        } else {
            double mse = sse / (double) (mat1.channels() * mat1.total());
            return 10.0 * Math.log10((255 * 255) / mse);
        }
    }

    /**
     * 计算ssim
     *
     * @param m1
     * @param m2
     * @return ssim分数
     */
    public static double getSSIM2(Mat m1, Mat m2) {
        // 验证输入
        if (m1.empty() || m2.empty() ||
                m1.size().width() != m2.size().width() ||
                m1.size().height() != m2.size().height()) {
            throw new IllegalArgumentException("输入图像无效或尺寸不匹配");
        }
        Mat mat1 = new Mat();
        Mat mat2 = new Mat();
        if (m1.channels() == 3) {
            opencv_imgproc.cvtColor(m1, mat1, opencv_imgproc.COLOR_BGR2GRAY);
        } else {
            mat1 = m1.clone();
        }
        if (m2.channels() == 3) {
            opencv_imgproc.cvtColor(m2, mat2, opencv_imgproc.COLOR_BGR2GRAY);
        } else {
            mat2 = m2.clone();
        }
        // SSIM参数
        double L = 255.0; // 像素最大值
        double K1 = 0.01;
        double K2 = 0.03;
        double C1 = Math.pow(K1 * L, 2);
        double C2 = Math.pow(K2 * L, 2);
        double sigma = 1.5;

        // ***************************** INITS **********************************/
        // 转换为浮点型
        int d = CV_32F;
        Mat I1 = new Mat();
        Mat I2 = new Mat();
        mat1.convertTo(I1, d);                  // cannot calculate on one byte large values
        mat2.convertTo(I2, d);

        Mat I2_2 = I2.mul(I2).asMat();        // I2^2
        Mat I1_2 = I1.mul(I1).asMat();        // I1^2
        Mat I1_I2 = I1.mul(I2).asMat();       // I1 * I2

        // *************************** END INITS **********************************/
        // PRELIMINARY COMPUTING
        Mat mu1 = new Mat();
        Mat mu2 = new Mat();
        opencv_imgproc.GaussianBlur(I1, mu1, GUASSIAN_SIZE, sigma);
        opencv_imgproc.GaussianBlur(I2, mu2, GUASSIAN_SIZE, sigma);
        Mat mu1_2 = mu1.mul(mu1).asMat();
        Mat mu2_2 = mu2.mul(mu2).asMat();
        Mat mu1_mu2 = mu1.mul(mu2).asMat();

        Mat sigma1_2 = new Mat();
        Mat sigma2_2 = new Mat();
        Mat sigma12 = new Mat();
        opencv_imgproc.GaussianBlur(I1_2, sigma1_2, GUASSIAN_SIZE, sigma);
        sigma1_2 = opencv_core.subtract(sigma1_2, mu1_2).asMat();
        opencv_imgproc.GaussianBlur(I2_2, sigma2_2, GUASSIAN_SIZE, sigma);
        sigma2_2 = opencv_core.subtract(sigma2_2, mu2_2).asMat();
        opencv_imgproc.GaussianBlur(I1_I2, sigma12, GUASSIAN_SIZE, sigma);
        sigma12 = opencv_core.subtract(sigma12, mu1_mu2).asMat();

        Mat t1, t2, t3;
        t1 = opencv_core.add(opencv_core.multiply(2, mu1_mu2), Scalar.all(C1)).asMat();
        t2 = opencv_core.add(opencv_core.multiply(2, sigma12), Scalar.all(C2)).asMat();
        t3 = t1.mul(t2).asMat();                     // t3 = ((2*mu1_mu2 + C1).*(2*sigma12 + C2))
        t1 = opencv_core.add(opencv_core.add(mu1_2, mu2_2), Scalar.all(C1)).asMat();
        t2 = opencv_core.add(opencv_core.add(sigma1_2, sigma2_2), Scalar.all(C2)).asMat();
        t1 = t1.mul(t2).asMat();                     // t1 =((mu1_2 + mu2_2 + C1).*(sigma1_2 + sigma2_2 + C2))

        Mat ssim_map = new Mat();
        opencv_core.divide(t3, t1, ssim_map);        // ssim_map =  t3./t1;
        Scalar mssim = opencv_core.mean(ssim_map);   // mssim = average of ssim map
        return mssim.get();
    }

    public static void main(String[] args) throws IOException {
        File file1 = new File("D:\\UIDS1050\\Desktop\\工作需要\\失败图像识别\\f2.png");
        File file2 = new File("D:\\UIDS1050\\Desktop\\工作需要\\失败图像识别\\f3.png");
        BufferedImage image1 = ImageIO.read(file1);
        BufferedImage image2 = ImageIO.read(file2);
        Mat originalMat = Java2DFrameUtils.toMat(image1);
        Mat templateMat = Java2DFrameUtils.toMat(image2);
        String effectImage = file1.getAbsolutePath();
        String realImage = file2.getAbsolutePath();

        long startTime = System.currentTimeMillis();
        double similarity3 = compareImages(effectImage, realImage, 0.65);
        System.out.println("compareImages相似度: " + similarity3);
        double similarity;
        // 根据条件设置最终相似度
        if (similarity3 > 0 && similarity3 < 0.8) {
            similarity = similarity3;
        } else {
            double similarity2 = calculateLocalHSVHistogramSimilarity(image1, image2);
            System.out.println("直方图相似度: " + similarity2);
            if (similarity2 < 0.4) {
                similarity = similarity2;
            } else {
                double similarity1 = getSSIMByProcessing(originalMat, templateMat, false, true, true);
                System.out.println("ssim相似度: " + similarity1);
                if (similarity1 > 0.8 && similarity2 > 0.8) {
                    similarity = similarity1;
                } else if (similarity1 > 0.8 && similarity2 < 0.8 && similarity3 != 1 && similarity1 != 1) {
                    similarity = similarity3;
                } else {
                    similarity = Math.min(similarity1, similarity2);
                }
            }
        }
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        System.out.println("Execution time in milliseconds: " + executionTime);
        System.out.println("综合相似度: " + similarity);
    }


}
