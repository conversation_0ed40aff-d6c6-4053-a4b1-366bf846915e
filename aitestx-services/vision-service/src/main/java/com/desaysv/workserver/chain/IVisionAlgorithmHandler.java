package com.desaysv.workserver.chain;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 13:21
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
public interface IVisionAlgorithmHandler {

    VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm,
                                 VisionEventHandler visionEventHandler,
                                 IVisionAlgorithmHandleChain handleChain) throws OperationFailNotification;

}