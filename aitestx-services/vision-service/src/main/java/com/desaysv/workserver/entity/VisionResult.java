package com.desaysv.workserver.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.algorithm.base.MatchResultModel;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import lombok.Data;
import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.Mat;

import java.text.NumberFormat;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 16:08
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
@Data
public class VisionResult {

    @JSONField(serialize = false)
    private final NumberFormat NUMBER_FORMAT = NumberFormat.getPercentInstance();

    private String templateName;

    private boolean passed;

    //对比阈值分数（0-1）
    private double score;

    private String message;

    private String extraDesc;

    private AbsoluteRoiRect roiRect; //ROI边框

    private AbsoluteRoiRect enlargeRoiRect;

    private Rectangle bestMatch;

    private PointInt centerPoint;

    private boolean largeImage;

    private String ocrResultText;

    private List<MatchResultModel> resultModelList;

    @JSONField(serialize = false)
    private Frame originalFrame; //原始图片

    @JSONField(serialize = false)
    private Frame eventFrame; //出错时的帧

    private double frequency;//频率

    private CloudVisionPathInfo cloudVisionPathInfo;

//    @JSONField(serialize = false)
//    private Frame roiFrame;

    private FileVisionResult fileVisionResult = new FileVisionResult();

    public VisionResult() {
        NUMBER_FORMAT.setMaximumFractionDigits(2);
    }

    @JSONField(serialize = false)
    public boolean isFailed() {
        return !isPassed();
    }

    public void addMessage(String extraMessage) {
        if (message == null) {
            message = extraMessage;
        } else {
            message += " | " + extraMessage;
        }
    }


    @JSONField(serialize = false)
    public String getPercentScore() {
        return NUMBER_FORMAT.format(score);
    }

    public SimpleVisionResult simpleResult() {
        SimpleVisionResult simpleVisionResult = new SimpleVisionResult();
        simpleVisionResult.setTemplateName(templateName);
        simpleVisionResult.setPassed(passed);
        simpleVisionResult.setScore(score);
        simpleVisionResult.setMessage(message);
        simpleVisionResult.setBestMatch(bestMatch);
        simpleVisionResult.setCenterPoint(centerPoint);
        simpleVisionResult.setLargeImage(largeImage);
        simpleVisionResult.setResultModelList(resultModelList);
        simpleVisionResult.setFileVisionResult(fileVisionResult);
        return simpleVisionResult;
    }

}
