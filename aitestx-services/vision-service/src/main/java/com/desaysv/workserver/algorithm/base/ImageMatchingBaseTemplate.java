package com.desaysv.workserver.algorithm.base;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


/**
 * 基于模板的图像匹配
 */
@Slf4j
public abstract class ImageMatchingBaseTemplate implements ImageTemplateAlgorithmStrategy {

    private interface ImageMatchEventHandler {

        boolean matchTerminated();

    }

    /**
     * 模板匹配
     *
     * @param visionAlgorithm 视觉算法
     * @return VisionResult
     */
    protected abstract VisionResult templateMatching(VisionAlgorithm visionAlgorithm) throws ImageTemplateMatchingException;

    private VisionResult combineVisionResults(List<VisionResult> visionResults) {
        if (visionResults.size() == 1) {
            return visionResults.get(0);
        }
        VisionResult visionResult = new VisionResult();
        for (VisionResult result : visionResults) {
            visionResult.addMessage(result.getMessage());
//            visionResult.setOriginalFrame(visionResult.getOriginalFrame());
            visionResult.setScore(result.getScore());
            if (result.isFailed()) {
                visionResult.setTemplateName(result.getTemplateName());
                visionResult.setEventFrame(result.getEventFrame());
            }
        }
        return visionResult;
    }

    private VisionResult multiMatch(VisionAlgorithm visionAlgorithm) {
        String wholeTemplateName = visionAlgorithm.getTemplateName();
        List<VisionResult> visionResults = new ArrayList<>();
        Map<String, Supplier<Boolean>> functionMap = BooleanExpressionEvaluator.autoPopulateFunctionMap(wholeTemplateName,
                atomicTemplateName -> () -> {
                    VisionResult visionResult;
                    try {
                        visionResult = singleMatch(atomicTemplateName, visionAlgorithm);
                    } catch (OperationFailNotification e) {
                        log.error(e.getMessage(), e);
                        visionResult = new VisionResult();
                        visionResult.setMessage(e.getMessage());
                    }
                    visionResults.add(visionResult);
                    return visionResult.isPassed();
                }
        );
        BooleanExpressionEvaluator evaluator = new BooleanExpressionEvaluator(functionMap);
        boolean finalMultiResult = evaluator.evaluate(wholeTemplateName);
        VisionResult visionResult = combineVisionResults(visionResults);
        visionResult.setPassed(finalMultiResult);
        return visionResult;
    }

    private VisionResult singleMatch(VisionAlgorithm visionAlgorithm) throws OperationFailNotification {
        return singleMatch(visionAlgorithm.getTemplateName(), visionAlgorithm);
    }

    /**
     * 单次匹配
     *
     * @param visionAlgorithm 视觉算法
     * @return 识别结果
     */
    private VisionResult singleMatch(String atomicTemplateName, VisionAlgorithm visionAlgorithm) throws OperationFailNotification {
        long startMills = System.currentTimeMillis();
        VisionResult visionResult;
        try {
            //设置模板或实时画面回调，不用直接依赖streamService.grab，使用依赖反转
            visionAlgorithm.setLiveFrame(visionAlgorithm.getVisionEventHandler().captureLiveFrame());
//        log.info("visionAlgorithm->getLiveFrame:{}", visionAlgorithm.getLiveFrame());
            visionAlgorithm.setTemplateFrame(visionAlgorithm.getVisionEventHandler().getTemplateFrame(atomicTemplateName));
            visionAlgorithm.setRoiRect(visionAlgorithm.getVisionEventHandler().getAbsoluteRoiRect(atomicTemplateName));
            preProcessingEveryFrame(visionAlgorithm);

            //执行各种算法的匹配
            VisionResult result = templateMatching(visionAlgorithm);
            //设置原子模板名
            result.setTemplateName(atomicTemplateName);
            //处理匹配结果
            visionResult = handleVisionResult(result, visionAlgorithm);
            //打印匹配结果
            printVisionResult(visionResult, startMills);
        } catch (ImageTemplateMatchingException | FrameGrabberException e) {
            log.error(e.getMessage(), e);
            //处理异常
            visionResult = new VisionResult();
            visionResult.setPassed(false);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
        }
        visionResult.setTemplateName(atomicTemplateName);
        return visionResult;
    }

    /**
     * 循环识别
     *
     * @param visionAlgorithm 视觉算法
     * @return 识别结果
     */
    private VisionResult loopMatch(VisionAlgorithm visionAlgorithm,
                                   VisionEventHandler visionEventHandler) throws OperationFailNotification {
        double timeout = visionAlgorithm.getTimeout() * 1000;
        long startTime = System.currentTimeMillis();
        long collapseTime;
        VisionResult visionResult;

        while (true) {
            visionResult = continuouslySingleMatch(visionAlgorithm, visionEventHandler,
                    visionAlgorithm.getRecognizedDuration(), () -> System.currentTimeMillis() - startTime >= timeout);
            if (visionResult.isPassed()) {
                //识别通过
                break;
            }
            collapseTime = System.currentTimeMillis() - startTime;
            if (collapseTime >= timeout) {
                //识别超时
                String message = String.format("图像识别超时:%.2fs", visionAlgorithm.getTimeout());
                log.info(message);
                visionResult.addMessage(message);
                break;
            }
            log.info("图像识别继续，{}", getMatchDescription(visionResult, visionAlgorithm));
            try {
                TimeUnit.MILLISECONDS.sleep(MIN_MATCHING_INTERVAL);
            } catch (InterruptedException e) {
                log.warn("收到图像识别停止信号:{}", e.getMessage());
                break;
            }
        }
        collapseTime = System.currentTimeMillis() - startTime;
        log.info("本次图像循环识别一共耗时{}s", collapseTime / 1000.0);
        return visionResult;
    }

    /**
     * 持续单次图像匹配
     *
     * @param visionAlgorithm
     * @param visionEventHandler
     * @param recognizedDuration
     * @param imageMatchEventHandler
     * @return
     * @throws OperationFailNotification
     */
    private VisionResult continuouslySingleMatch(VisionAlgorithm visionAlgorithm,
                                                 VisionEventHandler visionEventHandler,
                                                 double recognizedDuration,
                                                 ImageMatchEventHandler imageMatchEventHandler) throws OperationFailNotification {
        VisionResult visionResult;
        visionAlgorithm.setVisionEventHandler(visionEventHandler);
        long timeStart = System.currentTimeMillis();
        do {
            if (!StrUtils.isEmpty(visionAlgorithm.getTemplateName()) && (visionAlgorithm.getTemplateName().contains("&") ||
                    visionAlgorithm.getTemplateName().contains("|"))) {
                visionResult = multiMatch(visionAlgorithm);
            } else {
                visionResult = singleMatch(visionAlgorithm);
            }
            if (recognizedDuration <= 0) {
                log.info("要求只识别一次，退出此次图像断言任务");
                break;
            }
            if (imageMatchEventHandler != null && imageMatchEventHandler.matchTerminated()) {
                double singleAssertSuccessDuration = (System.currentTimeMillis() - timeStart) / 1000.0;
                log.info("图像断言通过已持续{}秒", singleAssertSuccessDuration);
                if (singleAssertSuccessDuration < recognizedDuration) {
                    visionResult.setPassed(false);
                    visionResult.setMessage(String.format("超时时间到，只连续断言成功%.2f秒，无法满足连续断言%.2f秒的要求",
                            singleAssertSuccessDuration, recognizedDuration));
                    log.info(visionResult.getMessage());
                }
                break;
            }
            if (!visionResult.isPassed() || visionAlgorithm.isOnlyTestSimilarity()) {
                //失败图片或者只测试相似度
                break;
            } else {
                log.info("图像断言通过已持续{}秒", (System.currentTimeMillis() - timeStart) / 1000.0);
            }
        } while ((System.currentTimeMillis() - timeStart) / 1000.0 < recognizedDuration);
        return visionResult;
    }


    @Override
    public VisionResult imageRecognize(VisionAlgorithm visionAlgorithm,
                                       VisionEventHandler visionEventHandler) throws OperationFailNotification {
        VisionResult visionResult;
        log.info("图像识别间隔:{}ms", MIN_MATCHING_INTERVAL);
        double timeout = visionAlgorithm.getTimeout();
        double recognizedDuration = visionAlgorithm.getRecognizedDuration();
        if (timeout <= 0) {
            //单次匹配
            visionResult = continuouslySingleMatch(visionAlgorithm, visionEventHandler, recognizedDuration, null);
        } else {
            //循环匹配
            visionResult = loopMatch(visionAlgorithm, visionEventHandler);
        }
        if (visionResult.getBestMatch() == null) {
            Frame liveFrame = visionAlgorithm.getLiveFrame();
            if (liveFrame != null) {
                visionResult.setBestMatch(new Rectangle(0, 0, liveFrame.imageWidth, liveFrame.imageHeight));
            }
        }
        return visionResult;
    }
}
