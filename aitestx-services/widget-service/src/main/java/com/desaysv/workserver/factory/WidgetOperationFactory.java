package com.desaysv.workserver.factory;

import com.desaysv.workserver.base.operation.base.OperationAbstractFactory;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.operation.method.WidgetOperationMethod;
import com.desaysv.workserver.operation.parameter.WidgetOperationParameter;
import com.desaysv.workserver.operation.targets.WidgetOperationTarget;
import com.desaysv.workserver.utils.MapToObj;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 14:52
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@Component
@Lazy
public class WidgetOperationFactory implements OperationAbstractFactory {

    @Override
    public OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        OperationTarget widgetOperationTarget;
        try {
            widgetOperationTarget = MapToObj.mapToObj(operationTarget, WidgetOperationTarget.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            widgetOperationTarget = new WidgetOperationTarget();
        }
        return widgetOperationTarget;
    }

    @Override
    public OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        OperationMethod widgetOperationMethod;
        try {
            widgetOperationMethod = MapToObj.mapToObj(operationMethod, WidgetOperationMethod.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            widgetOperationMethod = new WidgetOperationMethod();
        }
        return widgetOperationMethod;
    }

    @Override
    public OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        OperationParameter widgetOperationParameter;
        try {
            widgetOperationParameter = MapToObj.mapToObj(operationParameter, WidgetOperationParameter.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            widgetOperationParameter = new WidgetOperationParameter();
            widgetOperationParameter.putAll(operationParameter);
        }
        return widgetOperationParameter;
    }
}
