package com.desaysv.workserver.operation.method;

import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.method.UIOperationMethod;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-15 19:27
 * @description : 控件操作方法
 * @modified By : lwj
 * @since : 2022-3-16
 */
public class WidgetOperationMethod extends UIOperationMethod {

    public static OperationMethod Keyword(String keyword) {
        OperationMethod operationMethod = new WidgetOperationMethod();
        operationMethod.setKeyword(keyword);
        registerMethod(keyword, UIOperationMethod.class);
        return operationMethod;
    }


}
