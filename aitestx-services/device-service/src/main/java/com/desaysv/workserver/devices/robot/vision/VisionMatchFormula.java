package com.desaysv.workserver.devices.robot.vision;


import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.entity.Point2D;

public class VisionMatchFormula {

    /**
     * 相机和机械臂坐标系转换公式 x'=ax+by+c，y'=dx+ey+f
     *
     * @param pixelPoint        相机坐标点
     * @param visionGuideConfig 校准配置
     * @return 机械臂坐标
     */
    public static Point2D transferPixelPointToRobot(PointInt pixelPoint, VisionGuideConfig visionGuideConfig) {
        VisionGuideCalibrationData visionGuideCalibrationData = visionGuideConfig.getVisionGuideCalibrationData();
        double x = visionGuideCalibrationData.getA() * pixelPoint.getX() +
                visionGuideCalibrationData.getB() * pixelPoint.getY() +
                visionGuideCalibrationData.getC();
        double y = visionGuideCalibrationData.getD() * pixelPoint.getX() +
                visionGuideCalibrationData.getE() * pixelPoint.getY() +
                visionGuideCalibrationData.getF();
        return new Point2D(x, y);
    }

}
