package com.desaysv.workserver.devices.robot.type.mg400.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.devices.robot.type.mg400.entity.ErrorInfo;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 13:29
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
public class ErrorInfoHelper {
    private static final Map<Integer, ErrorInfo> controllerErrorInfoMap = new HashMap<>();
    private static final Map<Integer, ErrorInfo> servoErrorInfoMap = new HashMap<>();
    @Getter
    private static final ErrorInfoHelper instance = new ErrorInfoHelper();

    public ErrorInfo getController(Integer id) {
        return controllerErrorInfoMap.get(id);
    }

    public ErrorInfo getServo(int id) {
        return servoErrorInfoMap.get(id);
    }

    public RobotReply parseResult(String strResult) {
        RobotReply robotReply = new RobotReply();
        int iBegPos = strResult.indexOf('{');
        if (iBegPos < 0) {
            robotReply.setOk(false);
            return robotReply;
        }
        int iEndPos = strResult.indexOf('}',
                iBegPos + 1);
        if (iEndPos < 0) {
            robotReply.setOk(false);
            return robotReply;
        }
        boolean bOk = strResult.startsWith("0,");
        if (iEndPos - iBegPos <= 1) {
            robotReply.setOk(bOk);
            return robotReply;
        }
        strResult = strResult.substring(iBegPos + 1, iEndPos);
        if (strResult.isEmpty()) {
            robotReply.setOk(bOk);
            return robotReply;
        }
        StringBuilder sb = new StringBuilder();
//        strResult = '[' + strResult + ']';
        try {
            JSONArray jsonArray = JSONArray.parseArray(strResult);

            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    JSONArray array = ((JSONArray) jsonArray.get(i)).to(JSONArray.class);
                    for (Object o : array) {
                        ErrorInfo bean;
                        if (i == 0) {
                            bean = getController((Integer) o);
                        } else {
                            bean = getServo((Integer) o);
                        }
                        if (null != bean) {
                            sb.append("ID:").append(bean.getId()).append("\r\n");
                            sb.append("Type:").append(bean.getType()).append("\r\n");
                            sb.append("Level:").append(bean.getLevel()).append("\r\n");
                            sb.append("Solution:").append(bean.getEn().getSolution()).append("\r\n");
                        }
                    }
                } catch (ClassCastException e) {
                    break;
                }
            }

            if (sb.length() > 0) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");//设置日期格式
                String strTime = "Time Stamp:" + df.format(new Date());// new Date()为获取当前系统时间
//                log.error(strTime + "\r\n" + sb);
            }
        } catch (JSONException e) {
            log.warn(String.format("解析%s错误", strResult), e);
        }
        robotReply.setOk(bOk);
        robotReply.setMessage(sb.toString());
        return robotReply;
    }


    private ErrorInfoHelper() {
//        String projectPath = System.getProperty("user.dir");
        String alarmControllerPath = "alarm_controller.json";
        String alarmServoPath = "alarm_servo.json";
        StringBuffer alarmControllerBuffer = readConfigJson(alarmControllerPath);
        StringBuffer alarmServoBuffer = readConfigJson(alarmServoPath);
        configErrorInfoMap(alarmControllerBuffer, controllerErrorInfoMap);
        configErrorInfoMap(alarmServoBuffer, servoErrorInfoMap);
    }

    private StringBuffer readConfigJson(String filepath) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(filepath);
        InputStreamReader reader;
        assert in != null;
        StringBuffer buffer = new StringBuffer();
        try {
            reader = new InputStreamReader(in);
            BufferedReader bufferedReader = new BufferedReader(reader);
            String lineText;
            while ((lineText = bufferedReader.readLine()) != null) {
                buffer.append(lineText);
            }
            reader.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return buffer;
    }

    private void configErrorInfoMap(StringBuffer buffer, Map<Integer, ErrorInfo> errorInfoMap) {
        JSONArray objects = JSONArray.parseArray(buffer.toString());
        List<ErrorInfo> errorInfoList = JSONArray.parseArray(objects.toJSONString(), ErrorInfo.class);
        for (ErrorInfo errorInfo : errorInfoList) {
            errorInfoMap.put(errorInfo.getId(), errorInfo);
        }
    }
}
