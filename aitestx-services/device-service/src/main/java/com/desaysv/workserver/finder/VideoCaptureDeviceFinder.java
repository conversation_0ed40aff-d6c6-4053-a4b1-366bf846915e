package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.videocapture.VideoCaptureDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.PingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: QinHao
 * @description:
 * @date: 2024/12/23 18:17
 */
@Component
@Slf4j
@Lazy
public class VideoCaptureDeviceFinder {
    public List<Device> findAllVideoCaptureDevices(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        VideoCaptureDevice device;
        int deviceIndex;
        try {
            for (String ipAddress : VideoCaptureDevice.IP_ADDRESS) {
                if (PingUtils.ping(ipAddress)) {
                    device = new VideoCaptureDevice();
                    device.setDeviceName(ipAddress);
                    device.setDeviceUniqueCode(device.getDeviceName());
                    deviceIndex = Device.getDeviceModelIndex(deviceModel);
                    device.setAliasName(deviceModel + "#" + deviceIndex);
                    device.setDeviceOperationParameter(new DeviceOperationParameter());
                    devices.add(device);
                }
            }
        } catch (Exception e) {
            log.debug(e.getMessage(), e);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
        }
        return devices;
    }
}
