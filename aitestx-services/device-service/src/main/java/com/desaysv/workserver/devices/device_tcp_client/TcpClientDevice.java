package com.desaysv.workserver.devices.device_tcp_client;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.device_tcp_client.interfaces.ITcpClientDevice;
import com.desaysv.workserver.devices.device_udp.DIDInfo;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static com.desaysv.workserver.devices.device_udp.UdpDevice.parseCommandArrayByItems;
import static com.desaysv.workserver.utils.ByteUtils.StringToHexByteArr;

@Slf4j
public class TcpClientDevice extends Device implements ITcpClientDevice {
    private CommandTcpClient tcpClient;

    public TcpClientDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TCP_CLIENT;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.TcpClient.TCP_CLIENT;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String deviceName = getDeviceName();
        String[] str = deviceName.split(":");
        String ip = str[0];
        String port = str[1];
        tcpClient = CommandTcpClient.getInstance();
        tcpClient.setServerIP(ip);
        tcpClient.setServerPort(Integer.parseInt(port));
        return tcpClient.initialize();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return tcpClient.closeDevice();
    }

    @Override
    public Map<String, DIDInfo> getDIDInfo(String items) {
        String receiveData = tcpClient.sendReceive(items);
        log.info("receiveData:{}", receiveData);
        String parseData = parseTcpCommandArrayByItems(items, receiveData);
        Map<String, DIDInfo> didInfoMap = new HashMap<>();
        didInfoMap.put(items, new DIDInfo(StringToHexByteArr(receiveData), parseData));
        return didInfoMap;
    }

    @Override
    public boolean reconnectTcpClientDevice(int tryTimes) {
        if (tryTimes <= 0) return false;
        int attempts = 0;
        while (attempts < tryTimes) {
            try {
                if (close() && open()) return true;
                attempts++;
                Thread.sleep(500);
            } catch (DeviceOpenException | DeviceOpenRepeatException | DeviceCloseException e) {
                log.error("Recoverable error: {}", e.getMessage()); // 可考虑继续重试
                attempts++; // 继续消耗尝试次数
            } catch (InterruptedException e) {
                log.warn("Thread interrupted during reconnection");
                Thread.currentThread().interrupt(); // 恢复中断状态
                return false; // 立即终止
            }
        }
        return false;
    }

    public static String parseTcpCommandArrayByItems(String items, String receiveData) {
        String id = items.toLowerCase().replace("0x", "");
        return parseCommandArrayByItems(items, StringToHexByteArr(receiveData.substring(receiveData.lastIndexOf(id.toLowerCase()) + 4)));
    }

    public static void main(String[] args) {
        String item = "0xf18a";
        String receiveData1 = "02fd800200000005040d0f020002fd8001000000016040d0f0262f1874c41412d37303030383031303220202020";
        String receiveData3 = "02fd800200000005040d0f020002fd8001000000016040d0f0262f1874c414b2d37303035303031372d45502020";
        String receiveData2 = "02fd800200000005040d0f020002fd800100000011040d0f0262f18a3841412020202020202020";
        String receiveData4 = "4c414b5f37303035303031375f455031";
        String commandResultString = ByteUtils.bytesToStringWithPadRemoval(StringToHexByteArr(receiveData4), (byte) 0x20, StandardCharsets.US_ASCII);
        commandResultString = commandResultString.replace("-", "_");
        System.out.println(commandResultString);
//        System.out.println(parseTcpCommandArrayByItems(item, receiveData2));
//        String receiveData = String.format("%-20s: %s", StringToHexByteArr("02fd800200000005040d0f020002fd8001000000015040d0f0262f18c3233423036433030303139202020200000000000000000000000000"));
//        System.out.println(parseTcpCommandArrayByItems(item, receiveData));
        String command = "LAK_70050017_EP1";
        byte[] commandByteArray = ByteUtils.toPaddedByteArray(command, 16, StandardCharsets.US_ASCII,
                (byte) 0x20);
        System.out.println(Hex.encodeHexString(commandByteArray));
    }
}
