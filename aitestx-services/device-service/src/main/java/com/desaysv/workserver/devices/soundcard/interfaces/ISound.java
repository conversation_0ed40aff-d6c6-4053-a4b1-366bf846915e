package com.desaysv.workserver.devices.soundcard.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.soundcard.SoundMonitor;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 声卡操作接口
 */
public interface ISound {
    Logger log = LogManager.getLogger(ISound.class.getSimpleName());
    AtomicLong startTime = new AtomicLong(0);

    /**
     * 根据命令（"ON" 或 "OFF"）控制音频设备通道的录音状态（开启或关闭）
     *
     * @param deviceChannel
     * @param command
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_RECORD_ON_OFF"})
    default ActualExpectedResult soundRecordOnOff(Integer deviceChannel, String command) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean isOpen = command.equalsIgnoreCase("ON");
        if (isOpen) {
            getRecordedVolumes(deviceChannel).clear();
            startRecording(deviceChannel);
            startTime.set(System.currentTimeMillis());
            log.info("声卡通道{} 开始聆听", deviceChannel);
        } else {
            long executionTime = System.currentTimeMillis() - startTime.get(); // 计算耗时
            log.info("声卡通道{} 结束聆听 耗时：{}ms", deviceChannel, executionTime);
            stopRecording();
        }

        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        actualExpectedResult.put(methodName, true, "");
        return actualExpectedResult;
    }

    /**
     * 检查声音是否达到起始值
     *
     * @param deviceChannel
     * @param startVolume
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START"})
    default ActualExpectedResult isSoundAboveStartVolume(Integer deviceChannel, float startVolume) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否达到或超过起始值
            if (maxVolume >= startVolume) {
                log.info("声卡通道{}音量达标，已达到或超过起始值: {}dB", deviceChannel, startVolume);
                pass = true;
            } else {
                log.info("声卡通道{}音量未达标 未超过起始值: {}dB", deviceChannel, startVolume);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundAboveStartVolume", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_LOUD"})
    default ActualExpectedResult isSoundContinuousLoud(Integer deviceChannel, float loudDb) throws DeviceReadException{
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float minVolume = Collections.min(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最小音量: {}dB", deviceChannel, recordedVolumes.size(), minVolume);

            // 判断最小音量是否达到或超过蜂鸣阈值
            if (minVolume >= loudDb) {
                log.info("声卡通道{}所有音量达标，已达到或超过蜂鸣阈值: {}dB", deviceChannel, loudDb);
                pass = true;
            } else {
                log.info("声卡通道{}不是所有音量都达标，都超过蜂鸣阈值: {}dB", deviceChannel, loudDb);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundContinuousLoud", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_MUTE"})
    default ActualExpectedResult isSoundContinuousMute(Integer deviceChannel, float muteDb) throws DeviceReadException{
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否低于静音阈值
            if (maxVolume <= muteDb) {
                log.info("声卡通道{}所有音量达标，都低于静音阈值: {}dB", deviceChannel, muteDb);
                pass = true;
            } else {
                log.info("声卡通道{}不是所有音量都达标，都低于静音阈值: {}dB", deviceChannel, muteDb);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundContinuousMute", pass, "");
        return actualExpectedResult;
    }

    /**
     * 声音在持续时间有无达到范围值（起始到终止）
     *
     * @param deviceChannel
     * @param startVolume
     * @param endVolume
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START_TO_END"})
    default ActualExpectedResult isSoundInVolumeRange(Integer deviceChannel, float startVolume, float endVolume) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否达到或超过起始值
            if (maxVolume >= startVolume && maxVolume <= endVolume) {
                log.info("声卡通道{}音量达标，达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                pass = true;
            } else {
                log.info("声卡通道{}音量未达标，未达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundInVolumeRange", pass, "");
        return actualExpectedResult;
    }

    /**
     * 声音在持续时间有无达到范围值（起始到终止）
     *
     * @param deviceChannel
     * @param startVolume
     * @param endVolume
     * @param interval
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START_TO_END_TIME"})
    default ActualExpectedResult isSoundInVolumeRange(Integer deviceChannel, float startVolume, float endVolume, String interval) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);  //不带单位，代表ms
        log.info("声卡通道{}查看当前音量是否达到目标范围音量:({},{}), 间隔:{}s", deviceChannel, startVolume, endVolume, seconds);
        startRecording(deviceChannel);//录音
        try {
            Thread.sleep((long) (seconds * 1000));
        } catch (InterruptedException e) {
            log.warn("聆听过程中线程被中断", e);
        }
        stopRecording(); // 停止录音
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否达到或超过起始值
            if (maxVolume >= startVolume && maxVolume <= endVolume) {
                log.info("声卡通道{}音量达标，达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                pass = true;
            } else {
                log.info("声卡通道{}音量未达标，未达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }

        actualExpectedResult.put("isSoundInVolumeRange", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_CYCLE"})
    default ActualExpectedResult isBeeperCycle(Integer deviceChannel, double threshold, double targetPeriod) {
        log.info("声卡通道{}查看当前音量是否达到目标周期:{}s", deviceChannel, targetPeriod);
        int minIntervalMillis = 50;//默认50ms
        double recordSeconds = 5;//默认5s
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        if (detectBeeperCycle(deviceChannel, threshold, minIntervalMillis, recordSeconds, targetPeriod)) {
            pass = true;
        }
        actualExpectedResult.put("detectBeeperCycle", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_CYCLE_TIME"})
    default ActualExpectedResult isBeeperCycleWithTime(Integer deviceChannel, double threshold, double targetPeriod,double recordSeconds) {
        log.info("声卡通道{}查看当前音量是否达到目标周期:{}s，录音时间:{}s", deviceChannel, targetPeriod, recordSeconds);
        int minIntervalMillis = 50;//默认50ms
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        if (detectBeeperCycle(deviceChannel, threshold, minIntervalMillis, recordSeconds, targetPeriod)) {
            pass = true;
        }
        actualExpectedResult.put("detectBeeperCycleWithTime", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_FREQUENCY"})
    default ActualExpectedResult isBeeFrequency(Integer deviceChannel, double minFrequency, double maxFrequency) {
        log.info("声卡通道{}查看当前音量是否达到目标频率:{}HZ~{}HZ", deviceChannel, minFrequency, maxFrequency);
        double recordSeconds = 5;//默认5s
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        if (detectBeepFrequency(deviceChannel, recordSeconds, minFrequency, maxFrequency)) {
            pass = true;
        }
        actualExpectedResult.put("detectBeepFrequency", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_FREQUENCY_TIME"})
    default ActualExpectedResult isBeeFrequencyWithTime(Integer deviceChannel, double minFrequency, double maxFrequency,double recordSeconds) {
        log.info("声卡通道{}查看当前音量是否达到目标频率:{}HZ~{}HZ,录音时间:{}s", deviceChannel, minFrequency, maxFrequency, recordSeconds);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        if (detectBeepFrequency(deviceChannel, recordSeconds, minFrequency, maxFrequency)) {
            pass = true;
        }
        actualExpectedResult.put("detectBeepFrequencyWithTime", pass, "");
        return actualExpectedResult;
    }


    List<Float> getRecordedVolumes(Integer deviceChannel);

    String saveRecordingToFile();

    float getVolume(Integer deviceChannel) throws DeviceReadException;

    void startRecording(Integer deviceChannel);

    void stopRecording();

    /**
     * 开始声音监控
     */
    void beginSoundMonitor(Integer deviceChannel);

    /**
     * 结束声音监控
     */
    boolean endSoundMonitor(Integer deviceChannel, SoundMonitor soundMonitor) throws OperationFailNotification;

    boolean detectBeepFrequency(Integer deviceChannel, double recordSeconds, double minFrequency, double maxFrequency);

    boolean detectBeeperCycle(Integer deviceChannel, double threshold, int minIntervalMillis, double recordSeconds, double targetPeriod);
}
