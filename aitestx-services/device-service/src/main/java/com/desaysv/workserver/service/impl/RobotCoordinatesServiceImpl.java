package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.RobotCoordinatesMapper;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.service.RobotCoordinatesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 13:54
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
@Service
@Lazy
public class RobotCoordinatesServiceImpl implements RobotCoordinatesService {
    @Autowired
    private RobotCoordinatesMapper robotCoordinatesMapper;

    public Integer deleteCoordinatesByUUID(String coordinatesUUID) {
        return robotCoordinatesMapper.deleteByCoordinatesUUID(coordinatesUUID);
    }

    @Override
    public Integer deleteCoordinatesByCondition(RobotCoordinates robotCoordinates) {
        return robotCoordinatesMapper.deleteByCondition(robotCoordinates);
    }

    @Override
    public Integer addCoordinates(RobotCoordinates robotCoordinates) {
        return robotCoordinatesMapper.insert(robotCoordinates);
    }

    @Override
    public RobotCoordinates getCoordinatesByCondition(RobotCoordinates robotCoordinates) {
        return robotCoordinatesMapper.selectByCondition(robotCoordinates);
    }

    @Override
    public Integer updateCoordinatesByUUID(RobotCoordinates robotCoordinates) {
        return robotCoordinatesMapper.updateByCoordinatesUUID(robotCoordinates);
    }

    @Override
    public List<RobotCoordinates> getAllCoordinates() {
        return robotCoordinatesMapper.selectAll();
    }

    @Override
    public List<RobotCoordinates> getAllCoordinates(Integer projectId, Integer deviceId) {
        return robotCoordinatesMapper.selectAllByCondition(projectId, deviceId);
    }

    @Override
    public Integer clearAllCoordinates(Integer projectId, Integer deviceId) {
        return robotCoordinatesMapper.clearAllByCondition(projectId, deviceId);
    }
}
