package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.config.lin.LinConfig;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.bus.interfaces.IBusDevice;
import com.desaysv.workserver.devices.bus.interfaces.ILinSequence;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public abstract class BaseLinDevice extends ConfigurableDevice<LinConfig> implements IBusDevice, ILinSequence {
    public BaseLinDevice() {
        this(new DeviceOperationParameter());
    }

    public BaseLinDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_LIN;
    }

    @Override
    public Class<LinConfig> getDeviceConfigClass() {
        return LinConfig.class;
    }

    @Override
    public void updateConfig(LinConfig linConfig) {
        super.updateConfig(linConfig);
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }
}
