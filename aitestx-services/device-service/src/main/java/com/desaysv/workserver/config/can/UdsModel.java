package com.desaysv.workserver.config.can;

import lombok.Data;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/7/4 13:49
 * @Version: 1.0
 * @Desc : 描述信息
 */
@Data
public class UdsModel {

    private int channel;

    private String requestId;

    private String responseId;

    private String serviceData;

    private String responseData;

    private String treeData;

    private String functionId;

    private boolean enableFunction;

    private boolean enableResponse;

    public UdsModel(Integer channel, String requestId, String responseId, String serviceData, String responseData,String functionId,boolean enableFunction) {
        this.channel = channel;
        this.requestId = requestId;
        this.responseId = responseId;
        this.serviceData = serviceData;
        this.responseData = responseData;
        this.functionId = functionId;
        this.enableFunction = enableFunction;
    }

    public UdsModel() {

    }
}
