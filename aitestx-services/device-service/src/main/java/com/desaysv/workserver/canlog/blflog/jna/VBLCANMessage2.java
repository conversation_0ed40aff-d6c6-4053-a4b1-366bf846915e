package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLCANMessage2 extends Structure {
    public VBLObjectHeader mHeader;
    public short mChannel;
    public byte mFlags;
    public byte mDLC;
    public int mID;
    public byte[] mData;
    public int mFrameLength;
    public byte mBitCount;
    public byte mReserved1;
    public short mReserved2;

    public VBLCANMessage2() {
        this.mData = new byte[8];
    }

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mHeader", "mChannel", "mFlags", "mDLC", "mID", "mData", "mFrameLength", "mBitCount", "mReserved1", "mReserved2");
    }
}
