package com.desaysv.workserver.devices.bus.e2e;

import com.google.common.primitives.Bytes;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class GeelyE2E implements E2EHandler {
    private static final byte[] CRC_TABLE_8 = Bytes.toArray(Arrays.asList(
            0x00, 0x1d, 0x3a, 0x27, 0x74, 0x69, 0x4e, 0x53,
            0xe8, 0xf5, 0xd2, 0xcf, 0x9c, 0x81, 0xa6, 0xbb,
            0xcd, 0xd0, 0xf7, 0xea, 0xb9, 0xa4, 0x83, 0x9e,
            0x25, 0x38, 0x1f, 0x02, 0x51, 0x4c, 0x6b, 0x76,
            0x87, 0x9a, 0xbd, 0xa0, 0xf3, 0xee, 0xc9, 0xd4,
            0x6f, 0x72, 0x55, 0x48, 0x1b, 0x06, 0x21, 0x3c,
            0x4a, 0x57, 0x70, 0x6d, 0x3e, 0x23, 0x04, 0x19,
            0xa2, 0xbf, 0x98, 0x85, 0xd6, 0xcb, 0xec, 0xf1,
            0x13, 0x0e, 0x29, 0x34, 0x67, 0x7a, 0x5d, 0x40,
            0xfb, 0xe6, 0xc1, 0xdc, 0x8f, 0x92, 0xb5, 0xa8,
            0xde, 0xc3, 0xe4, 0xf9, 0xaa, 0xb7, 0x90, 0x8d,
            0x36, 0x2b, 0x0c, 0x11, 0x42, 0x5f, 0x78, 0x65,
            0x94, 0x89, 0xae, 0xb3, 0xe0, 0xfd, 0xda, 0xc7,
            0x7c, 0x61, 0x46, 0x5b, 0x08, 0x15, 0x32, 0x2f,
            0x59, 0x44, 0x63, 0x7e, 0x2d, 0x30, 0x17, 0x0a,
            0xb1, 0xac, 0x8b, 0x96, 0xc5, 0xd8, 0xff, 0xe2,
            0x26, 0x3b, 0x1c, 0x01, 0x52, 0x4f, 0x68, 0x75,
            0xce, 0xd3, 0xf4, 0xe9, 0xba, 0xa7, 0x80, 0x9d,
            0xeb, 0xf6, 0xd1, 0xcc, 0x9f, 0x82, 0xa5, 0xb8,
            0x03, 0x1e, 0x39, 0x24, 0x77, 0x6a, 0x4d, 0x50,
            0xa1, 0xbc, 0x9b, 0x86, 0xd5, 0xc8, 0xef, 0xf2,
            0x49, 0x54, 0x73, 0x6e, 0x3d, 0x20, 0x07, 0x1a,
            0x6c, 0x71, 0x56, 0x4b, 0x18, 0x05, 0x22, 0x3f,
            0x84, 0x99, 0xbe, 0xa3, 0xf0, 0xed, 0xca, 0xd7,
            0x35, 0x28, 0x0f, 0x12, 0x41, 0x5c, 0x7b, 0x66,
            0xdd, 0xc0, 0xe7, 0xfa, 0xa9, 0xb4, 0x93, 0x8e,
            0xf8, 0xe5, 0xc2, 0xdf, 0x8c, 0x91, 0xb6, 0xab,
            0x10, 0x0d, 0x2a, 0x37, 0x64, 0x79, 0x5e, 0x43,
            0xb2, 0xaf, 0x88, 0x95, 0xc6, 0xdb, 0xfc, 0xe1,
            0x5a, 0x47, 0x60, 0x7d, 0x2e, 0x33, 0x14, 0x09,
            0x7f, 0x62, 0x45, 0x58, 0x0b, 0x16, 0x31, 0x2c,
            0x97, 0x8a, 0xad, 0xb0, 0xe3, 0xfe, 0xd9, 0xc4
    ));
    private static final byte E2E_P01_MAX_COUNTER_VALUE = 14; //最大计数器值
    private static final byte CRC_INITIAL_VALUE8 = (byte) 0xFF; //CRC初始值
    private static final byte CRC_POLYNOMIAL_8 = 0x1D; //CRC多项式
    private static final byte CRC_FINAL_XOR_CRC8 = (byte) 0xFF; //CRC XOR
    private final Map<String, Integer> counters = new HashMap<>();
    private final Map<String, Integer> signals = new HashMap<>();
    private static final int MAX_COUNTER_VALUE = 0xE;

    public static final String KEY_TRSMPARKLOCKD = "TrsmParkLockd";

    public GeelyE2E() {
        counters.put(KEY_TRSMPARKLOCKD, 0);
        counters.put("SecPwrSrcUloWarn", 0);
    }

    public void onFrameVddmBackBoneFr18() {
        int counter = counters.get(KEY_TRSMPARKLOCKD);
        counter = (counter + 1) % MAX_COUNTER_VALUE;
        counters.put(KEY_TRSMPARKLOCKD, counter);
        signals.put("TrsmParkLockdCntr", counter); //TrsmParkLockdCntr信号=counter
    }

    public void onSignalVDDMBackBoneSignalIPdu18(int trsmParkLockdValue) {
        byte[] buf = new byte[3];
        buf[1] = counters.get(KEY_TRSMPARKLOCKD).byteValue();
        buf[2] = (byte) trsmParkLockdValue;
        buf[0] = computeE2EP01CRC(buf, (short) 0x32, (short) 0, (short) 24);
        signals.put("TrsmParkLockdChks", (int) buf[0]);
    }

    public void onFrameVddmBackBoneFr04() {
        int counter = counters.get("SecPwrSrcUloWarn");
        counter = (counter + 1) % MAX_COUNTER_VALUE;
        counters.put("SecPwrSrcUloWarn", counter);
        signals.put("SecPwrSrcUloWarnCntr", counter);
    }

    public void onSignalVDDMBackBoneSignalIPdu04(int secPwrSrcUloWarnValue) {
        byte[] buf = new byte[3];
        buf[1] = counters.get("SecPwrSrcUloWarn").byteValue();
        buf[2] = (byte) secPwrSrcUloWarnValue;
        buf[0] = computeE2EP01CRC(buf, (short) 0x43, (short) 0, (short) 24);
        signals.put("SecPwrSrcUloWarnChks", (int) buf[0]);
    }

    public int getSignalValue(String signalName) {
        return signals.getOrDefault(signalName, 0);
    }

    static {
        // Initialize CRC_TABLE_8 here
        for (int i = 0; i < 256; i++) {
            int crc = i;
            for (int j = 0; j < 8; j++) {
                if ((crc & 0x80) != 0) {
                    crc = (crc << 1) ^ CRC_POLYNOMIAL_8;
                } else {
                    crc <<= 1;
                }
            }
            CRC_TABLE_8[i] = (byte) crc;
        }
    }

    public static byte calculateCRC8(byte[] data, int offset, int length, byte startValue, boolean isFirstCall) {
        byte crcValue;

        if (isFirstCall) {
            crcValue = CRC_INITIAL_VALUE8;
        } else {
            crcValue = (byte) (CRC_FINAL_XOR_CRC8 ^ startValue);
        }

        for (int i = offset; i < offset + length; i++) {
            crcValue = CRC_TABLE_8[(crcValue ^ data[i]) & 0xFF];
        }

        return (byte) (crcValue ^ CRC_FINAL_XOR_CRC8);
    }

    public static byte computeE2EP01CRC(byte[] data, short dataID, short crcOffset, short dataLength) {
        byte crc;
        byte[] localDataID = new byte[2];
        int calculatedOffset;
        int calculatedLength;

        localDataID[0] = (byte) dataID;
        localDataID[1] = (byte) (dataID >> 8);

        crc = calculateCRC8(localDataID, 0, 2, (byte) 0xFF, false);

        calculatedOffset = crcOffset >> 3;

        if (calculatedOffset > 0) {
            crc = calculateCRC8(data, 0, calculatedOffset, crc, false);
        }

        calculatedLength = dataLength >> 3;

        if (calculatedOffset < (calculatedLength - 1)) {
            crc = calculateCRC8(data, calculatedOffset + 1, calculatedLength - calculatedOffset - 1, crc, false);
        }

        return (byte) (crc ^ 0xFF);
    }

    @Override
    public void applyE2EAlgorithm(int busId, byte[] data) {
        if (busId == 48) {
            //VddmBackBoneFr18
            //VDDMBackBoneSignalIPdu18::TrsmParkLockdTrsmParkLockd | VDDMBackBoneSignalIPdu18::TrsmParkLockd_UB | VDDMBackBoneSignalIPdu18::TrsmParkLockdCntr
            onFrameVddmBackBoneFr18();
            onSignalVDDMBackBoneSignalIPdu18(1);
        } else if (busId == 49) {
            //VddmBackBoneFr04
            //VDDMBackBoneSignalIPdu04::SecPwrSrcUloWarnULoWarn | VDDMBackBoneSignalIPdu04::SecPwrSrcUloWarn_UB | VDDMBackBoneSignalIPdu04::SecPwrSrcUloWarnCntr
            onFrameVddmBackBoneFr04();
            onSignalVDDMBackBoneSignalIPdu04(1);
        }
    }
}