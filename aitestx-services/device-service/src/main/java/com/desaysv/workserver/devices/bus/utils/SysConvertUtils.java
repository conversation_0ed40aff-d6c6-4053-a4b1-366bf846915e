package com.desaysv.workserver.devices.bus.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class SysConvertUtils {
    private SysConvertUtils() {
    }
    /**
     * 十六进制转换十进制
     *
     * @param s 十六进制字符串
     * @return 十进制字符串
     */
    public static String hex2Decimal(String s) {
        if (s == null || !Pattern.compile("0[xX][0-9a-fA-F]+").matcher(s).find()) {
            return "NaN"; // 返回一个合适的表示非法值的字符串
        }
        Matcher matcher = Pattern.compile("0[xX][0-9a-fA-F]+").matcher(s);
        Integer decimal = null;
        if (matcher.find()) {
            String hexNumber = matcher.group();
            decimal = Integer.valueOf(hexNumber.substring(2), 16);
        }
        return String.valueOf(decimal);
    }


    /**
     * 将十进制数转换为十六进制表示的字符串。
     *
     * @param s 十进制表示的字符串
     * @return 如果转换成功，返回十六进制字符串；如果输入不为字符串或只包含非数字字符，则返回null。
     */
    public static String decimal2Hex(String s) {
        if (s == null || s.isEmpty()) {
            // 输入为空或null，返回null
            return null;
        }
        // 使用stream判断字符串是否只包含数字字符
        /*if (!s.chars().allMatch(Character::isDigit)) {
            // 输入包含非数字字符，返回null
            return null;
        }*/
        // 解析并转换十进制数为十六进制
        long decimal = Long.parseLong(s);
        return "0x" + Long.toHexString(decimal).toUpperCase();
    }

    /**
     * 将输入的十六进制字符串转换为带有 0x 前缀的十六进制字符串。
     *
     * @param s 十六进制表示的字符串（不带 0x 前缀）
     * @return 带有 0x 前缀的十六进制字符串；如果输入为空或不合法，返回 null。
     */
    public static String toPrefixedHex(String s) {
        if (s == null || s.isEmpty()) {
            return null; // 输入为空或null，返回null
        }
        // 检查输入是否为合法的十六进制字符串
        if (!s.matches("[0-9a-fA-F]+")) {
            return null; // 输入包含非十六进制字符，返回null
        }
        // 将字符串转换为大写并添加0x前缀
        return "0x" + s.toUpperCase();
    }

    /**
     * 将带有 0x 前缀的十六进制字符串转换为去掉 0x 前缀的十六进制字符串。
     *
     * @param s 带有 0x 前缀的十六进制字符串
     * @return 去掉 0x 前缀的十六进制字符串；如果输入为空或不合法，返回 null。
     */
    public static String removeHexPrefix(String s) {
        if (s == null || s.isEmpty()) {
            return null; // 输入为空或null，返回null
        }
        // 检查输入是否以0x或0X开头
        if (s.startsWith("0x") || s.startsWith("0X")) {
            String hexPart = s.substring(2); // 去掉前缀
            // 检查去掉前缀后的字符串是否为合法的十六进制字符串
            if (hexPart.matches("[0-9a-fA-F]+")) {
                return hexPart.toLowerCase(); // 返回小写的十六进制字符串
            } else {
                return null; // 如果去掉前缀后的字符串不合法，返回null
            }
        } else {
            return null; // 如果没有0x前缀，返回null
        }
    }
}
