package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractCanFactory extends AbstractDeviceCreator {

    Device createZlgCanFdMiniIDevice(DeviceRegisterForm deviceRegisterForm);

    Device createZlgCanFd200UDevice(DeviceRegisterForm deviceRegisterForm);

    Device createZlgCanFdDtu400UEwgrDevice(DeviceRegisterForm deviceRegisterForm);

    Device createTC1016Device(DeviceRegisterForm deviceRegisterForm);

    Device createTC1026Device(DeviceRegisterForm deviceRegisterForm);

    Device createTC1034Device(DeviceRegisterForm deviceRegisterForm);

    Device createNICan8502Device(DeviceRegisterForm deviceRegisterForm);

    Device createVectorCANoeDevice(DeviceRegisterForm deviceRegisterForm);

    Device createTC1013Device(DeviceRegisterForm deviceRegisterForm);

}
