package com.desaysv.workserver.devices.bus.base.lin;

import com.desaysv.workserver.config.lin.LinConfig;
import com.desaysv.workserver.config.lin.LinConfigParameter;
import com.desaysv.workserver.config.lin.NetLinConfigParameter;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.interfaces.ILinSequence;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 支持动作序列的Lin总线
 */
public abstract class SequenceableLinBus extends LinBus implements ILinSequence {
    Logger log = LogManager.getLogger(SequenceableLinBus.class.getSimpleName());

    public static final int READ_TIMEOUT = 5000;

    public SequenceableLinBus() {
    }

    public SequenceableLinBus(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    /**
     * 写入配置文件
     *
     * @param linConfigParameter
     * @return
     */
    protected LinConfig writeConfig(LinConfigParameter linConfigParameter) {
        // 写入配置文件
        LinConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
        Map<String, LinConfigParameter> configParameters = deviceConfig.getConfigParameters();

        int channel = linConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= getMaxChannelCount(); i++) {
                LinConfigParameter clonedParam = SerializationUtils.clone(linConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(linConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    @Override
    public String getDeviceModel() {
        return null;
    }

    /**
     * 写入配置文件
     *
     * @param netLinConfigParameter
     * @return
     */
    protected LinConfig writeConfig(NetLinConfigParameter netLinConfigParameter) {
        // 写入配置文件
        LinConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);

        Map<String, NetLinConfigParameter> configParameters = deviceConfig.getConfigNetParameters();

        int channel = netLinConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= 4; i++) {
                NetLinConfigParameter clonedParam = SerializationUtils.clone(netLinConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(netLinConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    public abstract int getMaxChannelCount();

    @Override
    public boolean setLinPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        return false;
    }

    @Override
    public String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError {
        return null;
    }

    @Override
    public boolean fetchLinMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return false;
    }

    @Override
    public boolean lastCheckLinMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        return false;
    }

    public static void main(String[] args) {

    }
}
