package com.desaysv.workserver.devices.power.base;

import com.desaysv.workserver.MonitorType;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.exceptions.device.DeviceDataDecodeException;
import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.listeners.PortEventListener;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.utils.StrUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 18:17
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Slf4j
public class PowerPortEventListener extends PortEventListener {
    private final PowerDevice power;

    private DeviceDataDispatcher<PowerData> deviceDataDispatcher;

    private final int dataLength;

    @Getter
    private PowerData powerData;

    public PowerPortEventListener(PowerDevice power) {
        this.power = power;
        this.dataLength = -1;
    }

    public PowerPortEventListener(PowerDevice power, DeviceDataDispatcher<PowerData> deviceDataDispatcher, int dataLength) {
        this.power = power;
        this.deviceDataDispatcher = deviceDataDispatcher;
        this.dataLength = dataLength;
    }

    @Override
    public void serialEvent(SerialPortEvent serialPortEvent) {
        super.serialEvent(serialPortEvent);
        if (serialPortEvent.getEventType() == SerialPort.LISTENING_EVENT_DATA_AVAILABLE) { // 1 串口存在可用数据
            byte[] data;
            try {
                if (dataLength <= 0) {
                    data = SerialUtils.readFromPort(power.getSerialPort(), '\n');
                } else {
                    data = SerialUtils.readFromPort(power.getSerialPort(), dataLength);    //读取数据，存入字节数组
                }
                try {
                    log.info("电源数据返回:{}", StrUtils.getHexStringWithBlank(data));
                    power.getPowerProtocol().decodeData(data, powerData -> {
                        this.powerData = powerData;
                        deviceDataDispatcher.product(MonitorType.POWER_DATA, powerData);
                    });
                } catch (DeviceDataDecodeException e) {
                    log.warn(e.getMessage(), e);
                    if (powerData != null) {
                        powerData.clear();
                    }
                }
            } catch (SerialExceptions.ReadDataFromSerialPortFailure e) {
                log.error(e.getMessage(), e);
                //TODO: 检测端口拔插事件 WM_DEVICECHANGE，改用JSerialComm，https://blog.csdn.net/qq_36666559/article/details/122027164
            }
        }
    }
}
