package com.desaysv.workserver.devices.electronic_load.base;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class ElectronicLoadDevice extends PortDevice {

    public ElectronicLoadDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ELECTRONIC_LOAD;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.ElectronicLoad.N68000;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        return super.open();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return super.close();
    }

}
