package com.desaysv.workserver.devices.serial;

import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.listeners.PortEventListener;
import com.desaysv.workserver.monitor.data.DataDistributor;
import com.desaysv.workserver.utils.ByteUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortEvent;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-10 17:14
 * @description :
 * @modified By :
 * @since : 2022-6-10
 */
@Slf4j
public class UsbSerialPortEventListener extends PortEventListener {
    private final SerialPort serialPort;
    private String remainedString = "";

    private final List<DataDistributor<String>> oneLineDistributors;

    private final List<DataDistributor<byte[]>> byteDistributors;

    public interface DataListener {
        void readBytes(byte[] byteArray);
    }

    @Setter
    private DataListener dataListener;

    public void addByteDistributor(DataDistributor<byte[]> dataDistributor) {
        byteDistributors.add(dataDistributor);
    }

    //TODO：迁移到上层manager
    public void startDataConsumer(DataDistributor<String> dataDistributor) {
        oneLineDistributors.add(dataDistributor);
        dataDistributor.start();
    }

    public void pauseDataConsumer(DataDistributor<String> dataDistributor) {
        dataDistributor.pause();
    }

    public void resumeDataConsumer(DataDistributor<String> dataDistributor) {
        dataDistributor.resume();
    }

    public void stopDataConsumer(DataDistributor<String> dataDistributor) {
        dataDistributor.stop();
        oneLineDistributors.remove(dataDistributor);
    }

    public UsbSerialPortEventListener(SerialPort serialPort) {
        this.serialPort = serialPort;
        byteDistributors = new ArrayList<>();
        oneLineDistributors = new ArrayList<>();
    }

    public void clear() {
        byteDistributors.clear();
        oneLineDistributors.clear();
    }


    public void selfSendAndReceive(byte[] data) {
        dispatchData(data);
    }

    /**
     * 串口数据模拟
     */
    private void startSimulatedDataSourceThread() {
        new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                String data = "5A 02 0F 0A 02 00 01 EA 4F 06 D2 00 00 19 A2 5A 02 0F 0A 02 00 01 EA 4F 06 C4 00 00 03 7E 5A 02 0F 0A 02 00 01 EA 4F 46 B4 00 00 01 AC 5A 02 0F 0A 02 00 01 EA 4F 86 A4 00 00 02 DD 5A 02 0F 0A 02 00 01 EA 4F C6 94 00 00 01 0C";
                dispatchData(ByteUtils.hexStringToByteArray(data));
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }).start();
    }

    /**
     * 产生byte数组
     *
     * @param byteArray byte数组
     */
    private void productBytes(byte[] byteArray) {
//        log.info("productBytes:" + Arrays.toString(byteArray));
        for (DataDistributor<byte[]> dataDistributor : byteDistributors) {
            dataDistributor.accept(byteArray);
        }
    }

    /**
     * 产生新行
     *
     * @param data 新行数据
     */
    private void productNewLine(String data) {
//        log.info("productNewLine:" + data);
        String newLineNotation = System.lineSeparator();
        data = remainedString + data;
        String[] stringList = data.split(newLineNotation);
        if (data.endsWith(newLineNotation)) {
            //换行符结尾
            for (String line : stringList) {
//                log.info("{}->{}", line, oneLineConsumers);
                for (DataDistributor<String> dataDistributor : oneLineDistributors) {
                    dataDistributor.accept(line);
                }
            }
        } else {
            remainedString = stringList[stringList.length - 1]; //最后剩余字符串
            for (int i = 0; i < stringList.length - 1; i++) {
                String line = stringList[i];
//                log.info("{}->{}", line, oneLineConsumers);
                for (DataDistributor<String> dataDistributor : oneLineDistributors) {
                    dataDistributor.accept(line);
                }
            }
        }
    }

    /**
     * 分发串口数据
     *
     * @param byteArray 字节数据
     */
    private void dispatchData(byte[] byteArray) {
        //发送给客户端
        if (dataListener != null) {
            dataListener.readBytes(byteArray);
        }
        //分发byte
        productBytes(byteArray);
        //分发String
        productNewLine(new String(byteArray));
    }


    @Override
    public void serialEvent(SerialPortEvent serialPortEvent) {
        super.serialEvent(serialPortEvent);
        if (serialPortEvent.getEventType() == SerialPort.LISTENING_EVENT_DATA_AVAILABLE) { // 1 串口存在可用数据
            try {
                TimeUnit.MILLISECONDS.sleep(15);
                //读取数据，存入字节数组
                byte[] byteArray = SerialUtils.readFromPort(serialPort);
                dispatchData(byteArray);
            } catch (SerialExceptions.ReadDataFromSerialPortFailure | InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public static void main(String[] args) {
        String data = "5A 02 0F 0A 02 00 01 EA 4F 06 D2 00 00 19 A2 5A 02 0F 0A 02 00 01 EA 4F 06 C4 00 00 03 7E 5A 02 0F 0A 02 00 01 EA 4F 46 B4 00 00 01 AC 5A 02 0F 0A 02 00 01 EA 4F 86 A4 00 00 02 DD 5A 02 0F 0A 02 00 01 EA 4F C6 94 00 00 01 0C";
        System.out.println(Arrays.toString(ByteUtils.hexStringToByteArray(data)));
    }
}
