package com.desaysv.workserver.screen.distributor;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.common.port.CheckPointInfo;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.monitor.data.DataDistributor;
import com.desaysv.workserver.screen.ScreenService;
import com.desaysv.workserver.screen.TouchDataListener;
import com.desaysv.workserver.screen.config.DisplayConfig;
import com.desaysv.workserver.screen.config.GestureCode;
import com.desaysv.workserver.screen.config.ScreenConfig;
import com.desaysv.workserver.screen.consumers.EventBasedTouchPointDataConsumer;
import com.desaysv.workserver.screen.entity.PointManager;
import com.desaysv.workserver.screen.entity.PointUtils;
import com.desaysv.workserver.screen.entity.PreciousPoint2D;
import com.desaysv.workserver.screen.entity.TouchPoint;
import com.desaysv.workserver.screen.events.ClickEvent;
import com.desaysv.workserver.screen.events.LineTouchEvent;
import com.desaysv.workserver.screen.events.TouchEvent;
import com.desaysv.workserver.screen.result.TouchPointSummary;
import com.desaysv.workserver.screen.result.mist.BackTracePoint;
import com.desaysv.workserver.screen.result.mist.PointInfo;
import com.desaysv.workserver.screen.result.mist.TestPoint;
import com.desaysv.workserver.screen.result.report.GestureReport;
import com.desaysv.workserver.screen.result.report.ScreenLineSummary;
import com.desaysv.workserver.screen.result.report.ScreenPointSummary;
import com.desaysv.workserver.screen.result.report.TouchReport;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

import static com.desaysv.workserver.screen.TouchPointUtils.*;

/**
 * 串口报点分发线程
 */
@Slf4j
public class EventBasedTouchPointDistributor extends DataDistributor<byte[]> implements TouchDataListener {

    private final EventBasedTouchPointDataConsumer eventBasedTouchPointDataConsumer;

    private final List<TouchPoint> listenedTouchDataList;

    private final ScreenService screenService;
    private final TouchPointSummary touchPointSummary;

    public EventBasedTouchPointDistributor(EventBasedTouchPointDataConsumer consumer, ScreenService screenService) {
        super(consumer);
        this.screenService = screenService;
        this.eventBasedTouchPointDataConsumer = consumer;
        listenedTouchDataList = new ArrayList<>();
        touchPointSummary = new TouchPointSummary();
    }

    @Override
    public void notifyTouchDataListenerStart() {
        //报点收集开始
        log.info("触摸报点收集开始");
        // 清空监听数据
        listenedTouchDataList.clear();
        //清空消费者数据缓存
        eventBasedTouchPointDataConsumer.startCollect();
    }

    @Override
    public List<PointInfo> notifyTouchDataListenerEnd() {
        //报点收集结束
        log.info("触摸报点收集结束");
        List<byte[]> collectData = eventBasedTouchPointDataConsumer.endCollect();
        for (byte[] array : collectData) {
            log.info("收集到的报点数组:{}", ByteUtils.byteArrayToHexString(array));
        }
        ScreenConfig screenConfig = screenService.getScreenConfig();
        List<TouchPoint> touchPointList = parseByteArrayToTouchPoints(listToByteArray(collectData),
                screenConfig.getTouchPointMatrix(), true).getTouchPointList();
        listenedTouchDataList.addAll(touchPointList);
        log.info("监听到的报点数组:{}", listenedTouchDataList);
        List<PointInfo> pointList = new ArrayList<>();
        for (TouchPoint touchPoint : touchPointList) {
            PointInt pointFromScreen = parseTouchPointToPoint(touchPoint, screenConfig.getTouchPointMatrix());
            pointList.add(new PointInfo(pointFromScreen, touchPoint));
        }
        return pointList;
    }

    @Override
    public OperationResult notifyTouchDataListenerEnd(TouchEvent touchEvent) {
        List<PointInfo> pointList = notifyTouchDataListenerEnd();
        return checkTouchPoint(touchEvent, pointList, screenService.getScreenConfig());
    }

    @Override
    public OperationResult notifyTouchDataListenerEnd(CheckPointInfo checkPointInfo) {
        boolean enableCoordinate = checkPointInfo.isEnableCoordinate();
        List<PointInfo> pointList = notifyTouchDataListenerEnd();
        return checkTouchPoint(checkPointInfo, pointList);
    }

    @Override
    public void notifyTouchDataReportStart() {
        touchPointSummary.clear();
    }

    @Override
    public com.desaysv.workserver.screen.result.TouchPointSummary notifyTouchDataReportEnd() {
        log.info("本次测试收集到触摸点:{}个", touchPointSummary.getTouchPointSize());
        return touchPointSummary;
    }

    /**
     * byte[] list转化成byte[]
     *
     * @param byteList byte[] list
     * @return byte[]
     */
    private byte[] listToByteArray(List<byte[]> byteList) {
        int size = 0;
        for (byte[] bytes : byteList) {
            size += bytes.length;
        }
        byte[] byteArray = new byte[size];
        int index = 0;
        for (byte[] bytes : byteList) {
            System.arraycopy(bytes, 0, byteArray, index, bytes.length);
            index += bytes.length;
        }
        return byteArray;
    }


    /**
     * 转换机械臂坐标到屏幕坐标
     *
     * @param robotPoint 机械臂坐标到
     * @return 屏幕坐标
     */
    private PointInt convertRobotToDisplayCoordinateSystem(PreciousPoint2D robotPoint) {
        ScreenConfig screenConfig = screenService.getScreenConfig();
        DisplayConfig displayConfig = screenConfig.getDisplay();
        PreciousPoint2D leftBottom = displayConfig.getLeftBottom().toPreciousPoint2D();
        PreciousPoint2D leftTop = displayConfig.getLeftTop().toPreciousPoint2D();
        PreciousPoint2D rightTop = displayConfig.getRightTop().toPreciousPoint2D();
        PreciousPoint2D robotScreenPoint = PointManager.calculateScreenPointByAnyRobotCoordinatePoint(robotPoint, leftBottom, leftTop, rightTop);
        BigDecimal screenWidth = rightTop.getY().subtract(leftTop.getY()).abs();
        BigDecimal screenHeight = leftBottom.getX().subtract(leftTop.getX()).abs();
        int resolutionWidth = screenConfig.getDisplay().getScreen().getWidth();
        int resolutionHeight = screenConfig.getDisplay().getScreen().getHeight();
        //TODO:需要判断是否为0，或者考虑修改传入值的方式2024年4月24日
        return PointManager.getResolutionCoordinate(
                resolutionWidth,
                resolutionHeight,
                screenWidth,
                screenHeight,
                robotScreenPoint);
    }


    /**
     * 检查单点触摸
     *
     * @param clickEvent   点击事件
     * @param pointList    屏幕报点坐标列表
     * @param screenConfig 屏幕配置
     * @return 操作结果
     */
    private OperationResult checkSinglePoint(ClickEvent clickEvent, List<PointInfo> pointList, ScreenConfig screenConfig) {
        OperationResult operationResult = new OperationResult();
        TouchReport touchReport = new TouchReport();
        List<String> originalDataList = new ArrayList<>();
        for (PointInfo pointInfo : pointList) {
            originalDataList.add(pointInfo.getTouchPoint().toByteString());
        }
        touchReport.setOriginalDataList(originalDataList);
        touchReport.setTouchType(TouchReport.TouchType.POINT);
        touchReport.setReportName("坐标" + clickEvent.getPointName());
        touchReport.setReportQuantity(pointList.size());

        //计算机械臂实际坐标
        PreciousPoint2D robotPoint = clickEvent.getPoint().toPreciousPoint2D();
        //计算机械臂像素坐标
        PointInt pixelPointFromRobot = convertRobotToDisplayCoordinateSystem(robotPoint);

        SseUtils.pubMsg(SseConstants.ROBOT_TOUCH_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(pixelPointFromRobot));

        //记录到测试点
        TestPoint testPoint = new TestPoint();
        testPoint.setRobotPoint(robotPoint.toPointInt());
        testPoint.setPixelPoint(pixelPointFromRobot);

        //校验单点坐标
        List<ScreenPointSummary> screenPointSummaryList = new ArrayList<>();
        if (pointList.isEmpty()) {
            touchReport.setOk(false);

            //记录详情
            ScreenPointSummary screenPointSummary = new ScreenPointSummary();
            screenPointSummary.setOk(false);
            screenPointSummary.setDescription(operationResult.getMessage());
            //记录测试点
            screenPointSummary.setTestPoint(testPoint);
            screenPointSummary.setDescription("串口未反馈报点");

            screenPointSummaryList.add(screenPointSummary);
            touchReport.setTouchSummaryList(screenPointSummaryList);

            touchReport.setReportSummary(screenPointSummary.getDescription());
        } else {
            boolean ok = true;
            //检查Gesture
            String gestureCheckInfo = "";
            if (screenConfig.getTouchPointMatrix().getGestureCode() != null) {
                GestureReport gestureReport = checkGesture(pointList, "点击");
                if (!gestureReport.isOk()) {
                    ok = false;
                    gestureCheckInfo = gestureReport.getErrorMessage();
                } else {
                    gestureCheckInfo = "触摸事件ok";
                }
            }
            int i = 0;
            for (PointInfo pointInfo : pointList) {
                if (!pointInfo.getPoint().isValid()) {
                    continue;
                }
                PointInt pointFromScreen = pointInfo.getPoint();
                log.info("检测第{}个点,串口屏幕报点打印:{}", ++i, pointFromScreen);
                log.info("机械臂像素坐标打印:{}", pixelPointFromRobot);
                double distance = pointFromScreen.compareDistance(pixelPointFromRobot);
                log.info("串口报点和机械臂报点的距离:{}", distance);

                ScreenPointSummary screenPointSummary = new ScreenPointSummary();
                //记录测试点
                screenPointSummary.setTestPoint(testPoint);

                //记录串口点
                BackTracePoint backTracePoint = new BackTracePoint();
                backTracePoint.setPoint(pointFromScreen);
                backTracePoint.setRefLog(pointInfo.getTouchPoint().toByteString());
                screenPointSummary.setReportPoint(backTracePoint);
                //发送串口点数据
                SseUtils.pubMsg(SseConstants.REPORT_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(pointFromScreen));

                //记录距离偏差
                screenPointSummary.setDistanceDeviation(distance);

                //描述原因
                int pointPixelDeviationThreshold = screenConfig.getDisplay().getScreenCheckParameters().getPointPixelDeviationThreshold();
                String coordinateName = clickEvent.getPointName() == null ? String.valueOf(clickEvent.getPoint()) :
                        "坐标(" + clickEvent.getPointName() + ")";
//                screenClickSummary.setTouchName(coordinateName);
                if (distance > pointPixelDeviationThreshold) {
                    //超过阈值范围
                    ok = false;
                    screenPointSummary.setOk(false);
                    screenPointSummary.setDescription(String.format("%s偏差%.2f像素，超过设定阈值%d", coordinateName, distance, pointPixelDeviationThreshold));
                } else {
                    screenPointSummary.setOk(true);
                    screenPointSummary.setDescription(String.format("%s偏差%.2f像素，符合设定阈值%d", coordinateName, distance, pointPixelDeviationThreshold));
                }
                screenPointSummaryList.add(screenPointSummary);
            }
            touchReport.setOk(ok);
            touchReport.setTouchSummaryList(screenPointSummaryList);
            touchReport.setReportSummary(gestureCheckInfo + "\n" + touchReport.getSummaryReport("", true));
        }
        operationResult.setOk(touchReport.isOk());
        operationResult.setMessage(touchReport.getReportSummary());
        operationResult.setData(touchReport);
        return operationResult;
    }

    private OperationResult checkSinglePoint(CheckPointInfo checkPointInfo, List<PointInfo> pointList) {
        ScreenConfig screenConfig = checkPointInfo.getScreenConfig();
        String pointType = checkPointInfo.getTouchType();
        int fingers = checkPointInfo.getFingers();
        OperationResult operationResult = new OperationResult();
        TouchReport touchReport = new TouchReport();
        List<String> originalDataList = new ArrayList<>();
        for (PointInfo pointInfo : pointList) {
            originalDataList.add(pointInfo.getTouchPoint().toByteString());
        }
        touchReport.setOriginalDataList(originalDataList);
        touchReport.setTouchType(TouchReport.TouchType.POINT);
        touchReport.setReportQuantity(pointList.size());

        //校验单点坐标
        List<ScreenPointSummary> screenPointSummaryList = new ArrayList<>();
        if (pointList.isEmpty()) {
            touchReport.setOk(false);
            //记录详情
            ScreenPointSummary screenPointSummary = new ScreenPointSummary();
            screenPointSummary.setOk(false);
            screenPointSummary.setDescription(operationResult.getMessage());
            //记录测试点
            screenPointSummary.setDescription("串口未反馈报点");
            screenPointSummaryList.add(screenPointSummary);
            touchReport.setTouchSummaryList(screenPointSummaryList);
            touchReport.setReportSummary(screenPointSummary.getDescription());
        } else {
            boolean ok = true;
            String gestureCheckInfo = "";
            if (screenConfig.getTouchPointMatrix().getGestureCode() != null) {
                if (GestureCode.PRESS.equals(pointType)) {
                    pointType = "按下";
                } else {
                    pointType = "释放";
                }
                boolean ignore = screenConfig.getTouchPointMatrix().isIgnoreMiddle();
                GestureReport gestureReport = checkSingleGesture(pointList, pointType, fingers, ignore);
                if (!gestureReport.isOk()) {
                    ok = false;
                    gestureCheckInfo = gestureReport.getErrorMessage();
                } else {
                    gestureCheckInfo = "事件ok";
                }
            }
            int i = 0;
            for (PointInfo pointInfo : pointList) {
                if (!pointInfo.getPoint().isValid()) {
                    continue;
                }
                PointInt pointFromScreen = pointInfo.getPoint();
                log.info("检测第{}个点,串口屏幕报点打印:{}", ++i, pointFromScreen);
                ScreenPointSummary screenPointSummary = new ScreenPointSummary();
                //记录串口点
                BackTracePoint backTracePoint = new BackTracePoint();
                backTracePoint.setPoint(pointFromScreen);
                backTracePoint.setRefLog(pointInfo.getTouchPoint().toByteString());
                screenPointSummary.setReportPoint(backTracePoint);
                //发送串口点数据到显示界面
                SseUtils.pubMsg(SseConstants.REPORT_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(pointFromScreen));
                screenPointSummaryList.add(screenPointSummary);
            }
            touchReport.setOk(ok);
            touchReport.setTouchSummaryList(screenPointSummaryList);
            touchReport.setReportSummary(gestureCheckInfo + "\n" + touchReport.getSummaryReport("", true));
        }
        operationResult.setOk(touchReport.isOk());
        operationResult.setMessage(touchReport.getReportSummary());
        operationResult.setData(touchReport);
        return operationResult;
    }

    /**
     * 检测线段报点
     *
     * @param lineEvent 直线触摸事件
     * @param pointList 显示屏串口报点列表
     * @return 操作结果
     */
    private OperationResult checkLinePoints(LineTouchEvent lineEvent, List<PointInfo> pointList, ScreenConfig screenConfig) {
        OperationResult operationResult = new OperationResult();
        TouchReport touchReport = new TouchReport();
        List<String> originalDataList = new ArrayList<>();
        for (PointInfo pointInfo : pointList) {
            originalDataList.add(pointInfo.getTouchPoint().toByteString());
        }
        touchReport.setOriginalDataList(originalDataList);
        touchReport.setTouchType(TouchReport.TouchType.LINE);
        touchReport.setReportName(String.format("直线%s->%s", lineEvent.getStartPointName(), lineEvent.getEndPointName()));
        touchReport.setReportQuantity(pointList.size());
        if (pointList.size() <= 1) {
            //报点数量不足，不继续校验
            touchReport.setOk(false);
            touchReport.setReportSummary(String.format("报点数量不足，共采集%d个", pointList.size()));
        } else {
            touchReport.setOk(true);
            String gestureCheckInfo = "";
            if (screenConfig.getTouchPointMatrix().getGestureCode() != null) {
                GestureReport gestureReport = checkGesture(pointList, pointList.size() - 2, "滑动");
                if (!gestureReport.isOk()) {
                    touchReport.setOk(false);
                    gestureCheckInfo = gestureReport.getErrorMessage();
                } else {
                    gestureCheckInfo = "触摸事件ok";
                }
            }
            List<ScreenLineSummary> touchSummaryList = new ArrayList<>();
            StringBuilder summaryBuilder = new StringBuilder();
            //报点数量超过一个，进行校验
            if (screenConfig.getDisplay().getScreenCheckParameters().isCheckPointReportRate()) {
                //检查报点率
                Long motionDuration = lineEvent.getTouchDuration();
                if (motionDuration != null) {
                    int upperReportRate = screenConfig.getDisplay().getScreenCheckParameters().getUpperLimitPointReportRate();
                    int lowerReportRate = screenConfig.getDisplay().getScreenCheckParameters().getLowerLimitPointReportRate();

                    int upperPointNumber = (int) (upperReportRate * motionDuration / 1000);
                    int lowerPointNumber = (int) (lowerReportRate * motionDuration / 1000);
                    touchReport.setUpperReportQuantityLimit(upperPointNumber);
                    touchReport.setLowerReportQuantityLimit(lowerPointNumber);
                    int actualPointNumber = pointList.size();
                    if (actualPointNumber < lowerPointNumber || actualPointNumber > upperPointNumber) {
                        //不符合报点数量范围
                        touchReport.setOk(false);
                        summaryBuilder.append(String.format("实际报点数量%d个,不符合报点率要求(%d~%d个)", actualPointNumber, lowerPointNumber, upperPointNumber)).append("\n");
                    }
                } else {
                    touchReport.setOk(false);
                    summaryBuilder.append("直线运动时间无法获取，无法计算报点率").append("\n");
                }
            }

            //计算起始点
            PreciousPoint2D startRobotPoint = lineEvent.getStartPoint().toPreciousPoint2D();
            PreciousPoint2D endRobotPoint = lineEvent.getEndPoint().toPreciousPoint2D();
            PointInt startPoint = convertRobotToDisplayCoordinateSystem(startRobotPoint);
            PointInt endPoint = convertRobotToDisplayCoordinateSystem(endRobotPoint);
            //发送机械臂滑动到前端
            SseUtils.pubMsg(SseConstants.ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(startPoint));
            SseUtils.pubMsg(SseConstants.ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(endPoint));
            log.info("线段起点:{}, 线段终点:{}", startPoint, endPoint);
            log.info("报点列表:{}", pointList);

            //检测首末两点偏移
            PointInfo pStart = pointList.get(0);
            PointInfo pEnd = pointList.get(pointList.size() - 1);
            double distanceStart = startPoint.compareDistance(pStart.getPoint());
            double distanceEnd = endPoint.compareDistance(pEnd.getPoint());
            //发送显示屏数据到前端
            SseUtils.pubMsg(SseConstants.REPORT_SWIPE_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(pStart.getPoint()));
            SseUtils.pubMsg(SseConstants.REPORT_SWIPE_COORDINATE_SUBSCRIBE_ID, JSON.toJSONString(pEnd.getPoint()));
//            System.out.println("distanceStart:" + distanceStart);
//            System.out.println("distanceEnd:" + distanceEnd);

            int distanceThreshold = screenConfig.getDisplay().getScreenCheckParameters().getPointPixelDeviationThreshold();
            ScreenLineSummary startPointSummary = new ScreenLineSummary();
            //设置起点TestPoint
            TestPoint startTestPoint = new TestPoint();
            startTestPoint.setRobotPoint(startRobotPoint.toPointInt());
            startTestPoint.setPixelPoint(startPoint);
            startPointSummary.setTestPoint(startTestPoint);
            //设置起点BackTracePoint
            BackTracePoint startReportPoint = new BackTracePoint();
            startReportPoint.setPoint(pStart.getPoint());
            startReportPoint.setRefLog(pStart.getTouchPoint().toByteString());
            startPointSummary.setReportPoint(startReportPoint);
            //设置起点偏差
            startPointSummary.setDistanceDeviation(distanceStart);
            //设置起点描述
            String startCoordinateName = lineEvent.getStartPointName() == null ? String.valueOf(lineEvent.getStartPoint()) :
                    "起点(" + lineEvent.getStartPointName() + ")";
            if (distanceStart > distanceThreshold) {
                //偏差大于像素距离阈值
                startPointSummary.setOk(false);
                touchReport.setOk(false);
                startPointSummary.setDescription(String.format("%s偏差%.2f像素，超过阈值%d",
                        startCoordinateName, distanceStart, distanceThreshold));
            } else {
                startPointSummary.setOk(true);
                startPointSummary.setDescription(String.format("%s偏差%.2f像素，符合阈值%d",
                        startCoordinateName, distanceStart, distanceThreshold));
            }

            ScreenLineSummary endPointSummary = new ScreenLineSummary();
            //设置终点TestPoint
            TestPoint endTestPoint = new TestPoint();
            endTestPoint.setRobotPoint(endRobotPoint.toPointInt());
            endTestPoint.setPixelPoint(endPoint);
            endPointSummary.setTestPoint(endTestPoint);
            //设置起点BackTracePoint
            BackTracePoint endReportPoint = new BackTracePoint();
            endReportPoint.setPoint(pEnd.getPoint());
            endReportPoint.setRefLog(pEnd.getTouchPoint().toByteString());
            endPointSummary.setReportPoint(endReportPoint);
            //设置终点偏差
            endPointSummary.setDistanceDeviation(distanceEnd);
            //设置终点描述
            String endCoordinateName = lineEvent.getEndPointName() == null ? String.valueOf(lineEvent.getEndPoint()) :
                    "终点(" + lineEvent.getEndPointName() + ")";
            if (distanceEnd > distanceThreshold) {
                //偏差大于像素距离阈值
                endPointSummary.setOk(false);
                touchReport.setOk(false);
                endPointSummary.setDescription(String.format("%s偏差%.2f像素，超过阈值%d",
                        endCoordinateName, distanceEnd, distanceThreshold));
            } else {
                endPointSummary.setOk(true);
                endPointSummary.setDescription(String.format("%s偏差%.2f像素，符合阈值%d",
                        endCoordinateName, distanceEnd, distanceThreshold));
            }
            touchSummaryList.add(startPointSummary);
            touchSummaryList.add(endPointSummary);

            //检测线性度
            double linearPercentThreshold = screenConfig.getDisplay().getScreenCheckParameters().getLinearPercentDeviationThreshold();
            double linearRatio = Math.abs(PointUtils.getLinearRatio(pointList.stream().map(PointInfo::getPoint).toArray(PointInt[]::new)));
            touchReport.setLinearRatio(linearRatio);
            double percentLinearDeviation = 1 - linearRatio;
            if (percentLinearDeviation > linearPercentThreshold) {
                //大于线性度百分阈值
                NumberFormat numberFormat = NumberFormat.getPercentInstance();
                numberFormat.setMinimumFractionDigits(2);
                String desc = String.format("直线%s->%s线性度偏差%s,超过阈值%s", pStart.getPoint(), pEnd.getPoint(),
                        numberFormat.format(percentLinearDeviation), numberFormat.format(linearPercentThreshold));
                summaryBuilder.append(desc);
                for (PointInfo pointInfo : pointList) {
                    double pointLinearDeviationRatio = Math.abs(PointUtils.getLinearRatio(pStart.getPoint(), pointInfo.getPoint(), pEnd.getPoint()));
                    double percentPointLinearDeviationRatio = 1 - pointLinearDeviationRatio;
                    if (percentPointLinearDeviationRatio > linearPercentThreshold) {
                        //小于直线的线性度
                        ScreenLineSummary screenLineSummary = new ScreenLineSummary();
                        screenLineSummary.setOk(false);
                        BackTracePoint reportPoint = new BackTracePoint();
                        reportPoint.setPoint(pointInfo.getPoint());
                        reportPoint.setRefLog(pointInfo.getTouchPoint().toByteString());
                        screenLineSummary.setLinearDeviation(pointLinearDeviationRatio);
                        desc = String.format("%s偏差%s,超过阈值%s", pointInfo.getPoint(),
                                numberFormat.format(percentPointLinearDeviationRatio), numberFormat.format(linearPercentThreshold));
                        screenLineSummary.setDescription(desc);
                        screenLineSummary.setReportPoint(reportPoint);
                        touchSummaryList.add(screenLineSummary);
                    }
                }
                touchReport.setOk(false);
            }
//            for (int i = 1; i < pointList.size(); i++) {
//                PointInfo pointNext = pointList.get(i);
//                double angle = pointNext.getPoint().toIntersectionAngle(pStart.getPoint(), pEnd.getPoint());
////                double angle = Math.abs(pStart.getPoint().toAngle(pointNext.getPoint()) - targetAngle);
//                if (angle > angleThreshold) {
//                    //超过角度范围
//                    ScreenLineSummary screenLineSummary = new ScreenLineSummary();
//                    BackTracePoint reportPoint = new BackTracePoint();
//                    reportPoint.setPoint(pointNext.getPoint());
//                    reportPoint.setRefLog(pointNext.getTouchPoint().toByteString());
//                    screenLineSummary.setReportPoint(reportPoint);
//                    screenLineSummary.setAngleDeviation(angle);
//                    screenLineSummary.setOk(false);
//                    screenLineSummary.setDescription(String.format("%s角度%f,超过阈值%d", pointNext.getPoint(), angle, angleThreshold));
//                    touchReport.setOk(false);
//                    touchSummaryList.add(screenLineSummary);
//                }
//            }
            touchReport.setTouchSummaryList(touchSummaryList);
            touchReport.setReportSummary(gestureCheckInfo + "\n" + touchReport.getSummaryReport(summaryBuilder.toString(), true));

        }
        operationResult.setOk(touchReport.isOk());
        operationResult.setMessage(touchReport.getReportSummary());
        operationResult.setData(touchReport);
        return operationResult;
    }

    /**
     * 检查报点
     *
     * @param touchEvent   触摸事件
     * @param pointList    显示屏串口报点列表
     * @param screenConfig 显示屏配置
     * @return 操作结果
     */
    private OperationResult checkTouchPoint(TouchEvent touchEvent, List<PointInfo> pointList, ScreenConfig screenConfig) {
        touchPointSummary.addTouchPointSize(pointList.size());
        log.info("检测到报点数量{}个,校验:{}", pointList.size(), touchEvent);
        if (touchEvent instanceof ClickEvent) { //点击
            //校验点击坐标
            return checkSinglePoint((ClickEvent) touchEvent, pointList, screenConfig);
        } else if (touchEvent instanceof LineTouchEvent) { //直线
            //校验直线坐标
            return checkLinePoints((LineTouchEvent) touchEvent, pointList, screenConfig);
        }
        return OperationResult.staticFail("触摸事件未定义");
    }

    private OperationResult checkTouchPoint(CheckPointInfo checkPointInfo, List<PointInfo> pointList) {
        touchPointSummary.addTouchPointSize(pointList.size());
        log.info("检测到报点数量{}个", pointList.size());

        return checkSinglePoint(checkPointInfo, pointList);
    }

    public static void main(String[] args) {

//        byte[] b = ByteUtils.hexStringToByteArray("01 02 03 04");
//        List<Integer> list = TouchPointUtils.byteArrayToIntList(b);
//        list = list.subList(list.size() - 3, list.size());
//        System.out.println(list);
//        try {
//            BufferedReader reader = new BufferedReader(
//                    new FileReader("D:\\uidp4666\\Desktop\\ADS\\数据\\测试数据2-多个.txt"));
//            StringBuilder sb = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                sb.append(line);
//            }
//            String content = sb.toString();
////            System.out.println(content);
//            byte[] arrays = ByteUtils.hexStringToByteArray(content);
//            System.out.println("初始数组:" + ByteUtils.byteArrayToHexString(arrays));
//            List<TouchPoint> touchPointList = parseTouchPoints(arrays);
//            System.out.println("touchPointList:" + touchPointList);
//            reader.close();
//        } catch (IOException e) {
//            log.error(e.getMessage(), e);
//        }

    }
}
