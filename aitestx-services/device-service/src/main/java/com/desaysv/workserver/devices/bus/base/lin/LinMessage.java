package com.desaysv.workserver.devices.bus.base.lin;

import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.reader.ObjectReader;
import com.desaysv.workserver.entity.BaseMessage;
import com.desaysv.workserver.utils.ByteUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Base64;

/**
 * LIN报文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LinMessage extends BaseMessage {
    private float timestamp; // 时间戳
    private String idHex; // 报文id
    private Integer channel; // 通道1开始，不能直接引用
    private String dir;// 流向 TX发送方向 RX接收方向
    private int length; // 数据长度
    @JSONField(deserializeUsing = HexStringToByteArrayReader.class)
    private byte[] data; // 报文数据
    private Float duration; //秒
    private float period; //秒
    private int sendTimes = -1; // 发送次数,默认一直发送
    private int framesPerSendNum = 1; //每次发送帧数
    @JsonIgnore
    private String status;//状态

    // 自定义的反序列化器
    public static class HexStringToByteArrayReader implements ObjectReader<byte[]> {
        @Override
        public byte[] readObject(com.alibaba.fastjson2.JSONReader jsonReader, java.lang.reflect.Type fieldType, Object fieldName, long features) {
            String dataString = jsonReader.readString();

            // 判断数据是否为 Base64 格式(兼容旧版本)
            if (dataString.matches("^[A-Za-z0-9+/]+={0,2}$\n") && dataString.length() % 4 == 0) {
                try {
                    return Base64.getDecoder().decode(dataString);
                } catch (IllegalArgumentException e) {
                    System.err.println("Base64 解码失败: " + dataString);
                    return new byte[0];
                }
            } else {
                try {
                    return ByteUtils.hexStringToByteArray(dataString);
                } catch (IllegalArgumentException e) {
                    System.err.println("Hex 解码失败: " + dataString);
                    return new byte[0];
                }
            }
        }
    }

    public void setDuration(Float duration) {
        if (duration == null || duration < 0) {
            this.duration = null;
        } else {
            this.duration = duration;
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("Timestamp: %15.6f ", timestamp));
        sb.append(getIdHex());
        for (int i = 0; i < Math.min(length, data.length); i++) {
            sb.append(" ").append(String.format("%02X", data[i]));
        }
        sb.append(String.format(" Channel: %d", channel));
        sb.append(String.format(" Period:%.0fms", period * 1000));
        return sb.toString();
    }
}
