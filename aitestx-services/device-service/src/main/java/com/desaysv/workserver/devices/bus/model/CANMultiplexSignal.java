package com.desaysv.workserver.devices.bus.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

@Data
@AllArgsConstructor
public class CANMultiplexSignal {
    /**
     * UUID(父类用于关联)
     */
    private String CANMessageID;

    private CANSignal mainSignal;
    private boolean loop;

    private HashMap<Long, List<CANSignal>> muxGroups;

    public CANMultiplexSignal(CANSignal mainSignal, HashMap<Long, List<CANSignal>> muxGroups) {
        this.mainSignal = mainSignal;
        this.muxGroups = muxGroups;
    }
}
