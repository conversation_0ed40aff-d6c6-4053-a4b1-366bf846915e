package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.dc_collector.ZH4424DcCollector;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractDcCollectorDeviceFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class DcCollectorDeviceFactory implements AbstractDcCollectorDeviceFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.DcCollector.DC_COLLECTOR_24_DEVICE:
                return createDcCollectorDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createDcCollectorDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZH4424DcCollector(deviceRegisterForm.getDeviceOperationParameter());
    }


}
