package com.desaysv.workserver.factory;

import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.StrUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 18:20
 * @description : 设备注册表单
 * @modified By :
 * @since : 2022-3-16
 */
@Getter
@Setter
@ToString
public class DeviceRegisterForm {

    private Integer deviceIndex;
    private String deviceType;
    private String deviceModel;
    private String deviceName;
    private Integer devicePort;
    private Integer baudRate;
    private Integer sampleRate;
    private String aliasName;
    private String deviceUniqueCode;
    private int numberChannels = 0;

    private Integer channel; //可选

    private boolean simulated;

    private DeviceOperationParameter deviceOperationParameter = new DeviceOperationParameter();

    @JsonProperty("deviceName")
    private void unpackParameter(String deviceName) {
        if (StrUtils.isEmpty(deviceName)) {
            this.deviceName = "<def>" + deviceType;
        } else {
            this.deviceName = deviceName;
        }
    }

    @JsonProperty("deviceOperationParameter")
    private void unpackParameter(Map<String, Object> params) {
//        params.put("test_key", "test_value");
        deviceOperationParameter.putAll(params);
    }
}
