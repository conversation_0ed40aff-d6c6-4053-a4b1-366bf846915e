package com.desaysv.workserver.devices.robot.base;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.model.roi.ScaledRoiRect;

public interface RobotTouchEvent {
    OperationResult beforeTouch(DobotDevice dobotDevice, ScaledRoiRect scaledRoiRect) throws FrameGrabberException;

    OperationResult afterTouch(DobotDevice dobotDevice, ScaledRoiRect scaledRoiRect) throws FrameGrabberException, OperationFailNotification;

}
