package com.desaysv.workserver.common.port;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class ConcretePortSerialDevice extends PortDevice {

    public ConcretePortSerialDevice() {
    }

    public ConcretePortSerialDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Serial.PORT_SERIAL;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SERIAL;
    }

    public static void main(String[] args) throws DeviceOpenRepeatException, DeviceOpenException, DeviceCloseException {
        PortDevice p1 = new ConcretePortSerialDevice();
        p1.setDevicePort(5);
        p1.setBaudRate(115200);
        PortDevice p2 = new ConcretePortSerialDevice();
        p2.setDevicePort(13);
        p2.setBaudRate(9600);
    }
}
