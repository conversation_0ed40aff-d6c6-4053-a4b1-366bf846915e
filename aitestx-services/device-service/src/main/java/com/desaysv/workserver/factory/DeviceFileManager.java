package com.desaysv.workserver.factory;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.devices.robot.vision.VisionGuideCalibrationData;
import com.desaysv.workserver.devices.robot.vision.VisionGuideConfig;
import com.desaysv.workserver.filemanager.FileManager;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;

import java.io.File;

public class DeviceFileManager extends FileManager {
    private volatile static DeviceFileManager appFileManager;


    public static DeviceFileManager getInstance() {
        if (appFileManager == null) {
            synchronized (DeviceFileManager.class) {
                if (appFileManager == null) {
                    appFileManager = new DeviceFileManager();
                }
            }
        }
        return appFileManager;
    }

    private static final class _DeviceFileConstants {
        public File deviceFileDBPath; //全局设备文件夹路径
        public File visionGuideFilePath; //视觉引导文件夹路径

        public File deviceConfigFilePath; //设备初始化文件夹路径
    }

    public _DeviceFileConstants deviceFileConstants;

    private DeviceFileManager() {
        initDeviceFilePaths();
    }

    private void initDeviceFilePaths() {
        deviceFileConstants = new _DeviceFileConstants();
        deviceFileConstants.deviceFileDBPath = createFolder(baseConfigPath, "devices");
        deviceFileConstants.visionGuideFilePath = createFolder(deviceFileConstants.deviceFileDBPath, "visionGuideConfig");
        deviceFileConstants.deviceConfigFilePath = createFolder(deviceFileConstants.deviceFileDBPath, "deviceConfig");
    }

    public VisionGuideConfig writeVisionGuideCalibrationData(VisionGuideCalibrationData calibrationData) {
        File jsonFile = new File(deviceFileConstants.visionGuideFilePath, "visionGuideConfig.json");
        VisionGuideConfig visionGuideConfig;
        if (jsonFile.exists()) {
            String jsonData = ThreadSafeFileUtils.readFileToString(jsonFile.getAbsolutePath());
            visionGuideConfig = JSON.parseObject(jsonData, VisionGuideConfig.class);
        } else {
            visionGuideConfig = new VisionGuideConfig();
        }
        visionGuideConfig.setVisionGuideCalibrationData(calibrationData);
        ThreadSafeFileUtils.writeFileFromString(jsonFile, JSON.toJSONString(visionGuideConfig), false);
        return visionGuideConfig;
    }

    public VisionGuideConfig writeVisionGuideZ(double visionGuideZ) {
        File jsonFile = new File(deviceFileConstants.visionGuideFilePath, "visionGuideConfig.json");
        VisionGuideConfig visionGuideConfig;
        if (jsonFile.exists()) {
            String jsonData = ThreadSafeFileUtils.readFileToString(jsonFile.getAbsolutePath());
            visionGuideConfig = JSON.parseObject(jsonData, VisionGuideConfig.class);
        } else {
            visionGuideConfig = new VisionGuideConfig();
        }
        visionGuideConfig.setVisionGuideZ(visionGuideZ);
        ThreadSafeFileUtils.writeFileFromString(jsonFile, JSON.toJSONString(visionGuideConfig), false);
        return visionGuideConfig;
    }

    public VisionGuideConfig readVisionGuideData() {
        File jsonFile = new File(deviceFileConstants.visionGuideFilePath, "visionGuideConfig.json");
        VisionGuideConfig visionGuideConfig;
        if (!jsonFile.exists()) {
            visionGuideConfig = new VisionGuideConfig();
        } else {
            String jsonData = ThreadSafeFileUtils.readFileToString(jsonFile);
            visionGuideConfig = JSON.parseObject(jsonData, VisionGuideConfig.class);
        }
        return visionGuideConfig;
    }

    public void writeDeviceRegisterForm(DeviceRegisterForm deviceRegisterForm) {
        Integer deviceChannel = deviceRegisterForm.getChannel();
        String fileName = deviceChannel == null ?
                deviceRegisterForm.getAliasName() : String.format("%s", deviceRegisterForm.getAliasName());
        File jsonFile = new File(deviceFileConstants.deviceConfigFilePath, fileName + JSON_SUFFIX_WITH_DOT);
        String value = JSON.toJSONString(deviceRegisterForm);
        ThreadSafeFileUtils.writeFileFromString(jsonFile, value, false);
    }

    public DeviceRegisterForm readDeviceRegisterForm(OperationTarget operationTarget) {
        return readDeviceRegisterForm(operationTarget.getAliasName());
    }

    public DeviceRegisterForm updateDeviceRegisterForm(String deviceAliasName, DeviceOperationParameter deviceOperationParameter) {
        File jsonFile = new File(deviceFileConstants.deviceConfigFilePath, deviceAliasName + JSON_SUFFIX_WITH_DOT);
        String value = ThreadSafeFileUtils.readFileToString(jsonFile.getAbsolutePath());
        DeviceRegisterForm deviceRegisterForm = JSON.parseObject(value, DeviceRegisterForm.class);
        if (deviceRegisterForm != null) {
            deviceRegisterForm.setDeviceOperationParameter(deviceOperationParameter);
            writeDeviceRegisterForm(deviceRegisterForm);
        }
        return deviceRegisterForm;
    }

    public DeviceRegisterForm readDeviceRegisterForm(String deviceAliasName) {
        File jsonFile = new File(deviceFileConstants.deviceConfigFilePath, deviceAliasName + JSON_SUFFIX_WITH_DOT);
        String value = ThreadSafeFileUtils.readFileToString(jsonFile.getAbsolutePath());
        return JSON.parseObject(value, DeviceRegisterForm.class);
    }

}
