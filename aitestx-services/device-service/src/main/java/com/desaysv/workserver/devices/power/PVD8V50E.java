package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.PVD8V50EProtocol;
import com.desaysv.workserver.devices.power.protocol.PowerSCPIProtocol;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * N6700高精度电源
 */
@Slf4j
public class PVD8V50E extends DefaultPower {
    public PVD8V50E() {
        this(new DeviceOperationParameter());
    }

    public PVD8V50E(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);

        PowerSCPIProtocol powerProtocol = new PVD8V50EProtocol(this);
        setPowerProtocol(powerProtocol);
    }

    @Override
    public int getNumberChannels() {
        return 2;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.PVD8V50E;
    }

}
