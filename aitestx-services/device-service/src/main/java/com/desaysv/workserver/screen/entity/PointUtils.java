package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.utils.PearsonRatioUtils;

/**
 * 求点工具类
 */
public class PointUtils {

    /**
     * 分离点xy数组
     *
     * @param points 点
     * @return PointArray
     */
    public static PointArray splitArray(PointInt... points) {
        double[] x = new double[points.length];
        double[] y = new double[points.length];

        for (int i = 0; i < points.length; i++) {
            x[i] = points[i].getX();
            y[i] = points[i].getY();
        }
        return new PointArray(x, y);
    }

    /**
     * 计算线性度
     *
     * @param points 待计算的点
     * @return 线性度
     */
    public static double getLinearRatio(PointInt... points) {
        PointArray pointArray = splitArray(points);
        return PearsonRatioUtils.getPearsonByDim(pointArray.getX(), pointArray.getY());
    }

}
