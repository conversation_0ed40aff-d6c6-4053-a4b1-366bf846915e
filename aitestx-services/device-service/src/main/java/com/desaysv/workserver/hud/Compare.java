package com.desaysv.workserver.hud;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
public class Compare {
    /**
     * 比较效果图片和实拍图片
     * @param effectImage 效果图片路径
     * @param realImage 实拍图片路径
     * @param similarity 相似度阈值
     * @return 返回比较结果字符串
     */
    public String compareImages(String effectImage, String realImage, double similarity) {
        // 调用比较服务
        String url = "http://*************:8001/compare-images";
        try {
            // 使用effectImage、realImage和similarity发起POST请求
            URL postUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) postUrl.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("accept", "application/json");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=----Boundary123456789");

            try (OutputStream outputStream = conn.getOutputStream()) {
                String boundary = "----Boundary123456789";

                // 写入ui图片部分
                String uiPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"ui\"; filename=\"" + new File(effectImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(uiPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(effectImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入real图片部分
                String realPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"real\"; filename=\"" + new File(realImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(realPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(realImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入param部分
                String paramPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"param\"\r\n\r\n" +
                        similarity + "\r\n" +
                        "--" + boundary + "--\r\n";
                outputStream.write(paramPart.getBytes(StandardCharsets.UTF_8));
            }

            // 读取响应
            int responseCode = conn.getResponseCode();
            log.info("responseCode：{}", responseCode);
            if (responseCode != HttpURLConnection.HTTP_OK) {
                // 处理错误情况
                return "Error: HTTP response code " + responseCode;
            }

            try (java.util.Scanner scanner = new java.util.Scanner(conn.getInputStream())) {
                String response = scanner.useDelimiter("\\A").next();
                log.info("匹配结果{}", response);

                // 使用 Gson 解析 JSON 响应
                JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
                boolean result = jsonObject.get("result").getAsBoolean();  // 提取 result 值
                log.info("result: {}", result);

                return Boolean.toString(result);  // 返回布尔值的字符串形式
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return "Error: " + e.getMessage();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "Error: " + e.getMessage();
        }
    }

    public static void main(String[] args) {
        Compare comparator = new Compare();
        String result = comparator.compareImages("D:\\UIDS1050\\Desktop\\工作需要\\失败图像识别\\f2.png", "D:\\UIDS1050\\Desktop\\工作需要\\失败图像识别\\f2.png", 0.7);
        System.out.println("Comparison result: " + result);
    }
}