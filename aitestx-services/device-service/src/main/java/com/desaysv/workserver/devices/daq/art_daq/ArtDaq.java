package com.desaysv.workserver.devices.daq.art_daq;

import com.sun.jna.*;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import com.sun.jna.win32.StdCallLibrary.StdCallCallback;


public class ArtDaq {

    /******************************************************************************
     *** ArtDAQ Attributes **************************************************************
     ******************************************************************************/

    //********** Buffer Attributes **********
    public static final int ArtDAQ_Buf_Input_BufSize = 0x186C; // Specifies the number of samples the input buffer can hold for each channel in the task. Zero indicates to allocate no buffer. Use a buffer size of 0 to perform a hardware-timed operation without using a buffer. Setting this property overrides the automatic input buffer allocation that ArtDAQ performs.
    public static final int ArtDAQ_Buf_Input_OnbrdBufSize = 0x230A; // Indicates in samples per channel the size of the onboard input buffer of the device.
    public static final int ArtDAQ_Buf_Output_BufSize = 0x186D; // Specifies the number of samples the output buffer can hold for each channel in the task. Zero indicates to allocate no buffer. Use a buffer size of 0 to perform a hardware-timed operation without using a buffer. Setting this property overrides the automatic output buffer allocation that ArtDAQ performs.
    public static final int ArtDAQ_Buf_Output_OnbrdBufSize = 0x230B; // Specifies in samples per channel the size of the onboard output buffer of the device.

    //********** Calibration Info Attributes **********
    public static final int ArtDAQ_SelfCal_Supported = 0x1860; // Indicates whether the device supports self-calibration.

    //********** System Attributes **********
    public static final int ArtDAQ_Sys_Tasks = 0x1267; // Indicates an array that contains the names of all tasks saved on the system.
    public static final int ArtDAQ_Sys_DevNames = 0x193B; // Indicates the names of all devices installed in the system.
    public static final int ArtDAQ_Sys_MajorVersion = 0x1272; // Indicates the major portion of the installed version of ArtDAQ, such as 1 for version 1.5.3.
    public static final int ArtDAQ_Sys_MinorVersion = 0x1923; // Indicates the minor portion of the installed version of ArtDAQ, such as 5 for version 1.5.3.
    public static final int ArtDAQ_Sys_UpdateVersion = 0x2F22; // Indicates the update portion of the installed version of ArtDAQ, such as 3 for version 1.5.3.

    //********** Task Attributes **********
    public static final int ArtDAQ_Task_Name = 0x1276; // Indicates the name of the task.
    public static final int ArtDAQ_Task_Channels = 0x1273; // Indicates the names of all virtual channels in the task.
    public static final int ArtDAQ_Task_NumChans = 0x2181; // Indicates the number of virtual channels in the task.
    public static final int ArtDAQ_Task_Devices = 0x230E; // Indicates an array containing the names of all devices in the task.
    public static final int ArtDAQ_Task_NumDevices = 0x29BA; // Indicates the number of devices in the task.
    public static final int ArtDAQ_Task_Complete = 0x1274; // Indicates whether the task completed execution.

    //********** Device Attributes **********
    public static final int ArtDAQ_Dev_IsSimulated = 0x22CA; // Indicates if the device is a simulated device.
    public static final int ArtDAQ_Dev_ProductCategory = 0x29A9; // Indicates the product category of the device.
    public static final int ArtDAQ_Dev_ProductType = 0x0631; // Indicates the product name of the device.
    public static final int ArtDAQ_Dev_ProductNum = 0x231D; // Indicates the unique hardware identification number for the device.
    public static final int ArtDAQ_Dev_SerialNum = 0x0632; // Indicates the serial number of the device. This value is zero if the device does not have a serial number.
    public static final int ArtDAQ_Dev_AI_PhysicalChans = 0x231E; // Indicates the number of the analog input physical channels available on the device.
    public static final int ArtDAQ_Dev_AI_SupportedMeasTypes = 0x2FD2; // Indicates the measurement types supported by the physical channels of the device. Refer to Measurement Types for information on specific channels.
    public static final int ArtDAQ_Dev_AI_MaxSingleChanRate = 0x298C; // Indicates the maximum rate for an analog input task if the task contains only a single channel from this device.
    public static final int ArtDAQ_Dev_AI_MaxMultiChanRate = 0x298D; // Indicates the maximum sampling rate for an analog input task from this device. To find the maximum rate for the task, take the minimum of Maximum Single Channel Rate or the indicated sampling rate of this device divided by the number of channels to acquire data from (including cold-junction compensation and auto zero channels).
    public static final int ArtDAQ_Dev_AI_MinRate = 0x298E; // Indicates the minimum rate for an analog input task on this device. ArtDAQ returns a warning or error if you attempt to sample at a slower rate.
    public static final int ArtDAQ_Dev_AI_SampModes = 0x2FDC; // Indicates sample modes supported by devices that support sample clocked analog input.
    public static final int ArtDAQ_Dev_AI_TrigUsage = 0x2986; // Indicates the triggers supported by this device for an analog input task.
    public static final int ArtDAQ_Dev_AI_VoltageRngs = 0x2990; // Indicates pairs of input voltage ranges supported by this device. Each pair consists of the low value, followed by the high value.
    public static final int ArtDAQ_Dev_AI_VoltageIntExcitDiscreteVals = 0x29C9; // Indicates the set of discrete internal voltage excitation values supported by this device. If the device supports ranges of internal excitation values, use Range Values to determine supported excitation values.
    public static final int ArtDAQ_Dev_AI_VoltageIntExcitRangeVals = 0x29CA; // Indicates pairs of internal voltage excitation ranges supported by this device. Each pair consists of the low value, followed by the high value. If the device supports a set of discrete internal excitation values, use Discrete Values to determine the supported excitation values.
    public static final int ArtDAQ_Dev_AI_ChargeRngs = 0x3111; // Indicates in coulombs pairs of input charge ranges for the device. Each pair consists of the low value followed by the high value.
    public static final int ArtDAQ_Dev_AI_CurrentRngs = 0x2991; // Indicates the pairs of current input ranges supported by this device. Each pair consists of the low value, followed by the high value.
    public static final int ArtDAQ_Dev_AI_CurrentIntExcitDiscreteVals = 0x29CB; // Indicates the set of discrete internal current excitation values supported by this device.
    public static final int ArtDAQ_Dev_AI_BridgeRngs = 0x2FD0; // Indicates pairs of input voltage ratio ranges, in volts per volt, supported by devices that acquire using ratiometric measurements. Each pair consists of the low value followed by the high value.
    public static final int ArtDAQ_Dev_AI_ResistanceRngs = 0x2A15; // Indicates pairs of input resistance ranges, in ohms, supported by devices that have the necessary signal conditioning to measure resistances. Each pair consists of the low value followed by the high value.
    public static final int ArtDAQ_Dev_AI_FreqRngs = 0x2992; // Indicates the pairs of frequency input ranges supported by this device. Each pair consists of the low value, followed by the high value.
    public static final int ArtDAQ_Dev_AI_Couplings = 0x2994; // Indicates the coupling types supported by this device.
    public static final int ArtDAQ_Dev_AO_PhysicalChans = 0x231F; // Indicates the number of the analog output physical channels available on the device.
    public static final int ArtDAQ_Dev_AO_SupportedOutputTypes = 0x2FD3; // Indicates the generation types supported by the physical channels of the device. Refer to Output Types for information on specific channels.
    public static final int ArtDAQ_Dev_AO_SampModes = 0x2FDD; // Indicates sample modes supported by devices that support sample clocked analog output.
    public static final int ArtDAQ_Dev_AO_MaxRate = 0x2997; // Indicates the maximum analog output rate of the device.
    public static final int ArtDAQ_Dev_AO_MinRate = 0x2998; // Indicates the minimum analog output rate of the device.
    public static final int ArtDAQ_Dev_AO_TrigUsage = 0x2987; // Indicates the triggers supported by this device for analog output tasks.
    public static final int ArtDAQ_Dev_AO_VoltageRngs = 0x299B; // Indicates pairs of output voltage ranges supported by this device. Each pair consists of the low value, followed by the high value.
    public static final int ArtDAQ_Dev_AO_CurrentRngs = 0x299C; // Indicates pairs of output current ranges supported by this device. Each pair consists of the low value, followed by the high value.
    public static final int ArtDAQ_Dev_DI_Lines = 0x2320; // Indicates an array containing the names of the digital input lines available on the device.
    public static final int ArtDAQ_Dev_DI_Ports = 0x2321; // Indicates an array containing the names of the digital input ports available on the device.
    public static final int ArtDAQ_Dev_DI_MaxRate = 0x2999; // Indicates the maximum digital input rate of the device.
    public static final int ArtDAQ_Dev_DI_TrigUsage = 0x2988; // Indicates the triggers supported by this device for digital input tasks.
    public static final int ArtDAQ_Dev_DO_Lines = 0x2322; // Indicates an array containing the names of the digital output lines available on the device.
    public static final int ArtDAQ_Dev_DO_Ports = 0x2323; // Indicates an array containing the names of the digital output ports available on the device.
    public static final int ArtDAQ_Dev_DO_MaxRate = 0x299A; // Indicates the maximum digital output rate of the device.
    public static final int ArtDAQ_Dev_DO_TrigUsage = 0x2989; // Indicates the triggers supported by this device for digital output tasks.
    public static final int ArtDAQ_Dev_CI_PhysicalChans = 0x2324; // Indicates the number of the counter input physical channels available on the device.
    public static final int ArtDAQ_Dev_CI_SupportedMeasTypes = 0x2FD4; // Indicates the measurement types supported by the physical channels of the device. Refer to Measurement Types for information on specific channels.
    public static final int ArtDAQ_Dev_CI_TrigUsage = 0x298A; // Indicates the triggers supported by this device for counter input tasks.
    public static final int ArtDAQ_Dev_CI_SampModes = 0x2FDE; // Indicates sample modes supported by devices that support sample clocked counter input.
    public static final int ArtDAQ_Dev_CI_MaxSize = 0x299F; // Indicates in bits the size of the counters on the device.
    public static final int ArtDAQ_Dev_CI_MaxTimebase = 0x29A0; // Indicates in hertz the maximum counter timebase frequency.
    public static final int ArtDAQ_Dev_CO_PhysicalChans = 0x2325; // Indicates an array containing the names of the counter output physical channels available on the device.
    public static final int ArtDAQ_Dev_CO_SupportedOutputTypes = 0x2FD5; // Indicates the generation types supported by the physical channels of the device. Refer to Output Types for information on specific channels.
    public static final int ArtDAQ_Dev_CO_SampModes = 0x2FDF; // Indicates sample modes supported by devices that support sample clocked counter output.
    public static final int ArtDAQ_Dev_CO_TrigUsage = 0x298B; // Indicates the triggers supported by this device for counter output tasks.
    public static final int ArtDAQ_Dev_CO_MaxSize = 0x29A1; // Indicates in bits the size of the counters on the device.
    public static final int ArtDAQ_Dev_CO_MaxTimebase = 0x29A2; // Indicates in hertz the maximum counter timebase frequency.
    public static final int ArtDAQ_Dev_BusType = 0x2326; // Indicates the bus type of the device.
    public static final int ArtDAQ_Dev_PCI_BusNum = 0x2327; // Indicates the PCI bus number of the device.
    public static final int ArtDAQ_Dev_PCI_DevNum = 0x2328; // Indicates the PCI slot number of the device.
    public static final int ArtDAQ_Dev_PXI_ChassisNum = 0x2329; // Indicates the PXI chassis number of the device, as identified in DMC.
    public static final int ArtDAQ_Dev_PXI_SlotNum = 0x232A; // Indicates the PXI slot number of the device.
    public static final int ArtDAQ_Dev_TCPIP_Hostname = 0x2A8B; // Indicates the IPv4 hostname of the device.
    public static final int ArtDAQ_Dev_TCPIP_EthernetIP = 0x2A8C; // Indicates the IPv4 address of the Ethernet interface in dotted decimal format. This property returns 0.0.0.0 if the Ethernet interface cannot acquire an address.
    public static final int ArtDAQ_Dev_TCPIP_WirelessIP = 0x2A8D; // Indicates the IPv4 address of the 802.11 wireless interface in dotted decimal format. This property returns 0.0.0.0 if the wireless interface cannot acquire an address.
    public static final int ArtDAQ_Dev_Terminals = 0x2A40; // Indicates a list of all terminals on the device.

    //********** Physical Channel Attributes **********
    public static final int ArtDAQ_PhysicalChan_AI_InputSrcs = 0x2FD8; // Indicates the list of input sources supported by the channel. Channels may support using the signal from the I/O connector or one of several calibration signals.

    //********** Channel Attributes **********
    public static final int ArtDAQ_AI_Max = 0x17DD; // Specifies the maximum value you expect to measure. This value is in the units you specify with a units property. When you query this property, it returns the coerced maximum value that the device can measure with the current settings.
    public static final int ArtDAQ_AI_Min = 0x17DE; // Specifies the minimum value you expect to measure. This value is in the units you specify with a units property.  When you query this property, it returns the coerced minimum value that the device can measure with the current settings.
    public static final int ArtDAQ_AI_MeasType = 0x0695; // Indicates the measurement to take with the analog input channel and in some cases, such as for temperature measurements, the sensor to use.
    public static final int ArtDAQ_AI_TermCfg = 0x1097; // Specifies the terminal configuration for the channel.
    public static final int ArtDAQ_AI_InputSrc = 0x2198; // Specifies the source of the channel. You can use the signal from the I/O connector or one of several calibration signals. Certain devices have a single calibration signal bus. For these devices, you must specify the same calibration signal for all channels you connect to a calibration signal.
    public static final int ArtDAQ_AI_Voltage_Units = 0x1094; // Specifies the units to use to return voltage measurements from the channel.
    public static final int ArtDAQ_AI_Current_Units = 0x0701; // Specifies the units to use to return current measurements from the channel.
    public static final int ArtDAQ_AI_Temp_Units = 0x1033; // Specifies the units to use to return temperature measurements from the channel.
    public static final int ArtDAQ_AI_Thrmcpl_Type = 0x1050; // Specifies the type of thermocouple connected to the channel. Thermocouple types differ in composition and measurement range.
    public static final int ArtDAQ_AI_Thrmcpl_ScaleType = 0x29D0; // Specifies the method or equation form that the thermocouple scale uses.
    public static final int ArtDAQ_AI_Thrmcpl_CJCSrc = 0x1035; // Indicates the source of cold-junction compensation.
    public static final int ArtDAQ_AI_Thrmcpl_CJCVal = 0x1036; // Specifies the temperature of the cold junction if CJC Source is ArtDAQ_Val_ConstVal. Specify this value in the units of the measurement.
    public static final int ArtDAQ_AI_Thrmcpl_CJCChan = 0x1034; // Indicates the channel that acquires the temperature of the cold junction if CJC Source is ArtDAQ_Val_Chan. If the channel is a temperature channel, ArtDAQ acquires the temperature in the correct units. Other channel types, such as a resistance channel with a custom sensor, must use a custom scale to scale values to degrees Celsius.
    public static final int ArtDAQ_AI_Strain_Units = 0x0981; // Specifies the units to use to return strain measurements from the channel.
    public static final int ArtDAQ_AI_StrainGage_ForceReadFromChan = 0x2FFA; // Specifies whether the data is returned by an ArtDAQ Read function when set on a raw strain channel that is part of a rosette configuration.
    public static final int ArtDAQ_AI_StrainGage_GageFactor = 0x0994; // Specifies the sensitivity of the strain gage.  Gage factor relates the change in electrical resistance to the change in strain. Refer to the sensor documentation for this value.
    public static final int ArtDAQ_AI_StrainGage_PoissonRatio = 0x0998; // Specifies the ratio of lateral strain to axial strain in the material you are measuring.
    public static final int ArtDAQ_AI_StrainGage_Cfg = 0x0982; // Specifies the bridge configuration of the strain gages.
    public static final int ArtDAQ_AI_Excit_Src = 0x17F4; // Specifies the source of excitation.
    public static final int ArtDAQ_AI_Excit_Val = 0x17F5; // Specifies the amount of excitation that the sensor requires. If Voltage or Current is  ArtDAQ_Val_Voltage, this value is in volts. If Voltage or Current is  ArtDAQ_Val_Current, this value is in amperes.
    public static final int ArtDAQ_AI_AutoZeroMode = 0x1760; // Specifies how often to measure ground. ArtDAQ subtracts the measured ground voltage from every sample.
    public static final int ArtDAQ_AI_Coupling = 0x0064; // Specifies the coupling for the channel.
    public static final int ArtDAQ_AI_Bridge_Units = 0x2F92; // Specifies in which unit to return voltage ratios from the channel.
    public static final int ArtDAQ_AI_Bridge_Cfg = 0x0087; // Specifies the type of Wheatstone bridge connected to the channel.
    public static final int ArtDAQ_AI_Bridge_NomResistance = 0x17EC; // Specifies in ohms the resistance of the bridge while not under load.
    public static final int ArtDAQ_AI_Bridge_InitialVoltage = 0x17ED; // Specifies in volts the output voltage of the bridge while not under load. ArtDAQ subtracts this value from any measurements before applying scaling equations.  If you set Initial Bridge Ratio, ArtDAQ coerces this property to Initial Bridge Ratio times Actual Excitation Value. This property is set by ArtDAQ Perform Bridge Offset Nulling Calibration. If you set this property, ArtDAQ coerces Initial Bridge Ratio...
    public static final int ArtDAQ_AI_Bridge_InitialRatio = 0x2F86; // Specifies in volts per volt the ratio of output voltage from the bridge to excitation voltage supplied to the bridge while not under load. ArtDAQ subtracts this value from any measurements before applying scaling equations. If you set Initial Bridge Voltage, ArtDAQ coerces this property  to Initial Bridge Voltage divided by Actual Excitation Value. If you set this property, ArtDAQ coerces Initial Bridge Volt...
    public static final int ArtDAQ_AI_Bridge_ShuntCal_Enable = 0x0094; // Specifies whether to enable a shunt calibration switch. Use Shunt Cal Select to select the switch(es) to enable.
    public static final int ArtDAQ_AI_CurrentShunt_Resistance = 0x17F3; // Specifies in ohms the external shunt resistance for current measurements.
    public static final int ArtDAQ_AO_Max = 0x1186; // Specifies the maximum value you expect to generate. The value is in the units you specify with a units property. If you try to write a value larger than the maximum value, ArtDAQ generates an error. ArtDAQ might coerce this value to a smaller value if other task settings restrict the device from generating the desired maximum.
    public static final int ArtDAQ_AO_Min = 0x1187; // Specifies the minimum value you expect to generate. The value is in the units you specify with a units property. If you try to write a value smaller than the minimum value, ArtDAQ generates an error. ArtDAQ might coerce this value to a larger value if other task settings restrict the device from generating the desired minimum.
    public static final int ArtDAQ_AO_OutputType = 0x1108; // Indicates whether the channel generates voltage,  current, or a waveform.
    public static final int ArtDAQ_AO_Voltage_Units = 0x1184; // Specifies in what units to generate voltage on the channel. Write data to the channel in the units you select.
    public static final int ArtDAQ_AO_Current_Units = 0x1109; // Specifies in what units to generate current on the channel. Write data to the channel in the units you select.
    public static final int ArtDAQ_DI_NumLines = 0x2178; // Indicates the number of digital lines in the channel.
    public static final int ArtDAQ_DO_NumLines = 0x2179; // Indicates the number of digital lines in the channel.
    public static final int ArtDAQ_CI_Max = 0x189C; // Specifies the maximum value you expect to measure. This value is in the units you specify with a units property. When you query this property, it returns the coerced maximum value that the hardware can measure with the current settings.
    public static final int ArtDAQ_CI_Min = 0x189D; // Specifies the minimum value you expect to measure. This value is in the units you specify with a units property. When you query this property, it returns the coerced minimum value that the hardware can measure with the current settings.
    public static final int ArtDAQ_CI_MeasType = 0x18A0; // Indicates the measurement to take with the channel.
    public static final int ArtDAQ_CI_Freq_Units = 0x18A1; // Specifies the units to use to return frequency measurements.
    public static final int ArtDAQ_CI_Freq_StartingEdge = 0x0799; // Specifies between which edges to measure the frequency of the signal.
    public static final int ArtDAQ_CI_Freq_MeasMeth = 0x0144; // Specifies the method to use to measure the frequency of the signal.
    public static final int ArtDAQ_CI_Freq_MeasTime = 0x0145; // Specifies in seconds the length of time to measure the frequency of the signal if Method is ArtDAQ_Val_HighFreq2Ctr. Measurement accuracy increases with increased measurement time and with increased signal frequency. If you measure a high-frequency signal for too long, however, the count register could roll over, which results in an incorrect measurement.
    public static final int ArtDAQ_CI_Freq_Div = 0x0147; // Specifies the value by which to divide the input signal if  Method is ArtDAQ_Val_LargeRng2Ctr. The larger the divisor, the more accurate the measurement. However, too large a value could cause the count register to roll over, which results in an incorrect measurement.
    public static final int ArtDAQ_CI_Period_Units = 0x18A3; // Specifies the unit to use to return period measurements.
    public static final int ArtDAQ_CI_Period_StartingEdge = 0x0852; // Specifies between which edges to measure the period of the signal.
    public static final int ArtDAQ_CI_Period_MeasMeth = 0x192C; // Specifies the method to use to measure the period of the signal.
    public static final int ArtDAQ_CI_Period_MeasTime = 0x192D; // Specifies in seconds the length of time to measure the period of the signal if Method is ArtDAQ_Val_HighFreq2Ctr. Measurement accuracy increases with increased measurement time and with increased signal frequency. If you measure a high-frequency signal for too long, however, the count register could roll over, which results in an incorrect measurement.
    public static final int ArtDAQ_CI_Period_Div = 0x192E; // Specifies the value by which to divide the input signal if Method is ArtDAQ_Val_LargeRng2Ctr. The larger the divisor, the more accurate the measurement. However, too large a value could cause the count register to roll over, which results in an incorrect measurement.
    public static final int ArtDAQ_CI_CountEdges_InitialCnt = 0x0698; // Specifies the starting value from which to count.
    public static final int ArtDAQ_CI_CountEdges_ActiveEdge = 0x0697; // Specifies on which edges to increment or decrement the counter.
    public static final int ArtDAQ_CI_CountEdges_Term = 0x18C7; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_CountEdges_Dir = 0x0696; // Specifies whether to increment or decrement the counter on each edge.
    public static final int ArtDAQ_CI_CountEdges_DirTerm = 0x21E1; // Specifies the source terminal of the digital signal that controls the count direction if Direction is ArtDAQ_Val_ExtControlled.
    public static final int ArtDAQ_CI_CountEdges_CountReset_Enable = 0x2FAF; // Specifies whether to reset the count on the active edge specified with Terminal.
    public static final int ArtDAQ_CI_CountEdges_CountReset_ResetCount = 0x2FB0; // Specifies the value to reset the count to.
    public static final int ArtDAQ_CI_CountEdges_CountReset_Term = 0x2FB1; // Specifies the input terminal of the signal to reset the count.
    public static final int ArtDAQ_CI_CountEdges_CountReset_DigFltr_MinPulseWidth = 0x2FB4; // Specifies the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_CI_CountEdges_CountReset_ActiveEdge = 0x2FB2; // Specifies on which edge of the signal to reset the count.
    public static final int ArtDAQ_CI_PulseWidth_Units = 0x0823; // Specifies the units to use to return pulse width measurements.
    public static final int ArtDAQ_CI_PulseWidth_Term = 0x18AA; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_PulseWidth_StartingEdge = 0x0825; // Specifies on which edge of the input signal to begin each pulse width measurement.
    public static final int ArtDAQ_CI_DutyCycle_Term = 0x308D; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_DutyCycle_StartingEdge = 0x3092; // Specifies which edge of the input signal to begin the duty cycle measurement.
    public static final int ArtDAQ_CI_SemiPeriod_Units = 0x18AF; // Specifies the units to use to return semi-period measurements.
    public static final int ArtDAQ_CI_SemiPeriod_Term = 0x18B0; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_SemiPeriod_StartingEdge = 0x22FE; // Specifies on which edge of the input signal to begin semi-period measurement. Semi-period measurements alternate between high time and low time, starting on this edge.
    public static final int ArtDAQ_CI_TwoEdgeSep_Units = 0x18AC; // Specifies the units to use to return two-edge separation measurements from the channel.
    public static final int ArtDAQ_CI_TwoEdgeSep_FirstTerm = 0x18AD; // Specifies the source terminal of the digital signal that starts each measurement.
    public static final int ArtDAQ_CI_TwoEdgeSep_FirstEdge = 0x0833; // Specifies on which edge of the first signal to start each measurement.
    public static final int ArtDAQ_CI_TwoEdgeSep_SecondTerm = 0x18AE; // Specifies the source terminal of the digital signal that stops each measurement.
    public static final int ArtDAQ_CI_TwoEdgeSep_SecondEdge = 0x0834; // Specifies on which edge of the second signal to stop each measurement.
    public static final int ArtDAQ_CI_Pulse_Freq_Units = 0x2F0B; // Specifies the units to use to return pulse specifications in terms of frequency.
    public static final int ArtDAQ_CI_Pulse_Freq_Term = 0x2F04; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_Pulse_Freq_Start_Edge = 0x2F05; // Specifies on which edge of the input signal to begin pulse measurement.
    public static final int ArtDAQ_CI_Pulse_Time_Units = 0x2F13; // Specifies the units to use to return pulse specifications in terms of high time and low time.
    public static final int ArtDAQ_CI_Pulse_Time_Term = 0x2F0C; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_Pulse_Time_StartEdge = 0x2F0D; // Specifies on which edge of the input signal to begin pulse measurement.
    public static final int ArtDAQ_CI_Pulse_Ticks_Term = 0x2F14; // Specifies the input terminal of the signal to measure.
    public static final int ArtDAQ_CI_Pulse_Ticks_StartEdge = 0x2F15; // Specifies on which edge of the input signal to begin pulse measurement.
    public static final int ArtDAQ_CI_AngEncoder_Units = 0x18A6; // Specifies the units to use to return angular position measurements from the channel.
    public static final int ArtDAQ_CI_AngEncoder_PulsesPerRev = 0x0875; // Specifies the number of pulses the encoder generates per revolution. This value is the number of pulses on either signal A or signal B, not the total number of pulses on both signal A and signal B.
    public static final int ArtDAQ_CI_AngEncoder_InitialAngle = 0x0881; // Specifies the starting angle of the encoder. This value is in the units you specify with Units.
    public static final int ArtDAQ_CI_LinEncoder_Units = 0x18A9; // Specifies the units to use to return linear encoder measurements from the channel.
    public static final int ArtDAQ_CI_LinEncoder_DistPerPulse = 0x0911; // Specifies the distance to measure for each pulse the encoder generates on signal A or signal B. This value is in the units you specify with Units.
    public static final int ArtDAQ_CI_LinEncoder_InitialPos = 0x0915; // Specifies the position of the encoder when the measurement begins. This value is in the units you specify with Units.
    public static final int ArtDAQ_CI_Encoder_DecodingType = 0x21E6; // Specifies how to count and interpret the pulses the encoder generates on signal A and signal B. ArtDAQ_Val_X1, ArtDAQ_Val_X2, and ArtDAQ_Val_X4 are valid for quadrature encoders only. ArtDAQ_Val_TwoPulseCounting is valid for two-pulse encoders only.
    public static final int ArtDAQ_CI_Encoder_AInputTerm = 0x219D; // Specifies the terminal to which signal A is connected.
    public static final int ArtDAQ_CI_Encoder_AInputInvert = 0x21FD; // Specifies whether the A input signal needs to be inverted.
    public static final int ArtDAQ_CI_Encoder_BInputTerm = 0x219E; // Specifies the terminal to which signal B is connected.
    public static final int ArtDAQ_CI_Encoder_BInputInvert = 0x21FE; // Specifies whether the B input signal needs to be inverted.
    public static final int ArtDAQ_CI_Encoder_ZInputTerm = 0x219F; // Specifies the terminal to which signal Z is connected.
    public static final int ArtDAQ_CI_Encoder_ZInputInvert = 0x21FF; // Specifies whether the Z input signal needs to be inverted.
    public static final int ArtDAQ_CI_Encoder_ZIndexEnable = 0x0890; // Specifies whether to use Z indexing for the channel.
    public static final int ArtDAQ_CI_Encoder_ZIndexVal = 0x0888; // Specifies the value to which to reset the measurement when signal Z is high and signal A and signal B are at the states you specify with Z Index Phase. Specify this value in the units of the measurement.
    public static final int ArtDAQ_CI_Encoder_ZIndexPhase = 0x0889; // Specifies the states at which signal A and signal B must be while signal Z is high for ArtDAQ to reset the measurement. If signal Z is never high while signal A and signal B are high, for example, you must choose a phase other than ArtDAQ_Val_AHighBHigh.
    public static final int ArtDAQ_CI_Source_DigFltr_MinPulseWidth = 0x21FC; // Specifies in seconds the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_CI_Gate_DigFltr_MinPulseWidth = 0x2201; // Specifies in seconds the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_CI_Aux_DigFltr_MinPulseWidth = 0x2206; // Specifies in seconds the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_CI_CtrTimebaseSrc = 0x0143; // Specifies the terminal of the timebase to use for the counter.
    public static final int ArtDAQ_CI_CtrTimebaseRate = 0x18B2; // Specifies in Hertz the frequency of the counter timebase. Specifying the rate of a counter timebase allows you to take measurements in terms of time or frequency rather than in ticks of the timebase. If you use an external timebase and do not specify the rate, you can take measurements only in terms of ticks of the timebase.
    public static final int ArtDAQ_CI_CtrTimebaseActiveEdge = 0x0142; // Specifies whether a timebase cycle is from rising edge to rising edge or from falling edge to falling edge.
    public static final int ArtDAQ_CO_OutputType = 0x18B5; // Indicates how to public static final int pulses generated on the channel.
    public static final int ArtDAQ_CO_Pulse_IdleState = 0x1170; // Specifies the resting state of the output terminal.
    public static final int ArtDAQ_CO_Pulse_Term = 0x18E1; // Specifies on which terminal to generate pulses.
    public static final int ArtDAQ_CO_Pulse_Time_Units = 0x18D6; // Specifies the units in which to public static final int high and low pulse time.
    public static final int ArtDAQ_CO_Pulse_HighTime = 0x18BA; // Specifies the amount of time that the pulse is at a high voltage. This value is in the units you specify with Units or when you create the channel.
    public static final int ArtDAQ_CO_Pulse_LowTime = 0x18BB; // Specifies the amount of time that the pulse is at a low voltage. This value is in the units you specify with Units or when you create the channel.
    public static final int ArtDAQ_CO_Pulse_Time_InitialDelay = 0x18BC; // Specifies in seconds the amount of time to wait before generating the first pulse.
    public static final int ArtDAQ_CO_Pulse_DutyCyc = 0x1176; // Specifies the duty cycle of the pulses. The duty cycle of a signal is the width of the pulse divided by period. ArtDAQ uses this ratio and the pulse frequency to determine the width of the pulses and the delay between pulses.
    public static final int ArtDAQ_CO_Pulse_Freq_Units = 0x18D5; // Specifies the units in which to public static final int pulse frequency.
    public static final int ArtDAQ_CO_Pulse_Freq = 0x1178; // Specifies the frequency of the pulses to generate. This value is in the units you specify with Units or when you create the channel.
    public static final int ArtDAQ_CO_Pulse_Freq_InitialDelay = 0x0299; // Specifies in seconds the amount of time to wait before generating the first pulse.
    public static final int ArtDAQ_CO_Pulse_HighTicks = 0x1169; // Specifies the number of ticks the pulse is high.
    public static final int ArtDAQ_CO_Pulse_LowTicks = 0x1171; // Specifies the number of ticks the pulse is low.
    public static final int ArtDAQ_CO_Pulse_Ticks_InitialDelay = 0x0298; // Specifies the number of ticks to wait before generating the first pulse.
    public static final int ArtDAQ_CO_CtrTimebaseSrc = 0x0339; // Specifies the terminal of the timebase to use for the counter. Typically, ArtDAQ uses one of the internal counter timebases when generating pulses. Use this property to specify an external timebase and produce custom pulse widths that are not possible using the internal timebases.
    public static final int ArtDAQ_CO_CtrTimebaseRate = 0x18C2; // Specifies in Hertz the frequency of the counter timebase. Specifying the rate of a counter timebase allows you to public static final int output pulses in seconds rather than in ticks of the timebase. If you use an external timebase and do not specify the rate, you can public static final int output pulses only in ticks of the timebase.
    public static final int ArtDAQ_CO_CtrTimebaseActiveEdge = 0x0341; // Specifies whether a timebase cycle is from rising edge to rising edge or from falling edge to falling edge.
    public static final int ArtDAQ_CO_Count = 0x0293; // Indicates the current value of the count register.
    public static final int ArtDAQ_CO_OutputState = 0x0294; // Indicates the current state of the output terminal of the counter.
    public static final int ArtDAQ_CO_EnableInitialDelayOnRetrigger = 0x2EC9; // Specifies whether to apply the initial delay to retriggered pulse trains.
    public static final int ArtDAQ_ChanType = 0x187F; // Indicates the type of the virtual channel.
    public static final int ArtDAQ_PhysicalChanName = 0x18F5; // Specifies the name of the physical channel upon which this virtual channel is based.
    public static final int ArtDAQ_ChanDescr = 0x1926; // Specifies a user-defined description for the channel.
    public static final int ArtDAQ_ChanIsGlobal = 0x2304; // Indicates whether the channel is a global channel.

    //********** Read Attributes **********
    public static final int ArtDAQ_Read_AutoStart = 0x1826; // Specifies if an ArtDAQ Read function automatically starts the task  if you did not start the task explicitly by using ArtDAQStartTask(). The default value is TRUE. When  an ArtDAQ Read function starts a finite acquisition task, it also stops the task after reading the last sample.
    public static final int ArtDAQ_Read_OverWrite = 0x1211; // Specifies whether to overwrite samples in the buffer that you have not yet read.

    //********** Timing Attributes **********
    public static final int ArtDAQ_Sample_Mode = 0x1300; // Specifies if a task acquires or generates a finite number of samples or if it continuously acquires or generates samples.
    public static final int ArtDAQ_Sample_SampPerChan = 0x1310; // Specifies the number of samples to acquire or generate for each channel if Sample Mode is ArtDAQ_Val_FiniteSamps. If Sample Mode is ArtDAQ_Val_ContSamps, ArtDAQ uses this value to determine the buffer size.
    public static final int ArtDAQ_SampTimingType = 0x1347; // Specifies the type of sample timing to use for the task.
    public static final int ArtDAQ_SampClk_Rate = 0x1344; // Specifies the sampling rate in samples per channel per second. If you use an external source for the Sample Clock, set this input to the maximum expected rate of that clock.
    public static final int ArtDAQ_SampClk_MaxRate = 0x22C8; // Indicates the maximum Sample Clock rate supported by the task, based on other timing settings. For output tasks, the maximum Sample Clock rate is the maximum rate of the DAC. For input tasks, ArtDAQ calculates the maximum sampling rate differently for multiplexed devices than simultaneous sampling devices.
    public static final int ArtDAQ_SampClk_Src = 0x1852; // Specifies the terminal of the signal to use as the Sample Clock.
    public static final int ArtDAQ_SampClk_ActiveEdge = 0x1301; // Specifies on which edge of a clock pulse sampling takes place. This property is useful primarily when the signal you use as the Sample Clock is not a periodic clock.
    public static final int ArtDAQ_SampClk_Timebase_Src = 0x1308; // Specifies the terminal of the signal to use as the Sample Clock Timebase.
    public static final int ArtDAQ_AIConv_Src = 0x1502; // Specifies the terminal of the signal to use as the AI Convert Clock.
    public static final int ArtDAQ_RefClk_Src = 0x1316; // Specifies the terminal of the signal to use as the Reference Clock.
    public static final int ArtDAQ_SyncPulse_Src = 0x223D; // Specifies the terminal of the signal to use as the synchronization pulse. The synchronization pulse resets the clock dividers and the ADCs/DACs on the device.

    //********** Trigger Attributes **********
    public static final int ArtDAQ_StartTrig_Type = 0x1393; // Specifies the type of trigger to use to start a task.
    public static final int ArtDAQ_StartTrig_Term = 0x2F1E; // Indicates the name of the internal Start Trigger terminal for the task. This property does not return the name of the trigger source terminal.
    public static final int ArtDAQ_DigEdge_StartTrig_Src = 0x1407; // Specifies the name of a terminal where there is a digital signal to use as the source of the Start Trigger.
    public static final int ArtDAQ_DigEdge_StartTrig_Edge = 0x1404; // Specifies on which edge of a digital pulse to start acquiring or generating samples.
    public static final int ArtDAQ_AnlgEdge_StartTrig_Src = 0x1398; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the Start Trigger.
    public static final int ArtDAQ_AnlgEdge_StartTrig_Slope = 0x1397; // Specifies on which slope of the trigger signal to start acquiring or generating samples.
    public static final int ArtDAQ_AnlgEdge_StartTrig_Lvl = 0x1396; // Specifies at what threshold in the units of the measurement or generation to start acquiring or generating samples. Use Slope to specify on which slope to trigger on this threshold.
    public static final int ArtDAQ_AnlgEdge_StartTrig_Hyst = 0x1395; // Specifies a hysteresis level in the units of the measurement or generation. If Slope is ArtDAQ_Val_RisingSlope, the trigger does not deassert until the source signal passes below  Level minus the hysteresis. If Slope is ArtDAQ_Val_FallingSlope, the trigger does not deassert until the source signal passes above Level plus the hysteresis. Hysteresis is always enabled. Set this property to a non-zero value to use hyste...
    public static final int ArtDAQ_AnlgWin_StartTrig_Src = 0x1400; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the Start Trigger.
    public static final int ArtDAQ_AnlgWin_StartTrig_When = 0x1401; // Specifies whether the task starts acquiring or generating samples when the signal enters or leaves the window you specify with Bottom and Top.
    public static final int ArtDAQ_AnlgWin_StartTrig_Top = 0x1403; // Specifies the upper limit of the window. Specify this value in the units of the measurement or generation.
    public static final int ArtDAQ_AnlgWin_StartTrig_Btm = 0x1402; // Specifies the lower limit of the window. Specify this value in the units of the measurement or generation.
    public static final int ArtDAQ_StartTrig_Delay = 0x1856; // Specifies Specifies an amount of time to wait after the Start Trigger is received before acquiring or generating the first sample. This value is in the units you specify with Delay Units.
    public static final int ArtDAQ_StartTrig_DelayUnits = 0x18C8; // Specifies the units of Delay.
    public static final int ArtDAQ_StartTrig_DigFltr_MinPulseWidth = 0x2224; // Specifies in seconds the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_StartTrig_Retriggerable = 0x190F; // Specifies whether a finite task resets and waits for another Start Trigger after the task completes. When you set this property to TRUE, the device performs a finite acquisition or generation each time the Start Trigger occurs until the task stops. The device ignores a trigger if it is in the process of acquiring or generating signals.
    public static final int ArtDAQ_RefTrig_Type = 0x1419; // Specifies the type of trigger to use to mark a reference point for the measurement.
    public static final int ArtDAQ_RefTrig_PretrigSamples = 0x1445; // Specifies the minimum number of pretrigger samples to acquire from each channel before recognizing the reference trigger. Post-trigger samples per channel are equal to Samples Per Channel minus the number of pretrigger samples per channel.
    public static final int ArtDAQ_RefTrig_Term = 0x2F1F; // Indicates the name of the internal Reference Trigger terminal for the task. This property does not return the name of the trigger source terminal.
    public static final int ArtDAQ_DigEdge_RefTrig_Src = 0x1434; // Specifies the name of a terminal where there is a digital signal to use as the source of the Reference Trigger.
    public static final int ArtDAQ_DigEdge_RefTrig_Edge = 0x1430; // Specifies on what edge of a digital pulse the Reference Trigger occurs.
    public static final int ArtDAQ_AnlgEdge_RefTrig_Src = 0x1424; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the Reference Trigger.
    public static final int ArtDAQ_AnlgEdge_RefTrig_Slope = 0x1423; // Specifies on which slope of the source signal the Reference Trigger occurs.
    public static final int ArtDAQ_AnlgEdge_RefTrig_Lvl = 0x1422; // Specifies in the units of the measurement the threshold at which the Reference Trigger occurs.  Use Slope to specify on which slope to trigger at this threshold.
    public static final int ArtDAQ_AnlgEdge_RefTrig_Hyst = 0x1421; // Specifies a hysteresis level in the units of the measurement. If Slope is ArtDAQ_Val_RisingSlope, the trigger does not deassert until the source signal passes below Level minus the hysteresis. If Slope is ArtDAQ_Val_FallingSlope, the trigger does not deassert until the source signal passes above Level plus the hysteresis. Hysteresis is always enabled. Set this property to a non-zero value to use hysteresis.
    public static final int ArtDAQ_AnlgWin_RefTrig_Src = 0x1426; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the Reference Trigger.
    public static final int ArtDAQ_AnlgWin_RefTrig_When = 0x1427; // Specifies whether the Reference Trigger occurs when the source signal enters the window or when it leaves the window. Use Bottom and Top to specify the window.
    public static final int ArtDAQ_AnlgWin_RefTrig_Top = 0x1429; // Specifies the upper limit of the window. Specify this value in the units of the measurement.
    public static final int ArtDAQ_AnlgWin_RefTrig_Btm = 0x1428; // Specifies the lower limit of the window. Specify this value in the units of the measurement.
    public static final int ArtDAQ_RefTrig_AutoTrigEnable = 0x2EC1; // Specifies whether to send a software trigger to the device when a hardware trigger is no longer active in order to prevent a timeout.
    public static final int ArtDAQ_RefTrig_AutoTriggered = 0x2EC2; // Indicates whether a completed acquisition was triggered by the auto trigger. If an acquisition has not completed after the task starts, this property returns FALSE. This property is only applicable when Enable  is TRUE.
    public static final int ArtDAQ_RefTrig_Delay = 0x1483; // Specifies in seconds the time to wait after the device receives the Reference Trigger before switching from pretrigger to posttrigger samples.
    public static final int ArtDAQ_RefTrig_DigFltr_MinPulseWidth = 0x2ED8; // Specifies in seconds the minimum pulse width the filter recognizes.
    public static final int ArtDAQ_PauseTrig_Type = 0x1366; // Specifies the type of trigger to use to pause a task.
    public static final int ArtDAQ_PauseTrig_Term = 0x2F20; // Indicates the name of the internal Pause Trigger terminal for the task. This property does not return the name of the trigger source terminal.
    public static final int ArtDAQ_AnlgLvl_PauseTrig_Src = 0x1370; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the trigger.
    public static final int ArtDAQ_AnlgLvl_PauseTrig_When = 0x1371; // Specifies whether the task pauses above or below the threshold you specify with Level.
    public static final int ArtDAQ_AnlgLvl_PauseTrig_Lvl = 0x1369; // Specifies the threshold at which to pause the task. Specify this value in the units of the measurement or generation. Use Pause When to specify whether the task pauses above or below this threshold.
    public static final int ArtDAQ_AnlgLvl_PauseTrig_Hyst = 0x1368; // Specifies a hysteresis level in the units of the measurement or generation. If Pause When is ArtDAQ_Val_AboveLvl, the trigger does not deassert until the source signal passes below Level minus the hysteresis. If Pause When is ArtDAQ_Val_BelowLvl, the trigger does not deassert until the source signal passes above Level plus the hysteresis. Hysteresis is always enabled. Set this property to a non-zero value to use hys...
    public static final int ArtDAQ_AnlgWin_PauseTrig_Src = 0x1373; // Specifies the name of a virtual channel or terminal where there is an analog signal to use as the source of the trigger.
    public static final int ArtDAQ_AnlgWin_PauseTrig_When = 0x1374; // Specifies whether the task pauses while the trigger signal is inside or outside the window you specify with Bottom and Top.
    public static final int ArtDAQ_AnlgWin_PauseTrig_Top = 0x1376; // Specifies the upper limit of the window. Specify this value in the units of the measurement or generation.
    public static final int ArtDAQ_AnlgWin_PauseTrig_Btm = 0x1375; // Specifies the lower limit of the window. Specify this value in the units of the measurement or generation.
    public static final int ArtDAQ_DigLvl_PauseTrig_Src = 0x1379; // Specifies the name of a terminal where there is a digital signal to use as the source of the Pause Trigger.
    public static final int ArtDAQ_DigLvl_PauseTrig_When = 0x1380; // Specifies whether the task pauses while the signal is high or low.
    public static final int ArtDAQ_PauseTrig_DigFltr_MinPulseWidth = 0x2229; // Specifies in seconds the minimum pulse width the filter recognizes.

    //********** Export Signal Attributes **********
    public static final int ArtDAQ_Exported_AIConvClk_OutputTerm = 0x1687; // Specifies the terminal to which to route the AI Convert Clock.
    public static final int ArtDAQ_Exported_SampClk_OutputTerm = 0x1663; // Specifies the terminal to which to route the Sample Clock.
    public static final int ArtDAQ_Exported_PauseTrig_OutputTerm = 0x1615; // Specifies the terminal to which to route the Pause Trigger.
    public static final int ArtDAQ_Exported_RefTrig_OutputTerm = 0x0590; // Specifies the terminal to which to route the Reference Trigger.
    public static final int ArtDAQ_Exported_StartTrig_OutputTerm = 0x0584; // Specifies the terminal to which to route the Start Trigger.
    public static final int ArtDAQ_Exported_CtrOutEvent_OutputTerm = 0x1717; // Specifies the terminal to which to route the Counter Output Event.
    public static final int ArtDAQ_Exported_CtrOutEvent_OutputBehavior = 0x174F; // Specifies whether the exported Counter Output Event pulses or changes from one state to the other when the counter reaches terminal count.
    public static final int ArtDAQ_Exported_CtrOutEvent_Pulse_Polarity = 0x1718; // Specifies the polarity of the pulses at the output terminal of the counter when Output Behavior is ArtDAQ_Val_Pulse. ArtDAQ ignores this property if Output Behavior is ArtDAQ_Val_Toggle.
    public static final int ArtDAQ_Exported_CtrOutEvent_Toggle_IdleState = 0x186A; // Specifies the initial state of the output terminal of the counter when Output Behavior is ArtDAQ_Val_Toggle. The terminal enters this state when ArtDAQ commits the task.
    public static final int ArtDAQ_Exported_SampClkTimebase_OutputTerm = 0x18F9; // Specifies the terminal to which to route the Sample Clock Timebase.
    public static final int ArtDAQ_Exported_SyncPulseEvent_OutputTerm = 0x223C; // Specifies the terminal to which to route the Synchronization Pulse Event.

    /******************************************************************************
     *** ArtDAQ Values ************************************************************
     ******************************************************************************/

    public static final int ArtDAQ_Val_Cfg_Default = -1; // Default

    //*** Values for the Mode parameter of ArtDAQ_TaskControl ***
    public static final int ArtDAQ_Val_Task_Start = 0;   // Start
    public static final int ArtDAQ_Val_Task_Stop = 1;   // Stop
    public static final int ArtDAQ_Val_Task_Verify = 2;   // Verify
    public static final int ArtDAQ_Val_Task_Commit = 3;   // Commit
    public static final int ArtDAQ_Val_Task_Reserve = 4;   // Reserve
    public static final int ArtDAQ_Val_Task_Unreserve = 5;   // Unreserve
    public static final int ArtDAQ_Val_Task_Abort = 6;   // Abort

    //*** Values for ArtDAQ_Dev_ProductCategory ***
    //*** Value set ProductCategory ***
    public static final int ArtDAQ_Val_MultiFunc_Asyn = 14643; // Mutil function asynchronization DAQ
    public static final int ArtDAQ_Val_MultiFunc_Sync = 15858; // Mutil function synchronization DAQ
    public static final int ArtDAQ_Val_AOSeries = 14647; // AO Series
    public static final int ArtDAQ_Val_DigitalIO = 14648; // Digital I/O
    public static final int ArtDAQ_Val_TIOSeries = 14661; // TIO Series
    public static final int ArtDAQ_Val_DSA = 14649; // Dynamic Signal Acquisition
    public static final int ArtDAQ_Val_NetworkDAQ = 14829; // Network DAQ
    public static final int ArtDAQ_Val_Unknown = 12588; // Unknown

    //*** Values for ArtDAQ_Dev_BusType ***
    //*** Value set BusType ***
    public static final int ArtDAQ_Val_PCI = 12582; // PCI
    public static final int ArtDAQ_Val_PCIe = 13612; // PCIe
    public static final int ArtDAQ_Val_PXI = 12583; // PXI
    public static final int ArtDAQ_Val_PXIe = 14706; // PXIe
    public static final int ArtDAQ_Val_USB = 12586; // USB
    public static final int ArtDAQ_Val_TCPIP = 14828; // TCP/IP
//	public static final int ArtDAQ_Val_Unknown                           =                       12588; // Unknown

    //*** Values for ArtDAQ_AI_MeasType ***
    //*** Values for ArtDAQ_Dev_AI_SupportedMeasTypes ***
    //*** Values for ArtDAQ_PhysicalChan_AI_SupportedMeasTypes ***
    //*** Value set AIMeasurementType ***
    public static final int ArtDAQ_Val_Voltage = 10322; // Voltage
    public static final int ArtDAQ_Val_Current = 10134; // Current
    public static final int ArtDAQ_Val_Resistance = 10278; // Resistance
    public static final int ArtDAQ_Val_Bridge = 15908; // More:Bridge (V/V)
    public static final int ArtDAQ_Val_Strain_Gage = 10300; // Strain Gage
    public static final int ArtDAQ_Val_Voltage_IEPESensor = 15966; // IEPE Sensor
    public static final int ArtDAQ_Val_Temp_TC = 10303; // Temperature:Thermocouple

    //*** Value set for the Units parameter of ArtDAQ_CreateAIThrmcplChan ***
    public static final int ArtDAQ_Val_DegC = 10143; // Deg C
    public static final int ArtDAQ_Val_DegF = 10144; // Deg F
    public static final int ArtDAQ_Val_Kelvins = 10325; // Kelvins
    public static final int ArtDAQ_Val_DegR = 10145; // Deg R

    //*** Values for ArtDAQ_AI_Thrmcpl_Type ***
    //*** Value set ThermocoupleType1 ***
    public static final int ArtDAQ_Val_J_Type_TC = 10072; // J
    public static final int ArtDAQ_Val_K_Type_TC = 10073; // K
    public static final int ArtDAQ_Val_N_Type_TC = 10077; // N
    public static final int ArtDAQ_Val_R_Type_TC = 10082; // R
    public static final int ArtDAQ_Val_S_Type_TC = 10085; // S
    public static final int ArtDAQ_Val_T_Type_TC = 10086; // T
    public static final int ArtDAQ_Val_B_Type_TC = 10047; // B
    public static final int ArtDAQ_Val_E_Type_TC = 10055; // E

    //*** Values for ArtDAQ_AI_Thrmcpl_CJCSrc ***
    //*** Value set CJCSource1 ***
    public static final int ArtDAQ_Val_BuiltIn = 10200; // Built-In
    public static final int ArtDAQ_Val_ConstVal = 10116; // Constant Value
    public static final int ArtDAQ_Val_Chan = 10113; // Channel

    //*** Values for ArtDAQ_AI_AutoZeroMode ***
    //*** Value set AutoZeroType1 ***
    public static final int ArtDAQ_Val_None = 10230; // None
    public static final int ArtDAQ_Val_Once = 10244; // Once
    public static final int ArtDAQ_Val_EverySample = 10164; // Every Sample

    //*** Values for ArtDAQ_AO_OutputType ***
    //*** Values for ArtDAQ_Dev_AO_SupportedOutputTypes ***
    //*** Values for ArtDAQ_PhysicalChan_AO_SupportedOutputTypes ***
    //*** Value set AOOutputChannelType ***
//	public static final int ArtDAQ_Val_Voltage                          =                        10322; // Voltage
//	public static final int ArtDAQ_Val_Current                          =                        10134; // Current

    //*** Values for ArtDAQ_AI_Bridge_Cfg ***
    //*** Value set BridgeConfiguration1 ***
    public static final int ArtDAQ_Val_FullBridge = 10182; // Full Bridge
    public static final int ArtDAQ_Val_HalfBridge = 10187; // Half Bridge
    public static final int ArtDAQ_Val_QuarterBridge = 10270; // Quarter Bridge
    public static final int ArtDAQ_Val_NoBridge = 10228; // No Bridge

    //*** Values for ArtDAQ_AI_Bridge_Units ***
    //*** Value set BridgeUnits ***
    public static final int ArtDAQ_Val_VoltsPerVolt = 15896; // Volts/Volt
    public static final int ArtDAQ_Val_mVoltsPerVolt = 15897; // mVolts/Volt
    public static final int ArtDAQ_Val_FromCustomScale = 10065; // From Custom Scale

    //*** Values for ArtDAQ_AI_StrainGage_Cfg ***
    //*** Value set StrainGageBridgeType1 ***
    public static final int ArtDAQ_Val_FullBridgeI = 10183; // Full Bridge I
    public static final int ArtDAQ_Val_FullBridgeII = 10184; // Full Bridge II
    public static final int ArtDAQ_Val_FullBridgeIII = 10185; // Full Bridge III
    public static final int ArtDAQ_Val_HalfBridgeI = 10188; // Half Bridge I
    public static final int ArtDAQ_Val_HalfBridgeII = 10189; // Half Bridge II
    public static final int ArtDAQ_Val_QuarterBridgeI = 10271; // Quarter Bridge I
    public static final int ArtDAQ_Val_QuarterBridgeII = 10272; // Quarter Bridge II

    //*** Values for ArtDAQ_AI_Strain_Units ***
    //*** Value set StrainUnits1 ***
    public static final int ArtDAQ_Val_Strain = 10299; // Strain
//	public static final int ArtDAQ_Val_FromCustomScale                   =                       10065; // From Custom Scale

    //*** Values for ArtDAQ_CI_MeasType ***
    //*** Values for ArtDAQ_Dev_CI_SupportedMeasTypes ***
    //*** Values for ArtDAQ_PhysicalChan_CI_SupportedMeasTypes ***
    //*** Value set CIMeasurementType ***
    public static final int ArtDAQ_Val_Freq = 10179; // Frequency
    public static final int ArtDAQ_Val_Period = 10256; // Period
    public static final int ArtDAQ_Val_CountEdges = 10125; // Count Edges
    public static final int ArtDAQ_Val_PulseWidth = 10359; // Pulse Width
    public static final int ArtDAQ_Val_SemiPeriod = 10289; // Semi Period
    public static final int ArtDAQ_Val_PulseFrequency = 15864; // Pulse Frequency
    public static final int ArtDAQ_Val_PulseTime = 15865; // Pulse Time
    public static final int ArtDAQ_Val_PulseTicks = 15866; // Pulse Ticks
    public static final int ArtDAQ_Val_DutyCycle = 16070; // Duty Cycle
    public static final int ArtDAQ_Val_Position_AngEncoder = 10360; // Position:Angular Encoder
    public static final int ArtDAQ_Val_Position_LinEncoder = 10361; // Position:Linear Encoder
    public static final int ArtDAQ_Val_TwoEdgeSep = 10267; // Two Edge Separation

    //*** Values for ArtDAQ_CO_OutputType ***
    //*** Values for ArtDAQ_Dev_CO_SupportedOutputTypes ***
    //*** Values for ArtDAQ_PhysicalChan_CO_SupportedOutputTypes ***
    //*** Value set COOutputType ***
    public static final int ArtDAQ_Val_Pulse_Time = 10269; // Pulse:Time
    public static final int ArtDAQ_Val_Pulse_Freq = 10119; // Pulse:Frequency
    public static final int ArtDAQ_Val_Pulse_Ticks = 10268; // Pulse:Ticks

    //*** Values for ArtDAQ_ChanType ***
    //*** Value set ChannelType ***
    public static final int ArtDAQ_Val_AI = 10100; // Analog Input
    public static final int ArtDAQ_Val_AO = 10102; // Analog Output
    public static final int ArtDAQ_Val_DI = 10151; // Digital Input
    public static final int ArtDAQ_Val_DO = 10153; // Digital Output
    public static final int ArtDAQ_Val_CI = 10131; // Counter Input
    public static final int ArtDAQ_Val_CO = 10132; // Counter Output

    //*** Values for ArtDAQ_AI_TermCfg ***
    //*** Value set InputTermCfg ***
    public static final int ArtDAQ_Val_RSE = 10083; // RSE
    public static final int ArtDAQ_Val_NRSE = 10078; // NRSE
    public static final int ArtDAQ_Val_Diff = 10106; // Differential
    public static final int ArtDAQ_Val_PseudoDiff = 12529; // Pseudodifferential

    //*** Values for ArtDAQ_AI_Coupling ***
    //*** Value set Coupling1 ***
    public static final int ArtDAQ_Val_AC = 10045; // AC
    public static final int ArtDAQ_Val_DC = 10050; // DC
    public static final int ArtDAQ_Val_GND = 10066; // GND

    //*** Values for ArtDAQ_AI_Excit_Src ***
    //*** Value set ExcitationSource ***
    public static final int ArtDAQ_Val_Internal = 10200; // Internal
    public static final int ArtDAQ_Val_External = 10167; // External
//	public static final int ArtDAQ_Val_None                              =                       10230 // None

    //*** Values for ArtDAQ_AI_Voltage_Units ***
    //*** Values for ArtDAQ_AO_Voltage_Units ***
    //*** Value set VoltageUnits1 ***
    public static final int ArtDAQ_Val_Volts = 10348; // Volts

    //*** Values for ArtDAQ_AI_Current_Units ***
    //*** Values for ArtDAQ_AO_Current_Units ***
    //*** Value set CurrentUnits1 ***
    public static final int ArtDAQ_Val_Amps = 10342; // Amps

    //*** Values for ArtDAQ_CI_Freq_Units ***
    //*** Value set FrequencyUnits3 ***
    public static final int ArtDAQ_Val_Hz = 10373; // Hz
    public static final int ArtDAQ_Val_Ticks = 10304; // Ticks

    //*** Values for ArtDAQ_CI_Pulse_Freq_Units ***
    //*** Values for ArtDAQ_CO_Pulse_Freq_Units ***
    //*** Value set FrequencyUnits2 ***
//	public static final int ArtDAQ_Val_Hz                                =                       10373; // Hz

    //*** Values for ArtDAQ_CI_Period_Units ***
    //*** Values for ArtDAQ_CI_PulseWidth_Units ***
    //*** Values for ArtDAQ_CI_TwoEdgeSep_Units ***
    //*** Values for ArtDAQ_CI_SemiPeriod_Units ***
    //*** Value set TimeUnits3 ***
    public static final int ArtDAQ_Val_Seconds = 10364; // Seconds
//	public static final int ArtDAQ_Val_Ticks                             =                       10304; // Ticks

    //*** Values for ArtDAQ_CI_Pulse_Time_Units ***
    //*** Values for ArtDAQ_CO_Pulse_Time_Units ***
    //*** Value set TimeUnits2 ***
//	public static final int ArtDAQ_Val_Seconds                           =                       10364; // Seconds

    //*** Values for ArtDAQ_CI_AngEncoder_Units ***
    //*** Value set AngleUnits2 ***
    public static final int ArtDAQ_Val_Degrees = 10146; // Degrees
    public static final int ArtDAQ_Val_Radians = 10273; // Radians
//	public static final int ArtDAQ_Val_Ticks                             =                       10304; // Ticks

    //*** Values for ArtDAQ_CI_LinEncoder_Units ***
    //*** Value set LengthUnits3 ***
    public static final int ArtDAQ_Val_Meters = 10219; // Meters
    public static final int ArtDAQ_Val_Inches = 10379; // Inches
//	public static final int ArtDAQ_Val_Ticks                             =                       10304; // Ticks

    //*** Values for ArtDAQ_CI_Freq_MeasMeth ***
    //*** Values for ArtDAQ_CI_Period_MeasMeth ***
    //*** Value set CounterFrequencyMethod ***
    public static final int ArtDAQ_Val_LowFreq1Ctr = 10105; // Low Frequency with 1 Counter
    public static final int ArtDAQ_Val_HighFreq2Ctr = 10157; // High Frequency with 2 Counters
    public static final int ArtDAQ_Val_LargeRng2Ctr = 10205; // Large Range with 2 Counters
    public static final int ArtDAQ_Val_DynAvg = 16065; // Dynamic Averaging

    //*** Values for ArtDQ_CI_CountEdges_Dir ***
    //*** Value set CountDirection1 ***
    public static final int ArtDAQ_Val_CountUp = 10128; // Count Up
    public static final int ArtDAQ_Val_CountDown = 10124; // Count Down
    public static final int ArtDAQ_Val_ExtControlled = 10326; // Externally Controlled

    //*** Values for ArtDAQ_CI_Encoder_DecodingType ***
    //*** Values for ArtDAQ_CI_Velocity_Encoder_DecodingType ***
    //*** Value set EncoderType2 ***
    public static final int ArtDAQ_Val_X1 = 10090; // X1
    public static final int ArtDAQ_Val_X2 = 10091; // X2
    public static final int ArtDAQ_Val_X4 = 10092; // X4
    public static final int ArtDAQ_Val_TwoPulseCounting = 10313; // Two Pulse Counting

    //*** Values for ArtDAQ_CI_Encoder_ZIndexPhase ***
    //*** Value set EncoderZIndexPhase1 ***
    public static final int ArtDAQ_Val_AHighBHigh = 10040; // A High B High
    public static final int ArtDAQ_Val_AHighBLow = 10041; // A High B Low
    public static final int ArtDAQ_Val_ALowBHigh = 10042; // A Low B High
    public static final int ArtDAQ_Val_ALowBLow = 10043; // A Low B Low

    //*** Values for ArtDAQ_Exported_CtrOutEvent_OutputBehavior ***
    //*** Value set ExportActions2 ***
    public static final int ArtDAQ_Val_Pulse = 10265; // Pulse
    public static final int ArtDAQ_Val_Toggle = 10307; // Toggle

    //*** Values for ArtDAQ_Exported_CtrOutEvent_Pulse_Polarity ***
    //*** Value set Polarity2 ***
    public static final int ArtDAQ_Val_ActiveHigh = 10095; // Active High
    public static final int ArtDAQ_Val_ActiveLow = 10096; // Active Low

    //*** Values for ArtDAQ_CI_Freq_StartingEdge ***
    //*** Values for ArtDAQ_CI_Period_StartingEdge ***
    //*** Values for ArtDAQ_CI_CountEdges_ActiveEdge ***
    //*** Values for ArtDAQ_CI_CountEdges_CountReset_ActiveEdge ***
    //*** Values for ArtDAQ_CI_DutyCycle_StartingEdge ***
    //*** Values for ArtDAQ_CI_PulseWidth_StartingEdge ***
    //*** Values for ArtDAQ_CI_TwoEdgeSep_FirstEdge ***
    //*** Values for ArtDAQ_CI_TwoEdgeSep_SecondEdge ***
    //*** Values for ArtDAQ_CI_SemiPeriod_StartingEdge ***
    //*** Values for ArtDAQ_CI_Pulse_Freq_Start_Edge ***
    //*** Values for ArtDAQ_CI_Pulse_Time_StartEdge ***
    //*** Values for ArtDAQ_CI_Pulse_Ticks_StartEdge ***
    //*** Values for ArtDAQ_SampClk_ActiveEdge ***
    //*** Values for ArtDAQ_DigEdge_StartTrig_Edge ***
    //*** Values for ArtDAQ_DigEdge_RefTrig_Edge ***
    //*** Values for ArtDAQ_AnlgEdge_StartTrig_Slope ***
    //*** Values for ArtDAQ_AnlgEdge_RefTrig_Slope ***
    //*** Value set Edge1 ***
    public static final int ArtDAQ_Val_Rising = 10280; // Rising
    public static final int ArtDAQ_Val_Falling = 10171; // Falling
    //*** Value set Slope1 ***
    //public static final int ArtDAQ_Val_RisingSlope                                             10280 // Rising
    //public static final int ArtDAQ_Val_FallingSlope                                            10171 // Falling

    //*** Values for ArtDAQ_CI_CountEdges_GateWhen ***
    //*** Values for ArtDAQ_CI_OutputState ***
    //*** Values for ArtDAQ_CO_Pulse_IdleState ***
    //*** Values for ArtDAQ_CO_OutputState ***
    //*** Values for ArtDAQ_DigLvl_PauseTrig_When ***
    //*** Values for ArtDAQ_Exported_CtrOutEvent_Toggle_IdleState ***
    //*** Value set Level1 ***
    public static final int ArtDAQ_Val_High = 10192; // High
    public static final int ArtDAQ_Val_Low = 10214; // Low

    //*** Value set for the state parameter of ArtDAQ_SetDigitalPowerUpStates ***
//	public static final int ArtDAQ_Val_High                              =                       10192; // High
//	public static final int ArtDAQ_Val_Low                               =                       10214; // Low
    public static final int ArtDAQ_Val_Input = 10310; // Input

    //*** Value set AcquisitionType ***
    public static final int ArtDAQ_Val_FiniteSamps = 10178; // Finite Samples
    public static final int ArtDAQ_Val_ContSamps = 10123; // Continuous Samples
    public static final int ArtDAQ_Val_HWTimedSinglePoint = 12522; // Hardware Timed Single Point

    //*** Values for the everyNsamplesEventType parameter of ArtDAQ_RegisterEveryNSamplesEvent ***
    public static final int ArtDAQ_Val_Acquired_Into_Buffer = 1;    // Acquired Into Buffer
    public static final int ArtDAQ_Val_Transferred_From_Buffer = 2;     // Transferred From Buffer

    //*** Value for the Number of Samples per Channel parameter of ArtDAQ_ReadAnalogF64, ArtDAQ_ReadBinaryI16
    public static final int ArtDAQ_Val_Auto = -1;

    //*** Values for the Fill Mode parameter of ArtDAQ_Readxxxx ***
    public static final int ArtDAQ_Val_GroupByChannel = 0;   // Group by Channel
    public static final int ArtDAQ_Val_GroupByScanNumber = 1;   // Group by Scan Number

    //*** Values for ArtDAQ_Read_OverWrite ***
    //*** Value set OverwriteMode1 ***
    public static final int ArtDAQ_Val_OverwriteUnreadSamps = 10252; // Overwrite Unread Samples
    public static final int ArtDAQ_Val_DoNotOverwriteUnreadSamps = 10159; // Do Not Overwrite Unread Samples

    //*** Values for ArtDAQ_Write_RegenMode ***
    //*** Value set RegenerationMode1 ***
    public static final int ArtDAQ_Val_AllowRegen = 10097; // Allow Regeneration
    public static final int ArtDAQ_Val_DoNotAllowRegen = 10158; // Do Not Allow Regeneration

    //*** Values for the Line Grouping parameter of ArtDAQ_CreateDIChan and ArtDAQ_CreateDOChan ***
    public static final int ArtDAQ_Val_ChanPerLine = 0;   // One Channel For Each Line
    public static final int ArtDAQ_Val_ChanForAllLines = 1;   // One Channel For All Lines

    //*** Values for ArtDAQ_SampTimingType ***
    //*** Value set SampleTimingType ***
    public static final int ArtDAQ_Val_SampOnClk = 10388; // Sample Clock
    public static final int ArtDAQ_Val_Implicit = 10451; // Implicit
    public static final int ArtDAQ_Val_OnDemand = 10390; // On Demand

    //*** Value set Signal ***
    public static final int ArtDAQ_Val_AIConvertClock = 12484; // AI Convert Clock
    public static final int ArtDAQ_Val_SampleClock = 12487; // Sample Clock
    public static final int ArtDAQ_Val_RefClock = 12535; // Reference Clock
    public static final int ArtDAQ_Val_PauseTrigger = 12489; // Pause Trigger
    public static final int ArtDAQ_Val_ReferenceTrigger = 12490; // Reference Trigger
    public static final int ArtDAQ_Val_StartTrigger = 12491; // Start Trigger
    public static final int ArtDAQ_Val_CounterOutputEvent = 12494; // Counter Output Event
    public static final int ArtDAQ_Val_SampClkTimebase = 12495; // Sample Clock Timebase
    public static final int ArtDAQ_Val_SyncPulseEvent = 12496; // Sync Pulse Event

    //*** Value set Signal2 ***
//	public static final int ArtDAQ_Val_CounterOutputEvent                =                       12494; // Counter Output Event
    public static final int ArtDAQ_Val_ChangeDetectionEvent = 12511; // Change Detection Event

    //*** Values for ArtDAQ_PauseTrig_Type ***
    //*** Value set TriggerType6 ***
    public static final int ArtDAQ_Val_AnlgLvl = 10101; // Analog Level
    public static final int ArtDAQ_Val_AnlgWin = 10103; // Analog Window
    public static final int ArtDAQ_Val_DigLvl = 10152; // Digital Level
    public static final int ArtDAQ_Val_DigPattern = 10398; // Digital Pattern
//	public static final int ArtDAQ_Val_None                              =                       10230; // None

    //*** Values for ArtDAQ_RefTrig_Type ***
    //*** Value set TriggerType8 ***
    public static final int ArtDAQ_Val_AnlgEdge = 10099; // Analog Edge
    public static final int ArtDAQ_Val_DigEdge = 10150; // Digital Edge
//	public static final int ArtDAQ_Val_DigPattern                        =                       10398; // Digital Pattern
//	public static final int ArtDAQ_Val_AnlgWin                           =                       10103; // Analog Window
//	public static final int ArtDAQ_Val_None                              =                       10230; // None

    //*** Values for ArtDAQ_StartTrig_Type ***
    //*** Value set TriggerType10 ***
//	public static final int ArtDAQ_Val_AnlgEdge                          =                       10099; // Analog Edge
//	public static final int ArtDAQ_Val_DigEdge                           =                       10150; // Digital Edge
//	public static final int ArtDAQ_Val_DigPattern                        =                       10398; // Digital Pattern
//	public static final int ArtDAQ_Val_AnlgWin                           =                       10103; // Analog Window
//	public static final int ArtDAQ_Val_None                              =                       10230; // None

    //*** Values for ArtDAQ_AnlgWin_StartTrig_When ***
    //*** Values for ArtDAQ_AnlgWin_RefTrig_When ***
    //*** Value set WindowTriggerCondition1 ***
    public static final int ArtDAQ_Val_EnteringWin = 10163; // Entering Window
    public static final int ArtDAQ_Val_LeavingWin = 10208; // Leaving Window

    //*** Values for ArtDAQ_AnlgLvl_PauseTrig_When ***
    //*** Value set ActiveLevel ***
    public static final int ArtDAQ_Val_AboveLvl = 10093; // Above Level
    public static final int ArtDAQ_Val_BelowLvl = 10107; // Below Level

    //*** Values for ArtDAQ_AnlgWin_PauseTrig_When ***
    //*** Value set WindowTriggerCondition2 ***
    public static final int ArtDAQ_Val_InsideWin = 10199; // Inside Window
    public static final int ArtDAQ_Val_OutsideWin = 10251; // Outside Window

    //*** Values for ArtDAQ_StartTrig_DelayUnits ***
    //*** Value set DigitalWidthUnits1 ***
    public static final int ArtDAQ_Val_SampClkPeriods = 10286; // Sample Clock Periods
//	public static final int ArtDAQ_Val_Seconds                           =                       10364; // Seconds
//	public static final int ArtDAQ_Val_Ticks                             =                       10304; // Ticks

    //*** Values for ArtDAQ_AI_ResolutionUnits ***
    //*** Values for ArtDAQ_AO_ResolutionUnits ***
    //*** Value set ResolutionType1 ***
    public static final int ArtDAQ_Val_Bits = 10109; // Bits

    //*** Values for Read/Write Data Format
    public static final int ArtDAQ_Val_Binary_U32 = 1;
    public static final int ArtDAQ_Val_Voltage_F64 = 2;
    public static final int ArtDAQ_Val_CounterDutyCycleAndFrequency_F64 = 3;
    public static final int ArtDAQ_Val_CounterHighAndLowTimes_F64 = 4;
    public static final int ArtDAQ_Val_CounterHighAndLowTicks_U32 = 5;

    //*** Values for Run Mode
    //*** Value set RunMode ***
    public static final int ArtDAQ_Val_ServiceOn = 1;   // Default
    public static final int ArtDAQ_Val_ServiceOff = 0;


    // ##########################################################################################################
    public interface Clibrary extends Library {
        String filePath = "lib\\USB3200N\\Art_DAQ.dll";
        Clibrary INSTANCE = Native.load(filePath, Clibrary.class);

        int ArtDAQ_LoadTask(byte[] taskName, PointerByReference Pointer);

        int ArtDAQ_CreateTask(byte[] taskName, PointerByReference Pointer);

        int ArtDAQ_StartTask(Pointer Pointer);

        int ArtDAQ_StopTask(Pointer Pointer);

        int ArtDAQ_ClearTask(Pointer Pointer);

        int ArtDAQ_TestTask(Pointer Pointer);

        int ArtDAQ_TaskControl(Pointer Pointer, int action);

        int ArtDAQ_WaitUntilTaskDone(Pointer Pointer, double timeToWait);

        int ArtDAQ_IsTaskDone(Pointer Pointer, IntByReference isTaskDone);

        int ArtDAQ_GetTaskAttribute(Pointer Pointer, int attributeType, PointerByReference attribute, int size);

        interface ArtDAQ_EveryNSamplesEventCallbackPtr extends StdCallCallback {
            int EveryNSamplesEvent(Pointer Pointer, int everyNsamplesEventType, long nSamples, Pointer callbackData);
        }

        interface ArtDAQ_DoneEventCallbackPtr extends StdCallCallback {
            int DoneEvent(Pointer Pointer, int status, Pointer callbackData);
        }

        interface ArtDAQ_SignalEventCallbackPtr extends StdCallCallback {
            int SignalEvent(Pointer Pointer, int signalID, Pointer callbackData);
        }

        int ArtDAQ_RegisterEveryNSamplesEvent(Pointer task, int everyNsamplesEventType, long nSamples, long options, ArtDAQ_EveryNSamplesEventCallbackPtr callbackFunction, Pointer callbackData);

        int ArtDAQ_RegisterDoneEvent(Pointer task, long options, ArtDAQ_DoneEventCallbackPtr callbackFunction, Pointer callbackData);

        int ArtDAQ_RegisterSignalEvent(Pointer task, int signalID, long options, ArtDAQ_SignalEventCallbackPtr callbackFunction, Pointer callbackData);


        /******************************************************/
        /***        Channel Configuration/Creation          ***/
        /******************************************************/
        int ArtDAQ_CreateAIVoltageChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, int terminalConfig, double minVal, double maxVal, int units, byte[] customScaleName);

        int ArtDAQ_CreateAIVoltageIEPEChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, int terminalConfig, int coupling, double minVal, double maxVal, int currentExcitSource, double currentExcitVal);

        int ArtDAQ_CreateAIThrmcplChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int thermocoupleType, int cjcSource, double cjcVal, byte[] cjcChannel);

        int ArtDAQ_CreateAIStrainGageChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int strainConfig, int voltageExcitSource, double voltageExcitVal, double gageFactor, double initialBridgeVoltage, double nominalGageResistance, double poissonRatio, double leadWireResistance, byte[] customScaleName);

        int ArtDAQ_CreateAIBridgeChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int bridgeConfig, int voltageExcitSource, double voltageExcitVal, double nominalBridgeResistance, byte[] customScaleName);

        int ArtDAQ_CreateAOVoltageChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, byte[] customScaleName);

        int ArtDAQ_CreateAOCurrentChan(Pointer Pointer, byte[] physicalChannel, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, byte[] customScaleName);

        int ArtDAQ_CreateDIChan(Pointer Pointer, byte[] lines, byte[] nameToAssignToLines, int lineGrouping);

        int ArtDAQ_CreateDOChan(Pointer Pointer, byte[] lines, byte[] nameToAssignToLines, int lineGrouping);

        int ArtDAQ_CreateCIFreqChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int edge, int measMethod, double measTime, long divisor, byte[] customScaleName);

        int ArtDAQ_CreateCIPeriodChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int edge, int measMethod, double measTime, long divisor, byte[] customScaleName);

        int ArtDAQ_CreateCICountEdgesChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, int edge, long initialCount, int countDirection);

        int ArtDAQ_CreateCIPulseWidthChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int startingEdge, char[] customScaleName);

        int ArtDAQ_CreateCISemiPeriodChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, char[] customScaleName);

        int ArtDAQ_CreateCITwoEdgeSepChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units, int firstEdge, int secondEdge, char[] customScaleName);

        int ArtDAQ_CreateCIPulseChanFreq(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units);

        int ArtDAQ_CreateCIPulseChanTime(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, double minVal, double maxVal, int units);

        int ArtDAQ_CreateCIPulseChanTicks(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, char[] sourceTerminal, double minVal, double maxVal);

        int ArtDAQ_CreateCILinEncoderChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, int decodingType, int ZidxEnable, double ZidxVal, int ZidxPhase, int units, double distPerPulse, double initialPos, byte[] customScaleName);

        int ArtDAQ_CreateCIAngEncoderChan(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, int decodingType, int ZidxEnable, double ZidxVal, int ZidxPhase, int units, long pulsesPerRev, double initialAngle, byte[] customScaleName);

        int ArtDAQ_CreateCOPulseChanFreq(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, int units, int idleState, double initialDelay, double freq, double dutyCycle);

        int ArtDAQ_CreateCOPulseChanTime(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, int units, int idleState, double initialDelay, double lowTime, double highTime);

        int ArtDAQ_CreateCOPulseChanTicks(Pointer Pointer, byte[] counter, byte[] nameToAssignToChannel, char[] sourceTerminal, int idleState, int initialDelay, int lowTicks, int highTicks);

        /******************************************************/
        /***                    Timing                      ***/
        /******************************************************/
        int ArtDAQ_CfgSampClkTiming(Pointer taskHandle, byte[] source, double rate, int activeEdge, int sampleMode, int sampsPerChan);

        int ArtDAQ_CfgImplicitTiming(Pointer taskHandle, int sampleMode, long sampsPerChan);

        /******************************************************/
        /***                  Triggering                    ***/
        /******************************************************/
        int ArtDAQ_DisableStartTrig(Pointer taskHandle);

        int ArtDAQ_CfgDigEdgeStartTrig(Pointer taskHandle, byte[] triggerSource, int triggerEdge);

        int ArtDAQ_CfgAnlgEdgeStartTrig(Pointer taskHandle, byte[] triggerSource, int triggerSlope, double triggerLevel);

        int ArtDAQ_CfgAnlgWindowStartTrig(Pointer taskHandle, byte[] triggerSource, int triggerWhen, double windowTop, double windowBottom);

        int ArtDAQ_DisableRefTrig(Pointer taskHandle);

        int ArtDAQ_CfgDigEdgeRefTrig(Pointer taskHandle, byte[] triggerSource, int triggerEdge, long pretriggerSamples);

        int ArtDAQ_CfgAnlgEdgeRefTrig(Pointer taskHandle, byte[] triggerSource, int triggerSlope, double triggerLevel, long pretriggerSamples);

        int ArtDAQ_CfgAnlgWindowRefTrig(Pointer taskHandle, byte[] triggerSource, int triggerWhen, double windowTop, double windowBottom, long pretriggerSamples);

        int ArtDAQ_DisablePauseTrig(Pointer taskHandle);

        int ArtDAQ_CfgDigLvlPauseTrig(Pointer taskHandle, byte[] triggerSource, int triggerWhen);

        int ArtDAQ_CfgAnlgLvlPauseTrig(Pointer taskHandle, byte[] triggerSource, int triggerWhen, double triggerLevel);

        int ArtDAQ_CfgAnlgWindowPauseTrig(Pointer taskHandle, byte[] triggerSource, int triggerWhen, double windowTop, double windowBottom);

        int ArtDAQ_SendSoftwareTrigger(Pointer taskHandle, int triggerID);

        /******************************************************/
        /***                 Read Data                      ***/
        /******************************************************/
        int ArtDAQ_ReadAnalogF64(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, double[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadAnalogScalarF64(Pointer taskHandle, double timeout, DoubleByReference value, IntByReference reserved);

        int ArtDAQ_ReadBinaryI16(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, short[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadBinaryU16(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, int[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadBinaryI32(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, int[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadBinaryU32(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, long[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadDigitalU8(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, short[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadDigitalU16(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, int[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadDigitalU32(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, long[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadDigitalScalarU32(Pointer taskHandle, double timeout, LongByReference value, IntByReference reserved);

        int ArtDAQ_ReadDigitalLines(Pointer taskHandle, int numSampsPerChan, double timeout, int fillMode, short[] readArray, long arraySizeInBytes, IntByReference sampsPerChanRead, IntByReference numBytesPerSamp, IntByReference reserved);

        int ArtDAQ_ReadDigitalPort(Pointer taskHandle, byte[] deviceName, int portIndex, LongByReference portVal);

        int ArtDAQ_ReadCounterF64(Pointer taskHandle, int numSampsPerChan, double timeout, double[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadCounterU32(Pointer taskHandle, int numSampsPerChan, double timeout, long[] readArray, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadCounterScalarF64(Pointer taskHandle, double timeout, DoubleByReference value, IntByReference reserved);

        int ArtDAQ_ReadCounterScalarU32(Pointer taskHandle, double timeout, LongByReference value, IntByReference reserved);

        int ArtDAQ_ReadCtrFreq(Pointer taskHandle, int numSampsPerChan, double timeout, double[] readArrayFrequency, double[] readArrayDutyCycle, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadCtrTime(Pointer taskHandle, int numSampsPerChan, double timeout, double[] readArrayHighTime, double[] readArrayLowTime, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadCtrTicks(Pointer taskHandle, int numSampsPerChan, double timeout, long[] readArrayHighTicks, long[] readArrayLowTicks, long arraySizeInSamps, IntByReference sampsPerChanRead, IntByReference reserved);

        int ArtDAQ_ReadCtrFreqScalar(Pointer taskHandle, double timeout, DoubleByReference frequency, DoubleByReference dutyCycle, IntByReference reserved);

        int ArtDAQ_ReadCtrTimeScalar(Pointer taskHandle, double timeout, DoubleByReference highTime, DoubleByReference lowTime, IntByReference reserved);

        int ArtDAQ_ReadCtrTicksScalar(Pointer taskHandle, double timeout, LongByReference highTicks, LongByReference lowTicks, IntByReference reserved);

        /******************************************************/
        /***                 Write Data                     ***/
        /******************************************************/
        int ArtDAQ_WriteAnalogF64(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, double[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteAnalogScalarF64(Pointer taskHandle, int autoStart, double timeout, double value, IntByReference reserved);

        int ArtDAQ_WriteBinaryI16(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, short[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteBinaryU16(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, int[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteDigitalU8(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, short writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteDigitalU16(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, int[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteDigitalU32(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, long[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteDigitalScalarU32(Pointer taskHandle, int autoStart, double timeout, long value, IntByReference reserved);

        int ArtDAQ_WriteDigitalLines(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, int dataLayout, short[] writeArray, IntByReference sampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteCtrFreq(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, double[] frequency, double[] dutyCycle, IntByReference numSampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteCtrTime(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, double[] highTime, double[] lowTime, IntByReference numSampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteCtrTicks(Pointer taskHandle, int numSampsPerChan, int autoStart, double timeout, long[] highTicks, long[] lowTicks, IntByReference numSampsPerChanWritten, IntByReference reserved);

        int ArtDAQ_WriteCtrFreqScalar(Pointer taskHandle, int autoStart, double timeout, double frequency, double dutyCycle, IntByReference reserved);

        int ArtDAQ_WriteCtrTimeScalar(Pointer taskHandle, int autoStart, double timeout, double highTime, double lowTime, IntByReference reserved);

        int ArtDAQ_WriteCtrTicksScalar(Pointer taskHandle, int autoStart, double timeout, long highTicks, long lowTicks, IntByReference reserved);

        /******************************************************/
        /***               Events & Signals                 ***/
        /******************************************************/
        // Terminology:  For hardware, "signals" comprise "clocks," "triggers," and (output) "events".
        // Software signals or events are not presently supported.

        // For possible values for parameter signalID see "Value set Signal" in Values section above.
        int ArtDAQ_ExportSignal(Pointer taskHandle, int signalID, byte[] outputTerminal);

        int ArtDAQ_ExportCtrOutEvent(Pointer taskHandle, byte[] outputTerminal, int outputBehavior, int pulsePolarity, int toggleIdleState);

        /******************************************************/
        /***             Buffer Configurations              ***/
        /******************************************************/
        int ArtDAQ_CfgInputBuffer(Pointer taskHandle, long numSampsPerChan);

        int ArtDAQ_CfgOutputBuffer(Pointer taskHandle, long numSampsPerChan);

        /******************************************************/
        /***              System Configuration              ***/
        /******************************************************/
        int ArtDAQ_GetSystemAttribute(int attributeType, byte[] attribute, long bufferSize);

        /******************************************************/
        /***   Device Configuration                         ***/
        /******************************************************/
        int ArtDAQ_ResetDevice(byte[] deviceName);

        int ArtDAQ_SelfTestDevice(byte[] deviceName);

        int ArtDAQ_GetDeviceAttribute(byte[] deviceName, int attributeType, byte[] attribute, Pointer taskHandle);

        int ArtDAQ_SetDigitalPowerUpStates(byte[] deviceName, byte[] channelNames, int state);

        int ArtDAQ_GetDigitalPowerUpStates(byte[] deviceName, byte[] channelNames, int[] state, long arraySize);

        int ArtDAQ_Set5VPowerOutputStates(byte[] deviceName, int outputEnable);

        int ArtDAQ_Get5VPowerOutputStates(byte[] deviceName, IntByReference outputEnable, IntByReference reserved);

        int ArtDAQ_Set5VPowerPowerUpStates(byte[] deviceName, int outputEnable);

        int ArtDAQ_Get5VPowerPowerUpStates(byte[] deviceName, IntByReference outputEnable);

        /******************************************************/
        /***                 Calibration                    ***/
        /******************************************************/
        int ArtDAQ_SelfCal(byte[] deviceName);

        int ArtDAQ_GetAICalOffsetAndGain(byte[] deviceName, long channel, double minVal, double maxVal, double sampClock, DoubleByReference offset, DoubleByReference codeWidth);

        int ArtDAQ_GetAOCalOffsetAndGain(byte[] deviceName, long channel, double minVal, double maxVal, double sampClock, DoubleByReference offset, DoubleByReference codeWidth);

        int ArtDAQ_PerformBridgeOffsetNullingCal(Pointer taskHandle, byte[] channel);

        int ArtDAQ_PerformStrainShuntCal(Pointer taskHandle, byte[] channel, double shuntResistorValue, int shuntResistorLocation, int skipUnsupportedChannels);

        int ArtDAQ_PerformBridgeShuntCal(Pointer taskHandle, byte[] channel, double shuntResistorValue, int shuntResistorLocation, double bridgeResistance, int skipUnsupportedChannels);

        /******************************************************/
        /***                 Error Handling                 ***/
        /******************************************************/
        int ArtDAQ_GetErrorString(int errorCode, char[] errorString, long bufferSize);

        int ArtDAQ_GetExtendedErrorInfo(byte[] errorString, long bufferSize);

        /******************************************************************************
         *** ART-DAQ Specific Attribute Get/Set/Reset Function Declarations ***********
         ******************************************************************************/
        //*** Set functions for Run Mode ***
        // Uses value set RunMode
        void ArtDAQ_SetRunMode(int mode);

        //********** Timing **********
        //*** Set/Get functions for ArtDAQ_SampTimingType ***
        // Uses value set SampleTimingType
        int ArtDAQ_GetSampTimingType(Pointer taskHandle, IntByReference data);

        int ArtDAQ_SetSampTimingType(Pointer taskHandle, int data);

        int ArtDAQ_ResetSampTimingType(Pointer taskHandle);

        //*** Set functions for ArtDAQ_AIConv_Src ***
        int ArtDAQ_SetAIConvClk(Pointer taskHandle, byte[] source, int activeEdge);

        //*** Set functions for ArtDAQ_SampClk_Timebase_Src ***
        int ArtDAQ_SetSampClkTimebaseSrc(Pointer taskHandle, String data);

        //*** Set functions for ArtDAQ_Exported_SampClkTimebase_OutputTerm ***
        int ArtDAQ_SetExportedSampClkTimebaseOutputTerm(Pointer taskHandle, String data);

        //*** Set functions for ArtDAQ_RefClk_Src ***
        int ArtDAQ_SetRefClkSrc(Pointer taskHandle, String data);

        //*** Set functions for ArtDAQ_SyncPulse_Src ***
        int ArtDAQ_SetSyncPulseSrc(Pointer taskHandle, String data);

        //*** Set functions for ArtDAQ_Exported_SyncPulseEvent_OutputTerm ***
        int ArtDAQ_SetExportedSyncPulseEventOutputTerm(Pointer taskHandle, String data);

        //********** Trigger **********
        //*** Set/Get functions for ArtDAQ_AnlgEdge_StartTrig_Hyst ***
        int ArtDAQ_GetAnlgEdgeStartTrigHyst(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetAnlgEdgeStartTrigHyst(Pointer taskHandle, double data);

        int ArtDAQ_ResetAnlgEdgeStartTrigHyst(Pointer taskHandle);

        //*** Set/Get functions for ArtDAQ_AnlgEdge_RefTrig_Hyst ***
        int ArtDAQ_GetAnlgEdgeRefTrigHyst(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetAnlgEdgeRefTrigHyst(Pointer taskHandle, double data);

        int ArtDAQ_ResetAnlgEdgeRefTrigHyst(Pointer taskHandle);

        //*** Set/Get functions for ArtDAQ_AnlgLvl_PauseTrig_Hyst ***
        int ArtDAQ_GetAnlgLvlPauseTrigHyst(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetAnlgLvlPauseTrigHyst(Pointer taskHandle, double data);

        int ArtDAQ_ResetAnlgLvlPauseTrigHyst(Pointer taskHandle);

        //*** Set/Get functions for ArtDAQ_StartTrig_Delay ***
        int ArtDAQ_GetStartTrigDelay(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetStartTrigDelay(Pointer taskHandle, double data);

        //*** Set/Get functions for ArtDAQ_StartTrig_DelayUnits ***
        // Uses value set DigitalWidthUnits1
        int ArtDAQ_GetStartTrigDelayUnits(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetStartTrigDelayUnits(Pointer taskHandle, int data);

        //*** Set/Get functions for ArtDAQ_StartTrig_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetStartTrigDigFltrMinPulseWidth(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetStartTrigDigFltrMinPulseWidth(Pointer taskHandle, double data);

        //*** Set/Get functions for ArtDAQ_StartTrig_Retriggerable ***
        int ArtDAQ_GetStartTrigRetriggerable(Pointer taskHandle, IntByReference data);

        int ArtDAQ_SetStartTrigRetriggerable(Pointer taskHandle, int data);

        //*** Set/Get functions for ArtDAQ_RefTrig_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetRefTrigDigFltrMinPulseWidth(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetRefTrigDigFltrMinPulseWidth(Pointer taskHandle, double data);

        //*** Set/Get functions for ArtDAQ_PauseTrig_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetPauseTrigDigFltrMinPulseWidth(Pointer taskHandle, DoubleByReference data);

        int ArtDAQ_SetPauseTrigDigFltrMinPulseWidth(Pointer taskHandle, double data);

        //********** Read **********
        //*** Set/Get functions for ArtDAQ_Read_OverWrite ***
        // Uses value set OverwriteMode1
        int ArtDAQ_GetReadOverWrite(Pointer taskHandle, IntByReference data);

        int ArtDAQ_SetReadOverWrite(Pointer taskHandle, int data);

        //*** Set/Get functions for ArtDAQ_Read_AutoStart ***
        int ArtDAQ_GetReadAutoStart(Pointer taskHandle, IntByReference data);

        int ArtDAQ_SetReadAutoStart(Pointer taskHandle, int data);

        //********** Write **********
        //*** Set/Get functions for ArtDAQ_Write_RegenMode ***
        // Uses value set RegenerationMode1
        int ArtDAQ_GetWriteRegenMode(Pointer taskHandle, IntByReference data);

        int ArtDAQ_SetWriteRegenMode(Pointer taskHandle, int data);

        // AI
        //*** Set/Get functions for ArtDAQ_AI_InputSrc ***
        int ArtDAQ_GetAIInputSrc(Pointer taskHandle, byte[] device, byte[] data, long bufferSize);

        int ArtDAQ_SetAIInputSrc(Pointer taskHandle, byte[] device, String data);

        //*** Set/Get functions for ArtDAQ_AI_AutoZeroMode ***
        // Uses value set AutoZeroType1
        int ArtDAQ_GetAIAutoZeroMode(Pointer taskHandle, byte[] channel, IntByReference data);

        int ArtDAQ_SetAIAutoZeroMode(Pointer taskHandle, byte[] channel, int data);

        int ArtDAQ_ResetAIAutoZeroMode(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_AI_Bridge_ShuntCal_Enable ***
        int ArtDAQ_GetAIBridgeShuntCalEnable(Pointer taskHandle, byte[][] channel, IntByReference data);

        int ArtDAQ_SetAIBridgeShuntCalEnable(Pointer taskHandle, byte[][] channel, int data);

        int ArtDAQ_ResetAIBridgeShuntCalEnable(Pointer taskHandle, byte[][] channel);

        // DI
        //*** Set/Get functions for ArtDAQ_DI_DigFltr_Enable ***
        //int32 ART_API ArtDAQ_GetDIDigFltrEnable(Pointer taskHandle, byte[] channel[], bool32 *data);
        //int32 ART_API ArtDAQ_SetDIDigFltrEnable(Pointer taskHandle, byte[] channel[], bool32 data);
        //int32 ART_API ArtDAQ_ResetDIDigFltrEnable(Pointer taskHandle, byte[] channel[]);
        //
        ////*** Set/Get functions for ArtDAQ_DI_DigFltr_MinPulseWidth ***
        //int32 ART_API ArtDAQ_GetDIDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel[], double *data);
        //int32 ART_API ArtDAQ_SetDIDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel[], double data);
        //int32 ART_API ArtDAQ_ResetDIDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel[]);
        //
        ////*** Set/Get functions for ArtDAQ_DI_DigFltr_TimebaseSrc ***
        //int32 ART_API ArtDAQ_GetDIDigFltrTimebaseSrc(Pointer taskHandle, byte[] channel[], char *data, long bufferSize);
        //int32 ART_API ArtDAQ_SetDIDigFltrTimebaseSrc(Pointer taskHandle, byte[] channel[], byte[] *data);
        //int32 ART_API ArtDAQ_ResetDIDigFltrTimebaseSrc(Pointer taskHandle, byte[] channel[]);
        //
        ////*** Set/Get functions for ArtDAQ_DI_DigFltr_TimebaseRate ***
        //int32 ART_API ArtDAQ_GetDIDigFltrTimebaseRate(Pointer taskHandle, byte[] channel[], double *data);
        //int32 ART_API ArtDAQ_SetDIDigFltrTimebaseRate(Pointer taskHandle, byte[] channel[], double data);
        //int32 ART_API ArtDAQ_ResetDIDigFltrTimebaseRate(Pointer taskHandle, byte[] channel[]);

        //********** CTR **********
        //*** Set/Get functions for CI CountEdges CountReset ***
        int ArtDAQ_CfgCICountEdgesCountReset(Pointer taskHandle, byte[] sourceTerminal, long resetCount, int activeEdge, double digFltrMinPulseWidth);

        int ArtDAQ_DisableCICountEdgesCountReset(Pointer taskHandle);

        //*** Set/Get functions for ArtDAQ_CI_Source_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetCISourceDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, DoubleByReference data);

        int ArtDAQ_SetCISourceDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, double data);

        int ArtDAQ_ResetCISourceDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CI_Gate_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetCIGateDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, DoubleByReference data);

        int ArtDAQ_SetCIGateDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, double data);

        int ArtDAQ_ResetCIGateDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CI_Aux_DigFltr_MinPulseWidth ***
        int ArtDAQ_GetCIAuxDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, DoubleByReference data);

        int ArtDAQ_SetCIAuxDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel, double data);

        int ArtDAQ_ResetCIAuxDigFltrMinPulseWidth(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CI_Encoder_AInputInvert ***
        int ArtDAQ_GetCIEncoderAInputInvert(Pointer taskHandle, byte[] channel, IntByReference data);

        int ArtDAQ_SetCIEncoderAInputInvert(Pointer taskHandle, byte[] channel, int data);

        int ArtDAQ_ResetCIEncoderAInputInvert(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CI_Encoder_BInputInvert ***
        int ArtDAQ_GetCIEncoderBInputInvert(Pointer taskHandle, byte[] channel, IntByReference data);

        int ArtDAQ_SetCIEncoderBInputInvert(Pointer taskHandle, byte[] channel, int data);

        int ArtDAQ_ResetCIEncoderBInputInvert(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CI_Encoder_ZInputInvert ***
        int ArtDAQ_GetCIEncoderZInputInvert(Pointer taskHandle, byte[] channel, IntByReference data);

        int ArtDAQ_SetCIEncoderZInputInvert(Pointer taskHandle, byte[] channel, int data);

        int ArtDAQ_ResetCIEncoderZInputInvert(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CO_Pulse_Term ***
        int ArtDAQ_GetCOPulseTerm(Pointer taskHandle, byte[] channel, byte[] data, long bufferSize);

        int ArtDAQ_SetCOPulseTerm(Pointer taskHandle, byte[] channel, String data);

        int ArtDAQ_ResetCOPulseTerm(Pointer taskHandle, byte[] channel);

        //*** Set/Get functions for ArtDAQ_CO_Count ***
        int ArtDAQ_GetCOCount(Pointer taskHandle, byte[] channel, LongByReference data);

        //*** Set/Get functions for ArtDAQ_CO_OutputState ***
        // Uses value set Level1
        int ArtDAQ_GetCOOutputState(Pointer taskHandle, byte[] channel, IntByReference data);

        //*** Set/Get functions for ArtDAQ_CO_EnableInitialDelayOnRetrigger ***
        int ArtDAQ_GetCOEnableInitialDelayOnRetrigger(Pointer taskHandle, byte[] channel, IntByReference data);

        int ArtDAQ_SetCOEnableInitialDelayOnRetrigger(Pointer taskHandle, byte[] channel, int data);
    }

    /******************************************************************************
     *** ArtDAQ Error Codes *******************************************************
     ******************************************************************************/

    public static final int ArtDAQSuccess = (0);

    public static final int ArtDAQError_SerivesCanNotConnect = (-229779);
    public static final int ArtDAQError_SendSerivesData = (-229778);
    public static final int ArtDAQError_RecieveSerivesData = (-229776);
    public static final int ArtDAQError_SerivesResponseError = (-229775);

    public static final int ArtDAQError_PALCommunicationsFault = (-50401);
    public static final int ArtDAQError_PALDeviceInitializationFault = (-50303);
    public static final int ArtDAQError_PALDeviceNotSupported = (-50302);
    public static final int ArtDAQError_PALDeviceUnknown = (-50301);
    public static final int ArtDAQError_PALMemoryConfigurationFault = (-50350);
    public static final int ArtDAQError_PALResourceReserved = (-50103);
    public static final int ArtDAQError_PALFunctionNotFound = (-50255);
    public static final int ArtDAQError_PALOSFault = (-50202);

    public static final int ArtDAQError_BufferTooSmallForString = (-200228);
    public static final int ArtDAQError_ReadBufferTooSmall = (-200229);
    public static final int ArtDAQError_NULLPtr = (-200604);
    public static final int ArtDAQError_DuplicateTask = (-200089);
    public static final int ArtDAQError_InvalidTaskName = (-201340);
    public static final int ArtDAQError_InvalidDeviceName = (-201339);
    public static final int ArtDAQError_DeviceNotExist = (-200220);

    public static final int ArtDAQError_InvalidPhysChanString = (-201237);
    public static final int ArtDAQError_PhysicalChanDoesNotExist = (-200170);
    public static final int ArtDAQError_DevAlreadyInTask = (-200481);
    public static final int ArtDAQError_ChanAlreadyInTask = (-200489);
    public static final int ArtDAQError_ChanNotInTask = (-200486);
    public static final int ArtDAQError_DevNotInTask = (-200482);
    public static final int ArtDAQError_InvalidTask = (-200088);
    public static final int ArtDAQError_InvalidChannel = (-200087);
    public static final int ArtDAQError_InvalidSyntaxForPhysicalChannelRange = (-200086);
    public static final int ArtDAQError_MultiChanTypesInTask = (-200559);
    public static final int ArtDAQError_MultiDevsInTask = (-200558);
    public static final int ArtDAQError_PhysChanDevNotInTask = (-200648);
    public static final int ArtDAQError_RefAndPauseTrigConfigured = (-200628);
    public static final int ArtDAQError_ActivePhysChanTooManyLinesSpecdWhenGettingPrpty = (-200625);

    public static final int ArtDAQError_ActiveDevNotSupportedWithMultiDevTask = (-201207);
    public static final int ArtDAQError_RealDevAndSimDevNotSupportedInSameTask = (-201206);
    public static final int ArtDAQError_DevsWithoutSyncStrategies = (-201426);
    public static final int ArtDAQError_DevCannotBeAccessed = (-201003);
    public static final int ArtDAQError_SampleRateNumChansConvertPeriodCombo = (-200081);
    public static final int ArtDAQError_InvalidAttributeValue = (-200077);
    public static final int ArtDAQError_CanNotPerformOpWhileTaskRunning = (-200479);
    public static final int ArtDAQError_CanNotPerformOpWhenNoChansInTask = (-200478);
    public static final int ArtDAQError_CanNotPerformOpWhenNoDevInTask = (-200477);
    public static final int ArtDAQError_ErrorOperationTimedOut = (-200474);
    public static final int ArtDAQError_CannotSetPropertyWhenTaskRunning = (-200557);
    public static final int ArtDAQError_WriteFailsBufferSizeAutoConfigured = (-200547);
    public static final int ArtDAQError_CannotReadWhenAutoStartFalseAndTaskNotRunningOrCommitted = (-200473);
    public static final int ArtDAQError_CannotWriteWhenAutoStartFalseAndTaskNotRunningOrCommitted = (-200472);
    public static final int ArtDAQError_CannotWriteNotStartedAutoStartFalseNotOnDemandBufSizeZero = (-200802);
    public static final int ArtDAQError_CannotWriteToFiniteCOTask = (-201291);

    public static final int ArtDAQError_SamplesNotYetAvailable = (-200284);
    public static final int ArtDAQError_SamplesNoLongerAvailable = (-200279);
    public static final int ArtDAQError_SamplesWillNeverBeAvailable = (-200278);
    public static final int ArtDAQError_RuntimeAborted_Routing = (-88709);
    public static final int ArtDAQError_Timeout = (-26802);
    public static final int ArtDAQError_MinNotLessThanMax = (-200082);
    public static final int ArtDAQError_InvalidNumberSamplesToRead = (-200096);
    public static final int ArtDAQError_InvalidNumSampsToWrite = (-200622);

    public static final int ArtDAQError_DeviceNameNotFound_Routing = (-88717);
    public static final int ArtDAQError_InvalidRoutingSourceTerminalName_Routing = (-89120);
    public static final int ArtDAQError_InvalidTerm_Routing = (-89129);
    public static final int ArtDAQError_UnsupportedSignalTypeExportSignal = (-200375);

    public static final int ArtDAQError_ChanSizeTooBigForU16PortWrite = (-200879);
    public static final int ArtDAQError_ChanSizeTooBigForU16PortRead = (-200878);
    public static final int ArtDAQError_ChanSizeTooBigForU32PortWrite = (-200566);
    public static final int ArtDAQError_ChanSizeTooBigForU8PortWrite = (-200565);
    public static final int ArtDAQError_ChanSizeTooBigForU32PortRead = (-200564);
    public static final int ArtDAQError_ChanSizeTooBigForU8PortRead = (-200563);
    public static final int ArtDAQError_WaitUntilDoneDoesNotIndicateDone = (-200560);
    public static final int ArtDAQError_AutoStartWriteNotAllowedEventRegistered = (-200985);
    public static final int ArtDAQError_AutoStartReadNotAllowedEventRegistered = (-200984);
    public static final int ArtDAQError_EveryNSamplesAcqIntoBufferEventNotSupportedByDevice = (-200981);
    public static final int ArtDAQError_EveryNSampsTransferredFromBufferEventNotSupportedByDevice = (-200980);
    public static final int ArtDAQError_CannotRegisterArtDAQSoftwareEventWhileTaskIsRunning = (-200960);
    public static final int ArtDAQError_EveryNSamplesEventNotSupportedForNonBufferedTasks = (-200848);
    public static final int ArtDAQError_EveryNSamplesEventNotSupport = (-200849);
    public static final int ArtDAQError_BufferSizeNotMultipleOfEveryNSampsEventIntervalWhenDMA = (-200877);
    public static final int ArtDAQError_EveryNSampsTransferredFromBufferNotForInput = (-200965);
    public static final int ArtDAQError_EveryNSampsAcqIntoBufferNotForOutput = (-200964);
    public static final int ArtDAQError_ReadNoInputChansInTask = (-200460);
    public static final int ArtDAQError_WriteNoOutputChansInTask = (-200459);
    public static final int ArtDAQError_InvalidTimeoutVal = (-200453);
    public static final int ArtDAQError_AttributeNotSupportedInTaskContext = (-200452);
    public static final int ArtDAQError_FunctionNotSupportedForDevice = (-209876);
    public static final int ArtDAQError_NoMoreSpace = (-200293);
    public static final int ArtDAQError_SamplesCanNotYetBeWritten = (-200292);
    public static final int ArtDAQError_GenStoppedToPreventRegenOfOldSamples = (-200290);
    public static final int ArtDAQError_SamplesWillNeverBeGenerated = (-200288);
    public static final int ArtDAQError_CannotReadRelativeToRefTrigUntilDone = (-200281);
    public static final int ArtDAQError_ExtSampClkSrcNotSpecified = (-200303);
    public static final int ArtDAQError_CannotUpdatePulseGenProperty = (-200301);
    public static final int ArtDAQError_InvalidTimingType = (-200300);
    public static final int ArtDAQError_SampRateTooHigh = (-200332);
    public static final int ArtDAQError_SampRateTooLow = (-200331);

    public static final int ArtDAQError_InvalidAnalogTrigSrc = (-200265);
    public static final int ArtDAQError_TrigWhenOnDemandSampTiming = (-200262);
    public static final int ArtDAQError_RefTrigWhenContinuous = (-200358);
    public static final int ArtDAQError_SpecifiedAttrNotValid = (-200233);

    public static final int ArtDAQError_OutputBufferEmpty = (-200462);
    public static final int ArtDAQError_InvalidOptionForDigitalPortChannel = (-200376);

    public static final int ArtDAQError_CtrMinMax = (-200527);
    public static final int ArtDAQError_WriteChanTypeMismatch = (-200526);
    public static final int ArtDAQError_ReadChanTypeMismatch = (-200525);
    public static final int ArtDAQError_WriteNumChansMismatch = (-200524);
    public static final int ArtDAQError_OneChanReadForMultiChanTask = (-200523);

    public static final int ArtDAQError_MultipleCounterInputTask = (-200147);
    public static final int ArtDAQError_CounterStartPauseTriggerConflict = (-200146);
    public static final int ArtDAQError_CounterInputPauseTriggerAndSampleClockInvalid = (-200145);
    public static final int ArtDAQError_CounterOutputPauseTriggerInvalid = (-200144);
    public static final int ArtDAQError_FileNotFound = (-26103);
    public static final int ArtDAQError_FileCannotAccess = (-26104);

    public static final int ArtDAQError_NonbufferedOrNoChannels = (-201395);
    public static final int ArtDAQError_BufferedOperationsNotSupportedOnSelectedLines = (-201062);
    public static final int ArtDAQError_CalibrationFailed = (-200157);

    public static final int ArtDAQError_InvalidFillModeParameter = (-300001);

    public static final int ArtDAQError_PhysChanOutputType = (-200432);
    public static final int ArtDAQError_PhysChanMeasType = (-200431);
    public static final int ArtDAQError_InvalidPhysChanType = (-200430);

    public static final int ArtDAQError_SuitableTimebaseNotFoundTimeCombo2 = (-200746);
    public static final int ArtDAQError_SuitableTimebaseNotFoundFrequencyCombo2 = (-200745);
    public static final int ArtDAQError_HystTrigLevelAIMax = (-200425);
    public static final int ArtDAQError_HystTrigLevelAIMin = (-200421);

    public static final int ArtDAQErrorInvalidAttributeName = (-201086);
    public static final int ArtDAQError_ShuntCalFailedOutOfRange = (-201493);
    public static final int ArtDAQError_SelfCalFailedContactTechSupport = (-201386);

    public static final int ArtDAQWarning_ReturnedDataIsNotEnough = (30014);

}
