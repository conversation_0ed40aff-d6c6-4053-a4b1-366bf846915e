package com.desaysv.workserver.devices.bus.interfaces;

import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.utils.StrUtils.compareStrings;
import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.doubleToHexString;
import static com.desaysv.workserver.utils.StrUtils.removeParenthesesToDouble;

public interface ILinSequence {
    Logger log = LogManager.getLogger(ILinSequence.class.getSimpleName());
    int RETRY_TIMES = 1;
    int RETRY_INTERVAL = 1000;

    default String optimizeByteString(String byteString) {
        return byteString.replaceAll("\\s+", "");
    }


    /**
     * 设置LIN信号
     *
     * @param ecuNodeName ECU节点名称
     * @param signalName  信号名称
     * @param signalValue 信号值
     * @return 是否设置LIN信号成功
     */
    boolean setLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).CHANGE_LIN_SIGNAL"})
    default ActualExpectedResult setLinSignalInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //判断signalValueString是否是16进制0x开头的字符串,是就转成double类型
        boolean isHex = signalValueString.matches("^0x[0-9a-fA-F]+$") ? true : false;
        double signalValue = isHex ? Long.decode(signalValueString)
                : removeParenthesesToDouble(signalValueString);
        boolean pass = false;
        try {
            pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, signalValue);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setLinSignal", pass, signalValueString);
        return actualExpectedResult;
    }



    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).CHANGE_LIN_SIGNAL_HEX"})
    default ActualExpectedResult setLinSignalHexInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        return setLinSignalInfo(deviceChannel, ecuNodeName, messageName, signalName, signalValueString);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).RANDOM_LIN_SIGNAL"})
    default ActualExpectedResult setRandomLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String lowerSignalString, String upperSignalString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean isHexLowerSignal= lowerSignalString.matches("^0x[0-9a-fA-F]+$") ? true : false;
        boolean isHexUpperSignal = upperSignalString.matches("^0x[0-9a-fA-F]+$") ? true : false;
        double lowerSignal = isHexLowerSignal ? Long.decode(lowerSignalString)
                : removeParenthesesToDouble(lowerSignalString);
        double upperSignal = isHexUpperSignal ? Long.decode(upperSignalString)
                : removeParenthesesToDouble(upperSignalString);
        double signalValue = RandomUtil.randomDouble(lowerSignal, upperSignal);
        boolean pass = false;
        try {
            pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, signalValue);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setRandomLinSignal", pass, isHexLowerSignal && isHexUpperSignal ?  doubleToHexString(signalValue) : signalValue);
        return actualExpectedResult;
    }

    /**
     * 设置LIN信号步进值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param startSignalPhyValue 起始信号值
     * @param endSignalPhyValue   终止信号值
     * @param step                信号值步进值
     * @return 是否设置LIN信号步进值成功
     */

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).STEP_LIN_SIGNAL_WITHOUT_TIME"})
    default ActualExpectedResult setStepLinSignalWithoutTime(Integer deviceChannel, String ecuNodeName, String messageName,
                                                             String signalName, Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        double signalValue = Double.NaN;
        try {
            if (startSignalPhyValue <= endSignalPhyValue) {
                for (int val = startSignalPhyValue; val <= endSignalPhyValue; val += step) {
                    pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, val);
                    signalValue = val;
                }
            } else {
                for (int val = startSignalPhyValue; val >= endSignalPhyValue; val -= step) {
                    pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, val);
                    signalValue = val;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setStepLinSignalWithoutTime", pass, signalValue);
        return actualExpectedResult;
    }

    /**
     * 设置LIN信号步进值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param startSignalPhyValue 起始信号值
     * @param endSignalPhyValue   终止信号值
     * @param step                信号值步进值
     * @param stepInterval        信号值步进间隔
     * @return 是否设置LIN信号步进值成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).STEP_LIN_SIGNAL"})
    default ActualExpectedResult setStepLinSignalWithTime(Integer deviceChannel, String ecuNodeName, String messageName, String signalName,
                                                          Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step, String stepInterval) {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(stepInterval);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        double signalValue = Double.NaN;
        try {
            if (startSignalPhyValue <= endSignalPhyValue) {
                for (int val = startSignalPhyValue; val <= endSignalPhyValue; val += step) {
                    pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, val);
                    signalValue = val;
                    BaseRegexRule.sleepSeconds(seconds.longValue());
                }
            } else {
                for (int val = startSignalPhyValue; val >= endSignalPhyValue; val -= step) {
                    pass = setLinSignal(deviceChannel, ecuNodeName, messageName, signalName, val);
                    signalValue = val;
                    BaseRegexRule.sleepSeconds(seconds.longValue());
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setStepLinSignalWithTime", pass, signalValue);
        return actualExpectedResult;
    }

    boolean setLinSingleMsgControl(Integer deviceChannel, String ecuNodeName, String messageID, int messageStatus) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).CHANGE_LIN_MSG"})
    default ActualExpectedResult setLinSingleMsgControlInfo(Integer deviceChannel, String ecuNodeName, String messageID, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setLinSingleMsgControl(deviceChannel,ecuNodeName, messageID, messageStatus);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setLinSingleMsgControlInfo", pass, messageStatus);
        return actualExpectedResult;
    }

    boolean setLinAllMsgStatus(String ecuNodeName, int status) throws BusError;

    boolean setLinChannelMsgStatus(Integer deviceChannel, int status) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).CHANGE_ALL_ECU_LIN_MSG"})
    default ActualExpectedResult setLinEcuMsgStatusInfo(String ecuNodeName, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setLinAllMsgStatus(ecuNodeName, messageStatus);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setLinEcuMsgStatusInfo", pass, messageStatus);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).CHANGE_ALL_CHANNEL_LIN_MSG"})
    default ActualExpectedResult setLinChannelMsgStatusInfo(Integer deviceChannel, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setLinChannelMsgStatus(deviceChannel, messageStatus);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setLinChannelMsgStatusInfo", pass, messageStatus);
        return actualExpectedResult;
    }

    double fetchLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).COMPARE_LIN_SIGNAL"})
    default ActualExpectedResult compareLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //判断signalValueString是否是16进制0x开头的字符串,是就转成double类型
        boolean isHex = signalValueString.matches("^0x[0-9a-fA-F]+$") ? true : false;
        double signalValue = isHex ? Long.decode(signalValueString)
                : removeParenthesesToDouble(signalValueString);
        double linSignal = Double.NaN;
        try {
            linSignal = fetchLinSignal(deviceChannel, ecuNodeName, messageName, signalName);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("LIN报文信号期望值:{},实际获取信号值:{},检测结果:{},耗时:{}", signalValueString, linSignal, linSignal == signalValue ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("compareCanSignal", linSignal == signalValue, isHex ? doubleToHexString(linSignal) : linSignal);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).COMPARE_LIN_SIGNAL_HEX"})
    default ActualExpectedResult compareLinSignalHex(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        return compareLinSignal(deviceChannel, ecuNodeName, messageName, signalName, signalValueString);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule).COMPARE_LIN_SIGNAL_RANGE"})
    default ActualExpectedResult compareLinSignalRange(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String lowerSignalString, String upperSignalString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        double lowerSignal = removeParenthesesToDouble(lowerSignalString);
        double upperSignal = removeParenthesesToDouble(upperSignalString);
        double linSignal = Double.NaN;
        try {
            linSignal = fetchLinSignal(deviceChannel, ecuNodeName, messageName, signalName);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = linSignal >= lowerSignal && linSignal <= upperSignal;
        log.info("LIN报文信号值期望范围:{}~{},实际获取信号值:{},检测结果:{},耗时:{}", lowerSignal, upperSignal, linSignal, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("compareLinSignalRange", pass, linSignal);
        return actualExpectedResult;
    }

    /**
     * 写入Lin PTS
     *
     * @param deviceChannel   Lin通道
     * @param ecuNodeName     ECU节点名称
     * @param byteInstruction 8个字节的指令
     * @return 是否设置Lin PTS成功
     */
    boolean setLinPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError;

    boolean sendLinMessage(Integer deviceChannel, String messageId, String byteInstruction) throws BusError;

    default boolean isFailReTry() {
        return false;
    }
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).CHANGE_LIN_PTS_BY_MESSAGE_ID"})
    default ActualExpectedResult setLinPTSInfoByMessageId(Integer deviceChannel, String messageId, String byteInstruction, String checkedContext) {
        return setLinPTSInfo(deviceChannel, null, messageId, byteInstruction, checkedContext);
    }
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).CHANGE_LIN_PTS"})
    default ActualExpectedResult setLinPTSInfo(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        long startMills = System.currentTimeMillis();
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setLinPTS(deviceChannel, ecuNodeName, messageId, optimizeByteString(byteInstruction), checkedContext);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanPTS", pass, "");
        log.info("设置Lin PTS响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setLinWakeUp(Integer deviceChannel, int wakeUpCommand) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).LIN_WAKE_UP"})
    default ActualExpectedResult setLinWakeUpInfo(Integer deviceChannel, int wakeUpCommand) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        long startMills = System.currentTimeMillis();
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setLinWakeUp(deviceChannel, wakeUpCommand);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setLinWakeUpInfo", pass, "");
        log.info("设置Lin唤醒：{}, 响应事件耗时:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    String readLinDataByIdHex(Integer deviceChannel, String messageId);

    String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).COMPARE_LIN_PTS_WITH_MESSAGE_ID"})
    default ActualExpectedResult compareLinPTS(Integer deviceChannel, String messageId, String byteInstruction) throws BusError  {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String rxByteInstruction = "";
        for (int i = 0; i < RETRY_TIMES; i++) {
            rxByteInstruction = fetchLinPTS(deviceChannel, messageId);
            if ("NA".equals(byteInstruction)) {
                pass = byteInstruction.equals(rxByteInstruction);
            } else {
                pass = !rxByteInstruction.isEmpty() && compareStrings(byteInstruction, rxByteInstruction, 'X');
            }
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }

        log.info("Lin PTS报文期望值:{},实际获取报文:{},检测结果:{}", byteInstruction, rxByteInstruction, pass ? "通过" : "失败");
        actualExpectedResult.put("compareLinPTSWithID", pass, rxByteInstruction);
        log.info("获取Lin PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean fetchLinMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError;
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).EXIST_LIN_MESSAGE_ID"})
    default ActualExpectedResult compareExistLinMessageId(Integer deviceChannel, String messageId) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = fetchLinMsgID(deviceChannel, messageId, true);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("期望LIN总线上出现指定报文ID:{}, 检测结果:{}", messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareExistLinMessageId", pass, pass ? messageId : "NA");
        log.info("LIN总线上出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).NOT_EXIST_LIN_MESSAGE_ID"})
    default ActualExpectedResult compareNotExistLinMessageId(Integer deviceChannel, String messageId) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = fetchLinMsgID(deviceChannel, messageId, false);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("期望LIN总线上不出现指定报文ID:{}, 检测结果:{}", messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistLinMessageId", pass, "NA");
        log.info("LIN总线上不出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean lastCheckLinMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).LAST_CHECK_EXIST_LIN_MESSAGE_ID"})
    default ActualExpectedResult lastCheckExistLinMessageIdInfo(Integer deviceChannel, String messageId, Integer milliSecond) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = lastCheckLinMsgID(deviceChannel, messageId, true, milliSecond);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("持续{}ms检测LIN总线上出现指定报文ID:{}, 检测结果:{}", milliSecond, messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistLinMessageId", pass, "NA");
        log.info("持续检测LIN总线上出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LinRegexRule.PTS).LAST_CHECK_NOT_EXIST_LIN_MESSAGE_ID"})
    default ActualExpectedResult lastCheckNotExistLinMessageIdInfo(Integer deviceChannel, String messageId, Integer milliSecond) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = lastCheckLinMsgID(deviceChannel, messageId, false, milliSecond);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("持续{}ms检测LIN总线上不出现指定报文ID:{}, 检测结果:{}", milliSecond, messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistLinMessageId", pass, "NA");
        log.info("持续检测LIN总线上不出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


}