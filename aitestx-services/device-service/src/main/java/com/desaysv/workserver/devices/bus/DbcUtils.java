package com.desaysv.workserver.devices.bus;

import cantools.dbc.DbcReader;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DbcUtils {

    private final static Map<String, DbcReader> dbcReaderMap = new HashMap<>();

    public static List<DbcReader> getDbcReaders(List<String> dbcPaths) {
        List<DbcReader> dbcReaders = new ArrayList<>();
        for (String dbcPath : dbcPaths) {
            if (dbcReaderMap.containsKey(dbcPath)) {
                dbcReaders.add(dbcReaderMap.get(dbcPath));
            } else {
                log.info("解析dbc文件:{}", dbcPath);
                DbcReader dbcReader = new DbcReader();
                dbcReader.parseFile(new File(dbcPath));
                dbcReaderMap.put(dbcPath, dbcReader);
                dbcReaders.add(dbcReader);
            }
        }
        return dbcReaders;
    }

    public static DbcReader getDbcReader(String dbcPath) {
        if (dbcReaderMap.containsKey(dbcPath)) {
            return dbcReaderMap.get(dbcPath);
        }
        log.info("解析dbc文件:{}", dbcPath);
        DbcReader dbcReader = new DbcReader();
        dbcReader.parseFile(new File(dbcPath));
        dbcReaderMap.put(dbcPath, dbcReader);
        return dbcReader;
    }
}
