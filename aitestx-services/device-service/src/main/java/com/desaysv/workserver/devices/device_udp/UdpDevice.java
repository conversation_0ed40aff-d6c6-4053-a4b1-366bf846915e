package com.desaysv.workserver.devices.device_udp;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.device_udp.config.UdpConfig;
import com.desaysv.workserver.devices.device_udp.interfaces.IDeviceUDP;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.hexStringToBytes;
import static com.desaysv.workserver.utils.ByteUtils.bytesToHexString;
import static com.desaysv.workserver.utils.DateUtils.decodeDate;

@Slf4j
public class UdpDevice extends Device implements IDeviceUDP {
    private CommandUdpClient udpClient;
    private byte[] responseBytes;
    private UdpConfigService udpConfigService;
    private XcpCommander xcpCommander;
    private final byte[] didRwFlagBytes = new byte[]{0x01};
    private static final String DID_WRITE_DATA = "DID_Write_Data";
    private static final Map<String, byte[]> didRwItemsMap = new HashMap<>();
    private static final Map<String, Integer> didWriteDataSizeMap = new HashMap<>();

    static {
        didRwItemsMap.put("0xF187", new byte[]{0x01});
        didRwItemsMap.put("0xF18A", new byte[]{0x02});
        didRwItemsMap.put("0xF18C", new byte[]{0x03});
        didRwItemsMap.put("0xF18B", new byte[]{0x04});
        didRwItemsMap.put("0xF193", new byte[]{0x05});
        didRwItemsMap.put("0x03C1", new byte[]{0x06});
        didWriteDataSizeMap.put("0xF187", 15);
        didWriteDataSizeMap.put("0xF18A", 10);
        didWriteDataSizeMap.put("0xF18C", 14);
        didWriteDataSizeMap.put("0xF18B", 3);
        didWriteDataSizeMap.put("0xF193", 2);
        didWriteDataSizeMap.put("0x03C1", 2);
    }

    public UdpDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_UDP;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.UdpDevice.UDP_DEVICE;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String deviceName = getDeviceName();
        String[] str = deviceName.split(":");
        String ip = str[0];
        String port = str[1];
        udpClient = CommandUdpClient.getInstance();
        udpClient.setServerIP(ip);
        udpClient.setServerPort(Integer.parseInt(port));
        xcpCommander = new XcpCommander();
        return connect();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public Map<String, DIDInfo> getDIDInfo(String items) {
        String variableName = String.format("DID%s_Data", items.replace("0x", ""));
        byte[] receiveData = getReceiveData(variableName, didWriteDataSizeMap.get(items));
        String parseData = parseCommandArrayByItems(items, receiveData);
        Map<String, DIDInfo> didInfoMap = new HashMap<>();
        didInfoMap.put(items, new DIDInfo(receiveData, parseData));
        return didInfoMap;
    }

    @Override
    public boolean setDIDInfo(String items, String command) {
        if (!setCalibrationPage()) return false;
        if (!setDidRwFlag()) return false;
        byte[] commandByteArray = getCommandArrayByItems(items, command);
        if (!setDIDCommand(items, commandByteArray)) return false;
        return setDidRwItems(items);
    }

    private byte[] getCommandArrayByItems(String items, String command) {
        byte[] commandByteArray = null;
        switch (items) {
            case "0xF187":
                if (command.contains("_")) {
                    command = command.replace("_", "-");
                }
                commandByteArray = ByteUtils.toPaddedByteArray(command, 15, StandardCharsets.US_ASCII,
                        (byte) 0x20);
                break;
            case "0xF18A":
                commandByteArray = ByteUtils.toPaddedByteArray(command, 10, StandardCharsets.US_ASCII,
                        (byte) 0x20);
                break;
            case "0xF18C":
                commandByteArray = ByteUtils.toPaddedByteArray(command, 14, StandardCharsets.US_ASCII,
                        (byte) 0x20);
                break;
            case "0xF18B":
                commandByteArray = ByteUtils.dateToBytes(command, 3);
                break;
            case "0xF193":
            case "0x03C1":
                commandByteArray = hexStringToBytes(command);
                break;
            default:
                break;
        }
        return commandByteArray;
    }

    public static String parseCommandArrayByItems(String items, byte[] commandByteArray) {
        String commandResultString = null;
        switch (items) {
            case "0xF187":
            case "0xf187":
            case "0xF18A":
            case "0xf18a":
            case "0xF18C":
            case "0xf18c":
                commandResultString = ByteUtils.bytesToStringWithPadRemoval(commandByteArray, (byte) 0x20, StandardCharsets.US_ASCII);
                commandResultString = commandResultString.replace("-", "_");
                break;
            case "0xF18B":
            case "0xf18b":
                commandResultString = decodeDate(commandByteArray);
                break;
            case "0xF193":
            case "0xf193":
            case "0x03C1":
            case "0x03c1":
            case "0xF195":
            case "0xf195":
            case "0xF14A":
            case "0xf14a":
            case "0xF14B":
            case "0xf14b":
            case "0xF15F":
            case "0xf15f":
            case "0xF1A8":
            case "0xf1b8":
            case "0xF026":
            case "0xf026":
                commandResultString = bytesToHexString(commandByteArray);
                break;
            default:
                break;
        }
        return commandResultString;
    }

    private boolean setDIDCommand(String items, byte[] commandByteArray) {
        VariableInfo variableInfo = getVariableInfoByName(DID_WRITE_DATA);
        if (variableInfo == null) {
            log.info("在A2L文件中未找到变量名：{}", DID_WRITE_DATA);
            return false;
        }
        return sendCommandAndReceive(DID_WRITE_DATA, commandByteArray, didWriteDataSizeMap.get(items));
    }

    private boolean setDidRwItems(String items) {
        return startSendCommandAndReceiveData("did_rw_items", didRwItemsMap.get(items));
    }

    private boolean setDidRwFlag() {
        return startSendCommandAndReceiveData("did_rw_flag", didRwFlagBytes);
    }

    @Override
    public boolean sendCommandAndReceiveData(String variableName, byte[] command) {
        if (!setCalibrationPage()) return false;
        return startSendCommandAndReceiveData(variableName, command);
    }

    public boolean startSendCommandAndReceiveData(String variableName, byte[] command) {
        VariableInfo variableInfo = getVariableInfoByName(variableName);
        if (variableInfo == null) {
            log.info("在A2L文件中未找到变量名：{}", variableName);
            return false;
        }
        String dataType = variableInfo.getDataType();
        int variableSize = VariableSizeDict.getVariableSize(dataType);
        return sendCommandAndReceive(variableName, command, variableSize);
    }


    public boolean sendCommandAndReceive(String variableName, byte[] command, int size) {
        VariableInfo variableInfo = getVariableInfoByName(variableName);
        String address = variableInfo.getAddress();
        if (address != null) {
            log.info("--------------------开始标定------------------------");
            log.info("设置变量名：{}, A2L文件中的地址：{}", variableName, address);
            boolean pass = setAddressCommand(address);
            if (!pass) return false;
            byte[] writeCommand = xcpCommander.buildWritePacket(size, command);
            byte[] fullCommand = xcpCommander.wrapCommand(writeCommand);
            log.info(">>>>>开始执行写入标定量指令");
            byte[] responseBytes = xcpCommander.sendCommand(fullCommand);
            boolean successResponse = xcpCommander.isSuccessResponse(responseBytes);
            log.info("--------------------结束标定------------------------");
            return successResponse;
        }
        return true;
    }

    @Override
    public byte[] getReceiveData(String variableName) {
        VariableInfo variableInfo = getVariableInfoByName(variableName);
        if (variableInfo == null) {
            log.info("在A2L文件中未找到变量名：{}", variableName);
            return null;
        }
        String dataType = variableInfo.getDataType();
        int variableSize = VariableSizeDict.getVariableSize(dataType);
        return getReceiveData(variableName, variableSize);
    }

    @Override
    public boolean reconnectUdpDevice(int tryTimes) {
        if (tryTimes <= 0) return false;
        int attempts = 0;
        while (attempts < tryTimes) {
            try {
                if (open()) return true;
                attempts++;
                Thread.sleep(500);
            } catch (DeviceOpenException | DeviceOpenRepeatException e) {
                log.error("Recoverable error: {}", e.getMessage()); // 可考虑继续重试
                attempts++; // 继续消耗尝试次数
            } catch (InterruptedException e) {
                log.warn("Thread interrupted during reconnection");
                Thread.currentThread().interrupt(); // 恢复中断状态
                return false; // 立即终止
            }
        }
        return false;
    }

    public byte[] getReceiveData(String variableName, int size) {
        VariableInfo variableInfo = getVariableInfoByName(variableName);
        if (variableInfo == null) {
            log.info("在A2L文件中未找到变量名：{}", variableName);
            return null;
        }
        String address = variableInfo.getAddress();
        if (address != null) {
            log.info("--------------------开始回采------------------------");
            log.info("获取变量名：{}, A2L文件中的地址：{}", variableName, address);
            boolean pass = setAddressCommand(address);
            if (!pass) return null;
            byte[] readCommand = xcpCommander.buildReadPacket(size);
            byte[] fullCommand = xcpCommander.wrapCommand(readCommand);
            log.info(">>>>>开始执行读取观测量指令");
            byte[] responseBytes = xcpCommander.sendCommand(fullCommand);
            byte[] response = xcpCommander.parseResponse(responseBytes);
            log.info("--------------------结束回采------------------------");
            return response;
        }
        return null;
    }


    private VariableInfo getVariableInfoByName(String variableName) {
        UdpConfig udpConfig = SpringContextHolder.getBean(UdpConfigService.class).getUdpConfig();
        if (udpConfig == null) return null;
        Map<String, VariableInfo> variableInfoMap = udpConfig.getVariables();
        if (variableInfoMap != null) {
            VariableInfo variableInfo = variableInfoMap.get(variableName);
            return variableInfo;
        }
        return null;
    }

    // XCP协议连接命令
    public boolean connect() {
        byte[] xcpCommand = xcpCommander.buildXcpCommandPacket(new byte[]{(byte) 0xFF});
        byte[] fullCommand = xcpCommander.wrapCommand(xcpCommand);
        byte[] responseBytes = xcpCommander.sendCommand(fullCommand);
        boolean successResponse = xcpCommander.isSuccessResponse(responseBytes);
        if (!successResponse) {
            log.info("XCP连接失败");
        } else {
            log.info("XCP连接成功");
        }
        return successResponse;
    }

    // 设定标定⻚，切换工作簿
    public boolean setCalibrationPage() {
        byte[] xcpCommand = xcpCommander.buildXcpCommandPacket(new byte[]{(byte) 0xEB, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00});
        byte[] fullCommand = xcpCommander.wrapCommand(xcpCommand);
        byte[] responseBytes = xcpCommander.sendCommand(fullCommand);
        boolean successResponse = xcpCommander.isSuccessResponse(responseBytes);
        if (!successResponse) {
            log.info("XCP标定⻚切换失败");
        } else {
            log.info("XCP标定⻚切换成功");
        }
        return successResponse;
    }

    // 设置操作地址
    public boolean setAddressCommand(String address) {
        byte[] xcpCommand = xcpCommander.buildAddressPacket(address);
        byte[] fullCommand = xcpCommander.wrapCommand(xcpCommand);
        log.info(">>>>>开始执行设置地址指令");
        byte[] responseBytes = xcpCommander.sendCommand(fullCommand);
        boolean successResponse = xcpCommander.isSuccessResponse(responseBytes);
        if (!successResponse) {
            log.info("设置地址失败");
        } else {
            log.info("设置地址成功");
        }
        return successResponse;
    }


}
