package com.desaysv.workserver.devices.autoclicker;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.invoker.OperationResult;

public interface IAutoClicker {

    boolean clickOneChannelStart(AutoClickerDataPackage autoClickerDataPackage) throws InterruptedException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AutoClickerRegexRule).CLICK_ONE_CHANNEL"})
    default boolean clickOneChannelStart(int num, int hold, int interval, int times) throws InterruptedException {
        if (0 <= num && num <= 12 && hold > 0 && interval > 0 && times > 0) {
            AutoClickerDataPackage autoClickerDataPackage = new AutoClickerDataPackage();
            autoClickerDataPackage.setNum(num);
            autoClickerDataPackage.setHold(hold);
            autoClickerDataPackage.setInterval(interval);
            autoClickerDataPackage.setTimes(times);
            return clickOneChannelStart(autoClickerDataPackage);
        } else {
            return false;
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AutoClickerRegexRule).CLICK_TOUCH"})
    OperationResult channelDown( int channel);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AutoClickerRegexRule).CLICK_RELEASE"})
    OperationResult channelUp(int channel);

    OperationResult autoClickCheckPoint(AutoClickerCheckPoint autoClickerCheckPoint);

}
