package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.electric_relay.*;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractElectricRelayFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class ElectricRelayFactory implements AbstractElectricRelayFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.ElectricRelay.JYDAM1600C:
                return createJYDAM1600CDevice(deviceRegisterForm);
            case DeviceModel.ElectricRelay.ZQWL_IO_1BXRC32:
                return createJZQWL_IO_1BXRC32Device(deviceRegisterForm);
            case DeviceModel.ElectricRelay.ZQWL_IO_1CX5R4:
                return createZQWL_IO_1CX5R4Device(deviceRegisterForm);
            case DeviceModel.ElectricRelay.ZQWL_IO_1BX3C8:
                return createZQWL_IO_1BX3C8Device(deviceRegisterForm);
            case DeviceModel.ElectricRelay.ZQWL_IO_1CNRR4:
                return createZQWL_IO_1CNRR4Device(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createJYDAM1600CDevice(DeviceRegisterForm deviceRegisterForm) {
        return new JYDAM1600CDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createJZQWL_IO_1BXRC32Device(DeviceRegisterForm deviceRegisterForm) {
        return new ZQWLIO1BXRC32Device(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createZQWL_IO_1CX5R4Device(DeviceRegisterForm deviceRegisterForm) {
        return new ZQWLIO1CX5R4Device(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createZQWL_IO_1BX3C8Device(DeviceRegisterForm deviceRegisterForm) {
        return new ZQWLIO1BX3C8Device(deviceRegisterForm.getDeviceOperationParameter());
    }

    private Device createZQWL_IO_1CNRR4Device(DeviceRegisterForm deviceRegisterForm) {
        return new ZQWLIO1CNRR4Device(deviceRegisterForm.getDeviceOperationParameter());
    }
}
