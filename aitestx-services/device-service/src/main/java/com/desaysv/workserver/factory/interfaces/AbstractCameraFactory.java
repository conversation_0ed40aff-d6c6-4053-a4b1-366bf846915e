package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 14:19
 * @description : 抽象相机工厂
 * @modified By :
 * @since : 2022-4-12
 */
public interface AbstractCameraFactory extends AbstractDeviceCreator {

    Device createUSBCamera(DeviceRegisterForm deviceRegisterForm);

    Device createBaslerCamera(DeviceRegisterForm deviceRegisterForm);

    Device createHikCamera(DeviceRegisterForm deviceRegisterForm);
}
