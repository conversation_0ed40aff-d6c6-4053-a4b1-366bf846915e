package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.TestBoardBoxUtils;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

public interface ILightVoltageAcquireBoard {
    Logger log = LogManager.getLogger(ILightVoltageAcquireBoard.class.getSimpleName());

    String VOLTAGE = "voltage";

    /**
     * 采集电压（180个通道）
     *
     * @return Map(板卡号, 电压数组)
     */
    List<Float> fetchVoltageByChannel(int chassisNumber, int slotNumber, int channelNumber) throws BoardCardTransportException;

    boolean setVoltAcquisitionBoardCardInit();

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).LIGHT_VOLTAGE_ACQUIRE"})
    default ActualExpectedResult fetchVoltageBoardCard(Integer deviceChannel, Integer slotNumber, Integer channelNumber, float voltageExpectation, float voltageDeviation) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("采集电压板卡{}-{}-{}，期望电压{}V, 要求偏差不超过{}%", deviceChannel, slotNumber, channelNumber, voltageExpectation, voltageDeviation);
        try {
            List<Float> actualVoltageList = fetchVoltageByChannel(deviceChannel, slotNumber, channelNumber);
            if (actualVoltageList == null || actualVoltageList.isEmpty()) {
                throw new OperationFailNotification("采集电压板卡读取失败");
            }
            float actualVoltage = actualVoltageList.get(0);
            actualExpectedResult.put(VOLTAGE, true, String.format("%.2fV", actualVoltage));
            if (!TestBoardBoxUtils.isWithinDeviation(actualVoltage, voltageExpectation, voltageDeviation)) {
                actualExpectedResult.put(VOLTAGE, false);
                String info = String.format("采集电压板卡实际电压%fV不在允许偏差范围内", actualVoltage);
                ActionSequencesLoggerUtil.info(info);
            } else {
                String info = String.format("采集电压板卡实际电压%fV在允许偏差范围内", actualVoltage);
                ActionSequencesLoggerUtil.info(info);
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("采集电压板卡读取失败:" + e.getMessage());
        }
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).LIGHT_VOLTAGE_ACQUIRE_RANGE"})
    default ActualExpectedResult compareVoltageBoardCardRange(Integer deviceChannel, Integer slotNumber, Integer channelNumber, float lowerVoltageExpectation, float upperVoltageExpectation) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("采集电压板卡{}-{}-{}，期望电压{}~{}V", deviceChannel, slotNumber, channelNumber, lowerVoltageExpectation, upperVoltageExpectation);
        try {
            List<Float> actualVoltageList = fetchVoltageByChannel(deviceChannel, slotNumber, channelNumber);
            if (actualVoltageList == null || actualVoltageList.isEmpty()) {
                throw new OperationFailNotification("采集电压板卡读取失败");
            }
            float actualVoltage = actualVoltageList.get(0);
            boolean pass = actualVoltage >= lowerVoltageExpectation && actualVoltage <= upperVoltageExpectation;
            actualExpectedResult.put(VOLTAGE, pass, String.format("%.2fV", actualVoltage));
            String info = String.format("采集电压板卡实际采集电压：%fV", actualVoltage);
            log.info(info);
            ActionSequencesLoggerUtil.info(info);
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("采集电压板卡读取失败:" + e.getMessage());
        }
        return actualExpectedResult;
    }

    boolean setVoltageBoardCardStatus(int chassisNumber, int slotNumber, int channelNumber, int status) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).LIGHT_VOLTAGE_OPERATION"})
    default ActualExpectedResult setVoltageBoardCardStatusInfo(Integer deviceChannel, Integer slotNumber, Integer channelNumber, int status) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //如果status不等于0或者1，则抛异常，无效状态
        if (status != 0 && status != 1) {
            throw new OperationFailNotification("无效的电平状态");
        }
        log.info("采集电压板卡{}-{}-{}，设置电平状态:{}", deviceChannel, slotNumber, channelNumber, status == 0 ? "悬空" : "高电平");
        try {
            boolean pass = setVoltageBoardCardStatus(deviceChannel, slotNumber, channelNumber, status);
            actualExpectedResult.put(VOLTAGE, pass, status);
            String info = String.format("采集电压板卡通道：%d， 设置电平状态：%s", channelNumber, status == 0 ? "悬空" : "高电平");
            log.info(info);
            ActionSequencesLoggerUtil.info(info);
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("采集电压板卡设置电平状态失败:" + e.getMessage());
        }
        return actualExpectedResult;
    }
}
