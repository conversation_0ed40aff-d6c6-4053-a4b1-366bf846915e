package com.desaysv.workserver.screen.consumers;

import com.desaysv.workserver.MonitorType;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.screen.ScreenService;
import com.desaysv.workserver.screen.TouchPointUtils;
import com.desaysv.workserver.screen.config.TouchPointMatrixConfig;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.utils.ArrayUtils;

import java.util.List;
import java.util.function.Consumer;

/**
 * 报点监听线程
 */
public class KeepAliveTouchPointDataConsumer implements Consumer<byte[]> {

    private final DeviceDataDispatcher<List<PointInt>> deviceDataDispatcher;

    private final ScreenService screenService;

    private byte[] remainByteArray;

    public KeepAliveTouchPointDataConsumer(DeviceDataDispatcher<List<PointInt>> deviceDataDispatcher,
                                           ScreenService screenService) {
        this.deviceDataDispatcher = deviceDataDispatcher;
        this.screenService = screenService;
        remainByteArray = new byte[0];
    }

    @Override
    public void accept(byte[] bytes) {
        final TouchPointMatrixConfig touchPointMatrixConfig = screenService.getScreenConfig().getTouchPointMatrix();
        if (touchPointMatrixConfig.invalid()) {
            return;
        }
        byte[] concatByteArray = ArrayUtils.concat(remainByteArray, bytes);
        TouchPointUtils.TouchPointCollection touchPointCollection =
                TouchPointUtils.parseByteArrayToTouchPoints(concatByteArray, touchPointMatrixConfig, false);
        remainByteArray = touchPointCollection.getRemainByteArray();
        //FIXME：删除积累的符合长度但不符合报点协议的byte数据
        deviceDataDispatcher.product(MonitorType.TOUCH_POINT_DATA, TouchPointUtils.parseTouchPointToPointList(touchPointCollection.getTouchPointList(), touchPointMatrixConfig));
    }
}
