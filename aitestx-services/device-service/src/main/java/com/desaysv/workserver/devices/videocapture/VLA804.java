package com.desaysv.workserver.devices.videocapture;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.*;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.AsynchronousSocketChannel;
import java.nio.channels.CompletionHandler;
import java.util.Arrays;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class VLA804 {
    private static final int MAX_RETRIES = 1;
    private static final int RETRY_DELAY = 1000;
    private static final int SOCKET_TIMEOUT = 1000;
    private static final int BUFFER_SIZE = 1024;

    private AsynchronousSocketChannel socketChannel;
    private final String ipAddress;
    private final int port;
    @Getter
    private int imageWidth = 0;
    @Getter
    private int imageHeight = 0;
    private int imageLength = 0;
    private final ReentrantLock lock = new ReentrantLock();
    private volatile BufferedImage sharedImage;
    private final ExecutorService executor = Executors.newCachedThreadPool();

    public VLA804(String ipAddress, int port) {
        this.ipAddress = ipAddress;
        this.port = port;
    }

    public CompletableFuture<Void> connect() {
        return CompletableFuture.runAsync(() -> {
            for (int i = 0; i < MAX_RETRIES; i++) {
                try {
                    socketChannel = AsynchronousSocketChannel.open();
                    Future<Void> connectFuture = socketChannel.connect(new InetSocketAddress(ipAddress, port));
                    connectFuture.get(SOCKET_TIMEOUT, TimeUnit.MILLISECONDS);
                    return;
                } catch (Exception e) {
                    log.warn("连接失败，重新连接中... ({} / {})", (i + 1), MAX_RETRIES, e);
                    try {
                        Thread.sleep(RETRY_DELAY);
                    } catch (InterruptedException ex) {
                        try {
                            throw new ConnectionException("线程中断", ex);
                        } catch (ConnectionException exc) {
                            throw new RuntimeException(exc);
                        }
                    }
                }
            }
            throw new RuntimeException("重试" + MAX_RETRIES + "次后仍然失败。");
        }, executor);
    }


    public CompletableFuture<Void> disconnect() {
        return CompletableFuture.runAsync(() -> {
            try {
                if (socketChannel != null && socketChannel.isOpen()) {
                    socketChannel.close();
                }
            } catch (IOException e) {
                log.error("断开连接失败", e);
                throw new RuntimeException(e);
            }
        }, executor);
    }

    public boolean isConnected() {
        return socketChannel != null && socketChannel.isOpen();
    }

    public CompletableFuture<Void> getFirmwareVersion() {
        byte[] command = new byte[]{(byte) 0xAA, (byte) 0x84, 0x00, 0x02, 0x00};
        return sendCommandAndReceiveResponse(command, BUFFER_SIZE).thenAccept(response -> {
            if (response.length >= 4 && response[3] == 0x00) {
                if (response.length >= 35) {
                    byte[] firmwareVersionBytes = Arrays.copyOfRange(response, 5, 37);
                    String firmwareVersion = new String(firmwareVersionBytes);
                    log.info("固件版本: {}", firmwareVersion);
                } else {
                    log.warn("固件版本的响应长度不正确。");
                }
            } else {
                handleErrorResponse(response);
            }
        });
    }

    public CompletableFuture<Void> readInputImageStatus() {
        byte[] command = new byte[]{(byte) 0xAA, (byte) 0x84, 0x00, (byte) 0x12, 0x00};
        return sendCommandAndReceiveResponse(command, BUFFER_SIZE).thenAccept(fullResponse -> {
            if (fullResponse[3] == 0x00) {
                if (fullResponse[4] == 0x2B) {
                    String[] hexStrings = new String[fullResponse.length];
                    for (int i = 0; i < fullResponse.length; i++) {
                        hexStrings[i] = String.format("%02X", fullResponse[i]);
                    }
                    int width = Integer.parseInt(hexStrings[6] + hexStrings[7], 16);
                    int height = Integer.parseInt(hexStrings[8] + hexStrings[9], 16);
                    imageWidth = width;
                    imageHeight = height;
                    imageLength = width * height * 3;
                    log.info("图像宽度: {}，高度: {}", width, height);
                } else {
                    handleErrorResponse(fullResponse);
                    throw new RuntimeException("响应数据长度不正确。");
                }
            } else {
                handleErrorResponse(fullResponse);
                throw new RuntimeException("获取图像状态失败。");
            }
        });
    }

    public boolean readCapturedImage(String filePath) {
        lock.lock();
        try {
            if (!isConnected()) {
                reconnect().join(); // 确保连接已建立
            }
            if (imageWidth == 0 || imageHeight == 0) {
                readInputImageStatus().join(); // 确保图像状态已读取
            }
            if (imageLength != 0) {
                byte[] command = new byte[]{(byte) 0xAA, (byte) 0x84, 0x00, (byte) 0x15, 0x00};
                CompletableFuture<byte[]> responseFuture = sendCommandAndReceiveResponse(command, imageLength);
                byte[] fullResponse;
                try {
                    fullResponse = responseFuture.get(SOCKET_TIMEOUT, TimeUnit.MILLISECONDS);
                } catch (TimeoutException e) {
                    log.warn("读取图像超时，重新读取图像状态");
                    readInputImageStatus().join();
                    return false;
                }
                if (fullResponse.length == imageLength) {
                    BufferedImage image = getBufferedImage(imageWidth, imageHeight, fullResponse);
                    ImageIO.write(image, "png", new File(filePath));
                    return true;
                } else {
                    log.warn("响应数据长度不正确，预期长度: {}，实际长度: {}", imageLength, fullResponse.length);
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("捕获图像失败", e);
            return false;
        } finally {
            lock.unlock();
        }
    }


    private static BufferedImage getBufferedImage(int width, int height, byte[] fullResponse) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_3BYTE_BGR);
        System.arraycopy(fullResponse, 0,
                ((DataBufferByte) image.getRaster().getDataBuffer()).getData(), 0,
                fullResponse.length);
        return image;
    }

    public CompletableFuture<Void> reconnect() {
        return disconnect().thenCompose(v -> connect());
    }

    private CompletableFuture<byte[]> sendCommandAndReceiveResponse(byte[] command, int expectedLength) {
        return sendCommand(command).thenCompose(v -> {
            ByteBuffer buffer = ByteBuffer.allocate(expectedLength);
            CompletableFuture<byte[]> resultFuture = new CompletableFuture<>();

            socketChannel.read(buffer, buffer, new CompletionHandler<Integer, ByteBuffer>() {
                @Override
                public void completed(Integer bytesRead, ByteBuffer attachment) {
                    attachment.flip();
                    byte[] data = new byte[attachment.remaining()];
                    attachment.get(data);
                    resultFuture.complete(data);
                }

                @Override
                public void failed(Throwable exc, ByteBuffer attachment) {
                    resultFuture.completeExceptionally(exc);
                }
            });

            return resultFuture;
        });
    }

    private CompletableFuture<Void> sendCommand(byte[] command) {
        return CompletableFuture.runAsync(() -> {
            ByteBuffer buffer = ByteBuffer.wrap(command);
            try {
                Future<Integer> writeFuture = socketChannel.write(buffer);
                writeFuture.get(SOCKET_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                throw new RuntimeException("发送命令失败", e);
            }
        }, executor);
    }

    private void handleErrorResponse(byte[] response) {
        switch (response[3]) {
            case 0x5F:
                log.error("错误: 操作失败。");
                break;
            case 0x6F:
                log.error("错误: 参数错误。");
                break;
            case 0x7F:
                log.error("错误: 命令未定义。");
                break;
            case (byte) 0xBF:
                log.error("错误: 通信失败。");
                break;
            case (byte) 0xFF:
                log.error("错误: 设备忙。");
                break;
            default:
                log.error("错误: 未知错误。");
        }
    }

    public CompletableFuture<Void> close() {
        return disconnect();
    }

    public static void main(String[] args) {
        VLA804 controller = new VLA804("192.168.1.12", 55003);
        controller.connect().thenRun(() -> {
            if (controller.isConnected()) {
                log.info("连接成功！");
                controller.getFirmwareVersion().join();
                controller.readInputImageStatus().join();
                System.out.println(controller.readCapturedImage("captured.jpg"));
                //等待2秒后重新读取
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                controller.readInputImageStatus().join();
                System.out.println(controller.readCapturedImage("captured.jpg"));
            } else {
                log.error("连接失败！");
            }
        }).exceptionally(e -> {
            log.error("操作过程中发生异常", e);
            return null;
        }).thenRun(() -> {
            controller.close().join();
        });
    }

    public static class ConnectionException extends Exception {
        public ConnectionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
