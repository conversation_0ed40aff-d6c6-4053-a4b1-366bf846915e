package com.desaysv.workserver.devices.speaker;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import javax.sound.sampled.*;
import java.io.File;
import java.io.IOException;

/**
 * 扬声器设备
 */
@Slf4j
public class SpeakerDevice extends Device {

    private SourceDataLine line;
    private boolean isOpen = false;

    public SpeakerDevice(DeviceOperationParameter deviceOperationParameter) {
        this.setDeviceOperationParameter(deviceOperationParameter);
    }

    public SpeakerDevice() {
        this(new DeviceOperationParameter());
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SPEAKER;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Speaker.SPEAKER;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isOpen) {
            throw new DeviceOpenRepeatException();
        }

        try {
            // 初始化播放器
            AudioFormat format = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    44100, // 采样率
                    16,    // 位深度
                    2,     // 声道数（立体声）
                    4,     // 每帧字节数
                    44100, // 每秒帧数
                    true   // big-endian
            );

            DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);
            line = (SourceDataLine) AudioSystem.getLine(info);
            line.open(format);
            isOpen = true;
            log.info("扬声器设备已打开");
            return true;
        } catch (LineUnavailableException e) {
            log.error("打开扬声器设备失败", e);
            throw new DeviceOpenException("打开扬声器设备失败: " + e.getMessage());
        }
    }

    @Override
    public boolean close() throws DeviceCloseException {
        if (!isOpen) {
            log.warn("扬声器设备未打开，无需关闭");
            return true;
        }

        if (line != null) {
            line.stop();
            line.close();
            line = null;
        }
        isOpen = false;
        log.info("扬声器设备已关闭");
        return true;
    }

    public void speak(SpeakerMessage speakerMessage) {
        if (!isOpen) {
            try {
                open();
            } catch (Exception e) {
                log.error("打开扬声器设备失败", e);
                return;
            }
        }

        log.info("播报: {}", speakerMessage.getText());

        try {
            if (speakerMessage.getAudioFile() != null) {
                // 尝试标准播放方法，如果失败则使用备选播放方法
                try {
                    playAudioFile(speakerMessage.getAudioFile());
                } catch (Exception e) {
                    log.warn("标准播放方法失败，尝试备选播放方法: {}", e.getMessage());
                    playAudioFileAlternative(speakerMessage.getAudioFile());
                }
            } else if (speakerMessage.getText() != null) {
                // 使用TTS服务将文本转换为语音
                File audioFile = TextToSpeechUtil.synthesize(speakerMessage.getText());
                if (audioFile != null) {
                    try {
                        // 确保文件完全写入
                        ensureFileReady(audioFile);
                        playAudioFile(audioFile);
                    } catch (Exception e) {
                        log.warn("标准播放方法失败，尝试备选播放方法: {}", e.getMessage());
                        playAudioFileAlternative(audioFile);
                    } finally {
                        // 语音播放完毕后删除临时文件
                        audioFile.delete();
                    }
                }
            }
        } catch (Exception e) {
            log.error("播放音频失败", e);
        }
    }

    /**
     * 确保文件已完全写入且可用
     */
    private void ensureFileReady(File file) {
        // 尝试确保文件已完全写入并且可以访问
        try {
            // 等待一小段时间以确保文件完全写入
            Thread.sleep(100);

            // 尝试打开并关闭文件以验证可访问性
            try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
                // 仅读取少量字节以验证文件可读
                byte[] testBuffer = new byte[1024];
                fis.read(testBuffer);
            }

            log.debug("文件已准备好播放: {}", file.getAbsolutePath());
        } catch (Exception e) {
            log.warn("文件准备检查失败: {}", e.getMessage());
        }
    }

    /**
     * 备选音频播放方法 - 使用系统命令播放音频
     */
    private void playAudioFileAlternative(File audioFile) {
        if (audioFile == null || !audioFile.exists()) {
            log.error("音频文件不存在或为空");
            return;
        }

        log.info("使用备选方法播放音频文件: {}", audioFile.getAbsolutePath());

        try {
            // 使用系统默认程序播放音频
            String command;
            String osName = System.getProperty("os.name").toLowerCase();

            if (osName.contains("win")) {
                // Windows
                command = "cmd /c start \"\" \"" + audioFile.getAbsolutePath() + "\"";
                Process process = Runtime.getRuntime().exec(command);

                // 等待播放完成（大约）
                int exitCode = process.waitFor();
                if (exitCode != 0) {
                    log.warn("Windows播放命令可能未成功执行，退出码: {}", exitCode);
                }
            } else if (osName.contains("mac")) {
                // macOS
                command = "afplay \"" + audioFile.getAbsolutePath() + "\"";
                Process process = Runtime.getRuntime().exec(command);
                process.waitFor();
            } else if (osName.contains("nix") || osName.contains("nux")) {
                // Linux
                command = "aplay \"" + audioFile.getAbsolutePath() + "\"";
                Process process = Runtime.getRuntime().exec(command);
                process.waitFor();
            } else {
                log.warn("未知操作系统，无法使用系统命令播放音频");
                return;
            }

            log.info("备选播放方法完成");
        } catch (Exception e) {
            log.error("备选播放方法失败", e);
        }
    }

    private void playAudioFile(File audioFile) throws UnsupportedAudioFileException, IOException, LineUnavailableException {
        if (audioFile == null || !audioFile.exists()) {
            log.error("音频文件不存在或为空");
            return;
        }

        log.info("开始播放音频文件: {}", audioFile.getAbsolutePath());
        AudioInputStream audioInputStream = null;
        AudioInputStream convertedStream = null;

        try {
            // 打开音频文件流
            audioInputStream = AudioSystem.getAudioInputStream(audioFile);
            AudioFormat sourceFormat = audioInputStream.getFormat();
            log.info("原始音频格式: 采样率={}, 位深度={}, 声道数={}", 
                    sourceFormat.getSampleRate(), sourceFormat.getSampleSizeInBits(), sourceFormat.getChannels());

            // 使用与文件实际格式更兼容的目标格式
            AudioFormat targetFormat;

            // 尽量保持原始格式的采样率和声道数，但确保是PCM编码
            targetFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    sourceFormat.getSampleRate(),
                    16, // 标准位深度
                    sourceFormat.getChannels(),
                    sourceFormat.getChannels() * 2, // 每声道2字节
                    sourceFormat.getSampleRate(),
                    false // 小端序（更常见）
            );

            // 如果格式不匹配，进行转换
            AudioInputStream streamToPlay = audioInputStream;
            if (!sourceFormat.matches(targetFormat)) {
                log.info("正在转换音频格式以匹配播放器要求");
                convertedStream = AudioSystem.getAudioInputStream(targetFormat, audioInputStream);
                streamToPlay = convertedStream;
            }

            // 确保line是开放状态且格式匹配
            if (line == null || !line.isOpen() || !line.getFormat().matches(targetFormat)) {
                if (line != null && line.isOpen()) {
                    log.debug("关闭现有音频线路以重新配置");
                    line.stop();
                    line.close();
                }

                log.debug("初始化音频播放线路");
                DataLine.Info info = new DataLine.Info(SourceDataLine.class, targetFormat);
                line = (SourceDataLine) AudioSystem.getLine(info);
                line.open(targetFormat);
                isOpen = true;
            }

            // 开始播放
            line.start();
            log.debug("音频播放开始");

            byte[] buffer = new byte[8192]; // 更大的缓冲区
            int bytesRead;
            int totalBytesRead = 0;

            while ((bytesRead = streamToPlay.read(buffer)) != -1) {
                if (bytesRead > 0) {
                    line.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }
            }

            // 确保所有音频数据都被播放
            log.debug("音频数据写入完成，共写入{}字节", totalBytesRead);
            line.drain();
            line.stop();
            log.info("音频播放完成");
        } catch (UnsupportedAudioFileException e) {
            log.error("不支持的音频文件格式: {}", e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("读取音频文件时发生IO错误: {}", e.getMessage());
            throw e;
        } catch (LineUnavailableException e) {
            log.error("获取音频播放线路失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("播放音频时发生未知错误: {}", e.getMessage());
            throw new IOException("播放音频失败", e);
        } finally {
            // 按正确的顺序关闭流
            if (convertedStream != null) {
                try {
                    convertedStream.close();
                } catch (IOException e) {
                    log.warn("关闭转换后的音频流时发生错误", e);
                }
            }
            if (audioInputStream != null) {
                try {
                    audioInputStream.close();
                } catch (IOException e) {
                    log.warn("关闭原始音频流时发生错误", e);
                }
            }
        }
    }
}
