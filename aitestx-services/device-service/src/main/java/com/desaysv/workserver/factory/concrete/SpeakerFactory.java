package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.speaker.SpeakerDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractSpeakerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class SpeakerFactory implements AbstractSpeakerFactory {

    @Override
    public Device createSpeakerDevice(DeviceRegisterForm deviceRegisterForm) {
        return new SpeakerDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Speaker.SPEAKER)) {
            return createSpeakerDevice(deviceRegisterForm);
        }
        return null;
    }
}
