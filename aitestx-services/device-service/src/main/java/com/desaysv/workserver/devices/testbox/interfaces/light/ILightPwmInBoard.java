package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.PWMEntity;
import com.desaysv.workserver.devices.testbox.TestBoardBoxUtils;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

public interface ILightPwmInBoard {
    Logger log = LogManager.getLogger(ILightPwmInBoard.class.getSimpleName());

    String FREQ = "frequency";
    String DUTY = "dutyCycle";

    /**
     * 采集PWM输入（10个通道）
     *
     * @return
     */
//    Map<String, PWMEntity> fetchPWMInputBoardCard() throws BoardCardTransportException;

    List<PWMEntity> fetchPWMInputBoardCard(int chassisNumber, int slotNumber, int channelNumber) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).LIGHT_PWM_IN_ACQUIRE"})
    default ActualExpectedResult fetchPWMInputBoardCard(Integer deviceChannel, Integer slotNumber, Integer channelNumber
            ,int frequencyExpectation, int frequencyDeviation, int dutyExpectation, int dutyDeviation) throws OperationFailNotification {
        log.info("PWM输入板卡通道{}-{}-{}，期望频率{}HZ, 要求偏差不超过{}%, 期望占空比{}%, 要求偏差不超过{}%", deviceChannel,slotNumber,channelNumber, frequencyExpectation, frequencyDeviation,
                dutyExpectation, dutyDeviation);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        try {
            List<PWMEntity> pwmEntityList = fetchPWMInputBoardCard(deviceChannel, slotNumber, channelNumber);
            if (pwmEntityList == null || pwmEntityList.isEmpty()) {
                throw new OperationFailNotification(String.format("PWM输入板卡通道%d获取PWM数据为空", channelNumber));
            }
            PWMEntity pwmEntity = pwmEntityList.get(0);
            actualExpectedResult.put(FREQ, true, String.format("%.2fHz", pwmEntity.getFrequency()));
            actualExpectedResult.put(DUTY, true, String.format("%.2f%%", pwmEntity.getDutyCycle()));
            StringBuilder sb = new StringBuilder();
            if (!TestBoardBoxUtils.isWithinDeviation(pwmEntity.getFrequency(), frequencyExpectation, frequencyDeviation)) {
                String info = String.format("PWM输入板卡通道%d频率%fHz不在允许偏差范围内\n", channelNumber, pwmEntity.getFrequency());
                sb.append(info);
                actualExpectedResult.put(FREQ, false);
                ActionSequencesLoggerUtil.info(info);
            } else {
                String info = String.format("PWM输入板卡通道%d频率%fHz在允许偏差范围内\n", channelNumber, pwmEntity.getFrequency());
                ActionSequencesLoggerUtil.info(info);
            }
            if (!TestBoardBoxUtils.isWithinDeviation(pwmEntity.getDutyCycle(), dutyExpectation, dutyDeviation)) {
                String info = String.format("PWM输入板卡通道%d占空比%f%%不在允许偏差范围内\n", channelNumber, pwmEntity.getDutyCycle());
                sb.append(info);
                actualExpectedResult.put(DUTY, false);
                ActionSequencesLoggerUtil.info(info);
            } else {
                String info = String.format("PWM输入板卡通道%d占空比%f%%在允许偏差范围内\n", channelNumber, pwmEntity.getDutyCycle());
                ActionSequencesLoggerUtil.info(info);
            }
            if (sb.length() > 0) {
                log.warn(sb.toString());
//                throw new OperationFailNotification(sb.toString());
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification(e);
        }
        return actualExpectedResult;
    }
}
