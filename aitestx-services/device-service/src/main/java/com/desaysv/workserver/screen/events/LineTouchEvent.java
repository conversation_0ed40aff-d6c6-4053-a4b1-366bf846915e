package com.desaysv.workserver.screen.events;

import com.desaysv.workserver.screen.entity.PreciousPoint3D;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LineTouchEvent extends TouchEvent {

    private PreciousPoint3D startPoint;
    private PreciousPoint3D endPoint;

    private String startPointName;
    private String endPointName;
    private double segmentLength = 0; //分割线段长度
}
