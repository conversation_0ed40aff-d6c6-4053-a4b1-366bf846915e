package com.desaysv.workserver.screen.events;

import com.desaysv.workserver.screen.entity.PreciousPoint3D;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties("mostSpecificCause")
public class ClickEvent extends TouchEvent {

    private PreciousPoint3D point;

    private String pointName;
}
