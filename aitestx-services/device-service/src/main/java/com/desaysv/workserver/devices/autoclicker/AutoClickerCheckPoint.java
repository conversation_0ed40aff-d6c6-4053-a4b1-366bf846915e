package com.desaysv.workserver.devices.autoclicker;

import com.desaysv.workserver.screen.config.ScreenConfig;
import lombok.Builder;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 点击器报点检测
 * @date: 2024/8/14 19:34
 */
@Data
@Builder
public class AutoClickerCheckPoint {
    private String serialName;
    private int fingers;
    private AutoClickerDataPackage autoClickerDataPackage;
    private ScreenConfig screenConfig;

}
