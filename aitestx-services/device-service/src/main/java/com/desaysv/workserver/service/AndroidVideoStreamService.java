package com.desaysv.workserver.service;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Android视频流服务
 * 提供Android设备视频流的管理和控制功能
 */
@Slf4j
@Service
public class AndroidVideoStreamService {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    // 活跃的视频流计数器
    private final ConcurrentHashMap<String, AtomicInteger> activeStreams = new ConcurrentHashMap<>();
    
    // 视频流状态缓存
    private final ConcurrentHashMap<String, Boolean> streamStatusCache = new ConcurrentHashMap<>();

    /**
     * 启动Android设备视频流
     * 
     * @param deviceName Android设备名称/序列号
     * @return 视频流的InputStream
     * @throws IOException 如果启动视频流失败
     * @throws IllegalArgumentException 如果设备不存在或不是Android设备
     */
    public InputStream startVideoStream(String deviceName) throws IOException {
        return startVideoStream(deviceName, 0, 0, 0);
    }

    /**
     * 启动Android设备视频流（带参数）
     * 
     * @param deviceName Android设备名称/序列号
     * @param width 视频宽度（0表示使用默认值）
     * @param height 视频高度（0表示使用默认值）
     * @param bitRate 视频比特率（0表示使用默认值）
     * @return 视频流的InputStream
     * @throws IOException 如果启动视频流失败
     * @throws IllegalArgumentException 如果设备不存在或不是Android设备
     */
    public InputStream startVideoStream(String deviceName, int width, int height, int bitRate) throws IOException {
        log.info("启动Android设备视频流: 设备={}, 分辨率={}x{}, 比特率={}", 
                deviceName, width, height, bitRate);

        AndroidDevice androidDevice = getAndroidDevice(deviceName);
        if (androidDevice == null) {
            throw new IllegalArgumentException("未找到Android设备: " + deviceName);
        }

        try {
            InputStream videoStream = androidDevice.startVideoStream(width, height, bitRate);
            if (videoStream != null) {
                // 增加活跃流计数
                activeStreams.computeIfAbsent(deviceName, k -> new AtomicInteger(0)).incrementAndGet();
                streamStatusCache.put(deviceName, true);
                log.info("成功启动Android设备视频流: 设备={}, 当前活跃流数量={}", 
                        deviceName, activeStreams.get(deviceName).get());
            }
            return videoStream;
        } catch (IOException e) {
            log.error("启动Android设备视频流失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            streamStatusCache.put(deviceName, false);
            throw e;
        }
    }

    /**
     * 停止Android设备视频流
     * 
     * @param deviceName Android设备名称/序列号
     * @throws IllegalArgumentException 如果设备不存在或不是Android设备
     */
    public void stopVideoStream(String deviceName) {
        log.info("停止Android设备视频流: {}", deviceName);

        AndroidDevice androidDevice = getAndroidDevice(deviceName);
        if (androidDevice == null) {
            throw new IllegalArgumentException("未找到Android设备: " + deviceName);
        }

        try {
            androidDevice.stopVideoStream();
            
            // 减少活跃流计数
            AtomicInteger counter = activeStreams.get(deviceName);
            if (counter != null) {
                int currentCount = counter.decrementAndGet();
                if (currentCount <= 0) {
                    activeStreams.remove(deviceName);
                    streamStatusCache.put(deviceName, false);
                }
                log.info("已停止Android设备视频流: 设备={}, 剩余活跃流数量={}", 
                        deviceName, Math.max(currentCount, 0));
            }
        } catch (Exception e) {
            log.error("停止Android设备视频流时出错: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            // 即使出错也要更新状态
            streamStatusCache.put(deviceName, false);
        }
    }

    /**
     * 检查Android设备视频流是否正在运行
     * 
     * @param deviceName Android设备名称/序列号
     * @return 如果视频流正在运行则返回true
     * @throws IllegalArgumentException 如果设备不存在或不是Android设备
     */
    public boolean isVideoStreamRunning(String deviceName) {
        AndroidDevice androidDevice = getAndroidDevice(deviceName);
        if (androidDevice == null) {
            throw new IllegalArgumentException("未找到Android设备: " + deviceName);
        }

        try {
            boolean isRunning = androidDevice.isVideoStreamRunning();
            streamStatusCache.put(deviceName, isRunning);
            
            // 同步活跃流计数
            if (!isRunning) {
                activeStreams.remove(deviceName);
            }
            
            return isRunning;
        } catch (Exception e) {
            log.error("检查Android设备视频流状态时出错: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取设备的活跃视频流数量
     * 
     * @param deviceName Android设备名称/序列号
     * @return 活跃视频流数量
     */
    public int getActiveStreamCount(String deviceName) {
        AtomicInteger counter = activeStreams.get(deviceName);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 获取所有活跃的视频流设备名称
     * 
     * @return 活跃视频流设备名称集合
     */
    public java.util.Set<String> getActiveStreamDevices() {
        return activeStreams.keySet();
    }

    /**
     * 停止所有Android设备的视频流
     */
    public void stopAllVideoStreams() {
        log.info("停止所有Android设备视频流");
        
        for (String deviceName : activeStreams.keySet()) {
            try {
                stopVideoStream(deviceName);
            } catch (Exception e) {
                log.error("停止设备视频流时出错: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            }
        }
        
        activeStreams.clear();
        streamStatusCache.clear();
        log.info("已停止所有Android设备视频流");
    }

    /**
     * 获取Android设备实例
     * 
     * @param deviceName 设备名称
     * @return Android设备实例，如果不存在或不是Android设备则返回null
     */
    private AndroidDevice getAndroidDevice(String deviceName) {
        try {
            Object device = deviceRegisterManager.getDevice(deviceName);
            if (device instanceof AndroidDevice) {
                return (AndroidDevice) device;
            } else {
                log.warn("设备 {} 不是Android设备类型: {}", deviceName, 
                        device != null ? device.getClass().getSimpleName() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取Android设备失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清理资源
     * 在应用关闭时调用
     */
    public void cleanup() {
        log.info("清理Android视频流服务资源");
        stopAllVideoStreams();
    }
}
