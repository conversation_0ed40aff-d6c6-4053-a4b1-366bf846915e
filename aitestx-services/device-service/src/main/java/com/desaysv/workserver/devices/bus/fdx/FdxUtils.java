package com.desaysv.workserver.devices.bus.fdx;

import com.desaysv.workserver.utils.ByteUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import static com.desaysv.workserver.devices.bus.fdx.FdxCommandConstant.setDoCANSendUDSDataDataSize;
import static com.desaysv.workserver.utils.DtcValidatorUtils.checkDtcExistence;
import static com.desaysv.workserver.utils.DtcValidatorUtils.checkDtcStatus;

@Slf4j
public class FdxUtils {
    //组合byte[]
    public static byte[] combineBytes(byte[]... bytes) {
        byte[] result = null;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            for (byte[] aByte : bytes) {
                outputStream.write(aByte);
            }
            result = outputStream.toByteArray();

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                outputStream.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return result;
    }

    public static byte[] mergeHexByteArrays(byte[][] hexByteArrays) {
        StringBuilder hexString = new StringBuilder();
        // 将每个16进制byte[]转换为字符串并连接起来
        for (byte[] hexByteArray : hexByteArrays) {
            String hex = Hex.encodeHexString(hexByteArray);
            hexString.append(hex);
        }
        // 将大的字符串转换为byte[]
        try {
            return Hex.decodeHex(hexString.toString());
        } catch (org.apache.commons.codec.DecoderException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static int hexBytesToInt32(byte[] bytes) {
//        byte[] bytes = {0x01, 0x00, 0x00, 0x00};
        // 创建一个ByteBuffer，并将bytes数组放入其中
        ByteBuffer buffer = ByteBuffer.wrap(Arrays.copyOf(bytes, 4));
        // 设置字节序为小端序
        buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN);
        // 读取int值
        int result = buffer.getInt();
//        System.out.println("转换后的int值为：" + result);
        return result;
    }

    public static double hexBytesToDouble(byte[] bytes) {
        // 创建一个ByteBuffer，并将bytes数组放入其中
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        // 设置字节序为小端序
        buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN);
        // 读取int值
        double result = buffer.getDouble();
//        System.out.println("转换后的double值为：" + result);
        return result;
    }


    public static byte[] int32ToHexBytes(int value, int paddedLength) {
        byte[] byteArray = int32ToHexBytes(value);
        if (paddedLength > 0) {
            return Arrays.copyOf(byteArray, paddedLength);
        }
        return byteArray;
    }


    public static byte[] stringBytesPaddedSize(String str, int paddedLength) {
        byte[] strBytes = str.getBytes();
        return Arrays.copyOf(strBytes, paddedLength);
    }

    /**
     * since for arrays the number of the actual used data bytes is transmitted in the
     * datagram, 4 bytes for this number must be added.
     */
    public static byte[] stringBytesToCANoeDataType(String strBytes) {
        byte[] usedArrayElementsNumberBytes = int32ToHexBytes(strBytes.length() / 2);   //四个字节的有效数组元素个数
        byte[] byteInstructionBytes = Arrays.copyOf(ByteUtils.hexStringToByteArray(strBytes), hexBytesToInt32(setDoCANSendUDSDataDataSize) - 4);  //除了4个字节的有效数组元素外，剩下的都是数据
        return FdxUtils.mergeHexByteArrays(new byte[][]{usedArrayElementsNumberBytes, byteInstructionBytes});
    }

    public static byte[] int32ToHexBytes(int value) {
        String hexString = Integer.toHexString(value);
        ByteBuffer buffer = ByteBuffer.allocate(4);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        buffer.putInt(Integer.parseInt(hexString, 16));
        return buffer.array();
    }

    public static byte[] doubleToHexBytes(double value) {
        // 创建一个ByteBuffer对象，并设置字节顺序为小端排序
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        // 将double值放入ByteBuffer中
        buffer.putDouble(value);
        // 获取ByteBuffer中的字节数组
        return buffer.array();
    }

    public static String doubleToHexString(double value) {
        String hex1 = Double.toHexString(value)
                .replace("p0", "")
                .replace("0x", "");
        hex1 = "0x" + hex1.toUpperCase();
        return hex1;
    }

    public static String hexBytesToString(byte[] hexBytes) {
        String str = new String(new byte[]{0x00}, StandardCharsets.US_ASCII); // 将字节数组
        return hexStringToAsciiString(Hex.encodeHexString(hexBytes)).replaceAll(str, "");
    }

    public static String hexStringToUTF8(byte[] hexBytes) {
        // 将字节数组转换为字符串，使用UTF-8编码
        return new String(hexBytes, StandardCharsets.UTF_8).trim();
    }

    // 将16进制字符串转换为ASCII码字符串
    public static String hexStringToAsciiString(String hexString) {
        StringBuilder asciiString = new StringBuilder();
        for (int i = 0; i < hexString.length(); i += 2) {
            String hex = hexString.substring(i, i + 2);
            int decimal = Integer.parseInt(hex, 16);
            asciiString.append((char) decimal);
        }
        return asciiString.toString();
    }

    private static void sleep(int time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static boolean arraysAreEqual(byte[] array1, byte[] array2) {
        if (array1 == array2) {
            return true;
        }
        if (array1 == null || array2 == null || array1.length != array2.length) {
            return false;
        }
        // 遍历数组元素逐个比较
        for (int i = 0; i < array1.length; i++) {
            if (array1[i] != array2[i]) {
                return false;
            }
        }
        return true;
    }


    public static byte[] hexStringToByteArray(String hexInput) {
        if (hexInput.length() % 2 != 0) {
            throw new IllegalArgumentException("Input string length must be even");
        }

        int len = hexInput.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) Integer.parseInt(hexInput.substring(i, i + 2), 16);
        }
        return data;
    }

    /**
     * 将一个包含两个元素的byte数组转换为一个int值。
     * 注意：此方法假设byte数组长度为2，且采用小端序。
     *
     * @param byteArray byte数组
     * @return 转换后的int值
     */
    public static int byteArrayToInt8(byte[] byteArray) {
//        System.out.println(Arrays.toString(byteArray));
        if (byteArray == null || byteArray.length != 1) {
            throw new IllegalArgumentException("Byte array must be of length 2.");
        }
        // 小端序转换
        int value = (byteArray[0] & 0xFF);
        return value;
    }

    public static byte[] intToByteArray(int value) {
        byte[] src = new byte[2];
        src[0] = (byte) ((value >> 0) & 0xFF);
        src[1] = (byte) ((value >> 8) & 0xFF);
//        System.out.println(Hex.encodeHex(src));
        return src;
    }

    /**
     * 比较十六进制字符串与数值的字节表示
     * @param obj1 十六进制字符串（如 "0X100319"）或数值（如 0x100319）
     * @param obj2 十六进制字符串（如 "0X100319"）或数值（如 0x100319）
     * @return 字节表示是否一致
     */
    public static boolean compareObject(Object obj1, Object obj2) {
        byte[] bytes1 = convertToBytes(obj1);
        byte[] bytes2 = convertToBytes(obj2);
        return Arrays.equals(bytes1, bytes2);
    }

    // 统一转换逻辑
    private static byte[] convertToBytes(Object obj) {
        if (obj instanceof String) {
            return hexStringToBytes((String) obj);
        } else if (obj instanceof Number) {
            return numberToBytes(((Number) obj).longValue());
        }
        throw new IllegalArgumentException("参数必须是String或Number类型");
    }

    public static byte[] hexStringToHexBytes(String hex) {
        hex = hex.replaceAll("\\s+", "");
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string");
        }
        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < hex.length(); i += 2) {
            String byteStr = hex.substring(i, i + 2);
            bytes[i / 2] = (byte) Integer.parseInt(byteStr, 16);
        }
        return bytes;
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            int v = b & 0xFF;
            hex.append(String.format("%02x", v));
        }
        return hex.toString();
    }


    // 字符串转字节数组（大端序）
    public static byte[] hexStringToBytes(String hex) {
        String cleanHex = hex.replaceFirst("^0[xX]", "");
        if (cleanHex.isEmpty()) return new byte[0];
        if (cleanHex.length() % 2 != 0) {
            throw new IllegalArgumentException("十六进制长度必须为偶数");
        }

        byte[] bytes = new byte[cleanHex.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int pos = i * 2;
            bytes[i] = (byte) Integer.parseInt(cleanHex.substring(pos, pos + 2), 16);
        }
        return bytes;
    }

    // 字节数组转小写十六进制字符串（无分隔符）
    public static String bytesToHex2(byte[] bytes) {
        StringBuilder hex = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            int v = b & 0xFF; // 转无符号整型
            hex.append(String.format("%02x", v));
        }
        return hex.toString();
    }



    // 数值转字节数组（自动长度大端序）
    private static byte[] numberToBytes(long number) {
        if (number == 0) return new byte[]{0x00};

        int bytesNeeded = (Long.SIZE - Long.numberOfLeadingZeros(number) + 7) / 8;
        byte[] bytes = new byte[bytesNeeded];

        for (int i = bytes.length - 1; i >= 0; i--) {
            bytes[i] = (byte) (number & 0xFF);
            number >>= 8;
        }
        return bytes;
    }


    public static void main(String[] args) {
//        byte[] initCmd = hexStringToBytes("02 fd 00 05 00 00 00 07 0F 02 00 00 00 00 00");
//        System.out.println(Hex.encodeHexString(initCmd));
//
//
//        System.out.println(Hex.encodeHexString(stringBytesToCANoeDataType("1001")));
//        System.out.println(Hex.encodeHexString(doubleToHexBytes(1234)));
//        String str = new String(new byte[]{0x00}, StandardCharsets.US_ASCII); // 将字节数组
//        System.out.println(hexStringToAsciiString(Hex.encodeHexString(new byte[]{0x41,0x00,0x42,0x43,0x00,0x00,0x00,0x00,0x00})).replaceAll(str, ""));
//        System.out.println(new String(new byte[]{0x41,0x42,0x43,0x00,0x00,0x00,0x00,0x00}).trim());
//        System.out.println(bytesToDouble(new byte[]{0x00,0x00,0x00,0x00,0x20,0x65, (byte) 0xf8,0x40}));
//        String hexInput = "22F193";
//        String inputText = "NULL" ;
//        String inputText = "ECU 从收到请求到给出响应的最大时间:50ms\n" +
//                "当ECU发送NRC0x78（pending）开始计时到ECU发送下一响应的最大时间:4000ms";
         String inputText="500100320190";
//        byte[] hexBytes = hexStringToByteArray(hexInput);
////
////        for (byte b : hexBytes) {
////            System.out.printf("0x%02X ", b);
////        }
////        System.out.println();
//
//        String s = Hex.encodeHexString(hexBytes);
//        System.out.println(s);


//        String inputText = "你好, World!";

        // 获取字符串的UTF-8字节表示
//        byte[] bytes = inputText.getBytes(StandardCharsets.UTF_8);
//        System.out.println("bytes.length11==" + bytes.length);
//
//        bytes = Arrays.copyOf(bytes, 3000);
//        System.out.println("bytes.length22==" + bytes.length);
//
//        // 将字节数组转换为16进制字符串数组
//        String[] hexStrings = new String[bytes.length];
//        for (int i = 0; i < bytes.length; i++) {
//            hexStrings[i] = Integer.toHexString(bytes[i] & 0xFF).toUpperCase(); // 使用大写形式
//        }
//
//        // （可选）合并16进制字符串数组为单一字符串
//        String combinedHex = String.join(" ", hexStrings);
//
//        System.out.println("16进制字符串数组: " + Arrays.toString(hexStrings));
//        System.out.println("合并后的16进制字符串: " + combinedHex);
//
//
//        byte[] testBbytes = new byte[hexStrings.length];
//        // 将16进制数组还原为字节数组
//        for (int i = 0; i < hexStrings.length; i++) {
//            testBbytes[i] = (byte) Integer.parseInt(hexStrings[i], 16); // 解析16进制字符串为字节
//        }
//        System.out.println("16进制数组: " + Hex.encodeHexString(testBbytes));
//
//        // 将字节数组转换为字符串，使用UTF-8编码
//        String originalString = new String(testBbytes, StandardCharsets.UTF_8);
//        System.out.println("恢复后的中英文字符串: " + originalString.trim());
//
//        byte[] bs = new byte[]{0x08, 0x00, 0x00, 0x00, 0x62, (byte) 0xF1, (byte) 0x93, 0x32, 0x01, (byte) 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
//
//        int usedLength = hexBytesToInt32(Arrays.copyOfRange(bs, bs.length - 22, bs.length - 18));
//        //12
//        System.out.println(usedLength);
//
//        System.out.println(Hex.encodeHexString(Arrays.copyOfRange(bs, bs.length - 18, bs.length - 18 + usedLength)));
//
//        System.out.println(hexBytesToInt32(getDoCANRecUdsDataSize));

        byte[] data = {
                (byte) 0x43, (byte) 0x41, (byte) 0x4e, (byte) 0x6f, (byte) 0x65, (byte) 0x46, (byte) 0x44, (byte) 0x58,
                (byte) 0x02, (byte) 0x01, (byte) 0x02, (byte) 0x00, (byte) 0xc9, (byte) 0x5c, (byte) 0x00, (byte) 0x00,
                (byte) 0x10, (byte) 0x00, (byte) 0x04, (byte) 0x00, (byte) 0x03, (byte) 0x00, (byte) 0x00, (byte) 0x00,
                (byte) 0xaf, (byte) 0x66, (byte) 0x31, (byte) 0xd3, (byte) 0xa3, (byte) 0x03, (byte) 0x00, (byte) 0x00,
                (byte) 0xc0, (byte) 0x0b, (byte) 0x05, (byte) 0x00, (byte) 0x28, (byte) 0x00, (byte) 0xb8, (byte) 0x0b,
                (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x41, (byte) 0x20, (byte) 0x30,
                (byte) 0x31, (byte) 0x20, (byte) 0x32, (byte) 0x35, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6,
                (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44,
                (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x41, (byte) 0x20, (byte) 0x30, (byte) 0x35,
                (byte) 0x20, (byte) 0x32, (byte) 0x35, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6,
                (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54,
                (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x30, (byte) 0x20, (byte) 0x30, (byte) 0x32, (byte) 0x20,
                (byte) 0x31, (byte) 0x33, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80,
                (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43,
                (byte) 0x3a, (byte) 0x39, (byte) 0x30, (byte) 0x20, (byte) 0x31, (byte) 0x31, (byte) 0x20, (byte) 0x31,
                (byte) 0x32, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81,
                (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a,
                (byte) 0x39, (byte) 0x35, (byte) 0x20, (byte) 0x30, (byte) 0x30, (byte) 0x20, (byte) 0x31, (byte) 0x33,
                (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a,
                (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39,
                (byte) 0x35, (byte) 0x20, (byte) 0x30, (byte) 0x31, (byte) 0x20, (byte) 0x31, (byte) 0x33, (byte) 0x20,
                (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30,
                (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x35,
                (byte) 0x20, (byte) 0x30, (byte) 0x32, (byte) 0x20, (byte) 0x31, (byte) 0x33, (byte) 0x20, (byte) 0xe7,
                (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x38,
                (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x35, (byte) 0x20,
                (byte) 0x30, (byte) 0x35, (byte) 0x20, (byte) 0x31, (byte) 0x33, (byte) 0x20, (byte) 0xe7, (byte) 0x8a,
                (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a,
                (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x35, (byte) 0x20, (byte) 0x30,
                (byte) 0x38, (byte) 0x20, (byte) 0x30, (byte) 0x30, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6,
                (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44,
                (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x39, (byte) 0x35, (byte) 0x20, (byte) 0x31, (byte) 0x34,
                (byte) 0x20, (byte) 0x30, (byte) 0x30, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6,
                (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54,
                (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x31, (byte) 0x20, (byte) 0x42, (byte) 0x32, (byte) 0x20,
                (byte) 0x38, (byte) 0x37, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80,
                (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43,
                (byte) 0x3a, (byte) 0x43, (byte) 0x32, (byte) 0x20, (byte) 0x45, (byte) 0x34, (byte) 0x20, (byte) 0x38,
                (byte) 0x37, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81,
                (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a,
                (byte) 0x43, (byte) 0x31, (byte) 0x20, (byte) 0x33, (byte) 0x36, (byte) 0x20, (byte) 0x38, (byte) 0x32,
                (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a,
                (byte) 0x30, (byte) 0x31, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43,
                (byte) 0x31, (byte) 0x20, (byte) 0x33, (byte) 0x36, (byte) 0x20, (byte) 0x38, (byte) 0x33, (byte) 0x20,
                (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30,
                (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x31,
                (byte) 0x20, (byte) 0x35, (byte) 0x32, (byte) 0x20, (byte) 0x38, (byte) 0x32, (byte) 0x20, (byte) 0xe7,
                (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39,
                (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x31, (byte) 0x20,
                (byte) 0x35, (byte) 0x32, (byte) 0x20, (byte) 0x38, (byte) 0x33, (byte) 0x20, (byte) 0xe7, (byte) 0x8a,
                (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a,
                (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x32, (byte) 0x20, (byte) 0x31,
                (byte) 0x38, (byte) 0x20, (byte) 0x38, (byte) 0x32, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6,
                (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44,
                (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x32, (byte) 0x20, (byte) 0x31, (byte) 0x38,
                (byte) 0x20, (byte) 0x38, (byte) 0x33, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6,
                (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54,
                (byte) 0x43, (byte) 0x3a, (byte) 0x43, (byte) 0x32, (byte) 0x20, (byte) 0x39, (byte) 0x34, (byte) 0x20,
                (byte) 0x38, (byte) 0x37, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80,
                (byte) 0x81, (byte) 0x3a, (byte) 0x30, (byte) 0x39, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43,
                (byte) 0x3a, (byte) 0x39, (byte) 0x30, (byte) 0x20, (byte) 0x34, (byte) 0x34, (byte) 0x20, (byte) 0x37,
                (byte) 0x31, (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81,
                (byte) 0x3a, (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a,
                (byte) 0x39, (byte) 0x35, (byte) 0x20, (byte) 0x31, (byte) 0x37, (byte) 0x20, (byte) 0x30, (byte) 0x30,
                (byte) 0x20, (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a,
                (byte) 0x30, (byte) 0x38, (byte) 0x0a, (byte) 0x44, (byte) 0x54, (byte) 0x43, (byte) 0x3a, (byte) 0x43,
                (byte) 0x30, (byte) 0x20, (byte) 0x45, (byte) 0x30, (byte) 0x20, (byte) 0x38, (byte) 0x37, (byte) 0x20,
                (byte) 0xe7, (byte) 0x8a, (byte) 0xb6, (byte) 0xe6, (byte) 0x80, (byte) 0x81, (byte) 0x3a, (byte) 0x30,
                (byte) 0x39, (byte) 0x0a, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00};

        String udsRecUdsDataDescribe = hexStringToUTF8(data);
        System.out.println("udsRecUdsDataDescribe: " + udsRecUdsDataDescribe);
        String udsRecUdsDataDescribe22 = new String(data, StandardCharsets.UTF_8);
        System.out.println("udsRecUdsDataDescribe222: " + udsRecUdsDataDescribe22);
        boolean checkDtcStatus1 = checkDtcStatus("C0E08710", udsRecUdsDataDescribe);
        System.out.println(checkDtcStatus1);
        boolean found = checkDtcExistence("C0E087", udsRecUdsDataDescribe);
        System.out.println(found);
    }

}
