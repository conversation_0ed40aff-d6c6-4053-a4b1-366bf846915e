package com.desaysv.workserver.canlog.queue;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

@Data
public class FixedSizeQueue<E> {
    private final LinkedBlockingQueue<E> queue;
    private boolean enabled;

    public FixedSizeQueue(int size) {
        queue = new LinkedBlockingQueue<>(size);
    }

    public synchronized void add(E element) throws InterruptedException {
        if (queue.remainingCapacity() == 0) {
            queue.poll();  // Remove the head element if the queue is full
        }
        queue.put(element);  // Add the new element
    }

    public synchronized void addAll(List<E> elements) throws InterruptedException {
        for (E element : elements) {
            add(element);
        }
    }

    public E poll() {
        return queue.poll();
    }

    public E peek() {
        return queue.peek();
    }

    public int size() {
        return queue.size();
    }

    public boolean isEmpty() {
        return queue.isEmpty();
    }

    public synchronized E[] toArray(E[] array) {
        return queue.toArray(array);
    }

    public List<E> toList() {
        return new ArrayList<>(queue);
    }

    public void clear() {
        queue.clear();
    }
}
