package com.desaysv.workserver.config.can;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
//TODO：增加通道
public class PtsConfig {

    private String sendPtsMessageId;

    private String receivePtsMessageId;

    private boolean canFd;

    @JSONField(serialize = false)
    public String getHexSendPtsMessageId() {
        if (sendPtsMessageId != null && !sendPtsMessageId.startsWith("0x")) {
            return "0x" + sendPtsMessageId;
        } else {
            return sendPtsMessageId;
        }
    }

    @JSONField(serialize = false)
    public String getHexReceivePtsMessageId() {
        if (receivePtsMessageId != null && !receivePtsMessageId.startsWith("0x")) {
            return receivePtsMessageId = "0x" + receivePtsMessageId;
        } else {
            return receivePtsMessageId;
        }
    }
}
