package com.desaysv.workserver.devices.I2C.interfaces;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.I2C.IICMessage;
import com.desaysv.workserver.devices.I2C.IICConfig;

public interface IUsbI2C {
    boolean receive(IICMessage iicMessage) throws OperationFailNotification;

    boolean setI2CIO(IICConfig iicConfig);

    boolean setI2CParameter(IICConfig iicConfig);

    boolean sendAndMatch(IICMessage iicMessage);

    boolean pwmOutputBegin(IICConfig iicConfig);

    boolean pwmOutputStop(IICConfig iicConfig);

    boolean adcGather(IICConfig iicConfig);
}
