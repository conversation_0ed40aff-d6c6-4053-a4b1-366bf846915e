package com.desaysv.workserver.devices.robot.type.magician.CPlusDll;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Structure;
import com.sun.jna.ptr.IntByReference;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public interface DobotDll extends Library {
    String filePath = "lib\\dobot\\DobotDll";
    DobotDll instance = Native.load(filePath, DobotDll.class);

    class Pose extends Structure {
        public static class ByReference extends Pose implements Structure.ByReference {
        }

        public static class ByValue extends Pose implements Structure.ByValue {
        }

        public float x;
        public float y;
        public float z;
        public float r;
        public float[] jointAngle = new float[4];

        @Override
        protected List<String> getFieldOrder() {
            List<String> a = new ArrayList<>();
            a.add("x");
            a.add("y");
            a.add("z");
            a.add("r");
            a.add("jointAngle");
            return a;
        }
    }

    class EndEffectorParams extends Structure {
        public float xBias;
        public float yBias;
        public float zBias;

        @Override
        protected List<String> getFieldOrder() {
            List<String> a = new ArrayList<>();
            a.add("xBias");
            a.add("yBias");
            a.add("zBias");
            return a;
        }
    }

    @Getter
    enum JOG {
        JogIdle(0),
        JogAPPressed(1),
        JogANPressed(2),
        JogBPPressed(3),
        JogBNPressed(4),
        JogCPPressed(5),
        JogCNPressed(6),
        JogDPPressed(7),
        JogDNPressed(8),
        JogEPPressed(9),
        JogENPressed(10);

        private final int value;

        JOG(int value) {
            this.value = value;
        }
    }

    class JOGCmd extends Structure {
        public JOGCmd() {
            setAlignType(Structure.ALIGN_NONE);
        }

        public byte isJoint;
        public byte cmd;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("isJoint");
            fieldOrder.add("cmd");
            return fieldOrder;
        }
    }

    class JOGJointParams extends Structure {
        public float[] velocity = new float[4];
        public float[] acceleration = new float[4];

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("velocity");
            fieldOrder.add("acceleration");
            return fieldOrder;
        }
    }

    class JOGCoordinateParams extends Structure {
        public float[] velocity = new float[4];
        public float[] acceleration = new float[4];

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("velocity");
            fieldOrder.add("acceleration");
            return fieldOrder;
        }
    }


    class JOGCommonParams extends Structure {
        public float velocityRatio;
        public float accelerationRatio;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("velocityRatio");
            fieldOrder.add("accelerationRatio");
            return fieldOrder;
        }
    }

    @Getter
    enum PTPMode {
        PTPJUMPXYZMode(0),
        PTPMOVJXYZMode(1),
        PTPMOVLXYZMode(2),

        PTPJUMPANGLEMode(3),
        PTPMOVJANGLEMode(4),
        PTPMOVLANGLEMode(5),

        PTPMOVJXYZINCMode(6),
        PTPMOVLXYZINCMode(7);

        private final int value;

        PTPMode(int value) {
            this.value = value;
        }
    }

    class PTPCmd extends Structure {
        public PTPCmd() {
            setAlignType(Structure.ALIGN_NONE);
        }

        public static class ByReference extends PTPCmd implements Structure.ByReference {
        }

        public static class ByValue extends PTPCmd implements Structure.ByValue {
        }

        public byte ptpMode;
        public float x;
        public float y;
        public float z;
        public float r;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("ptpMode");
            fieldOrder.add("x");
            fieldOrder.add("y");
            fieldOrder.add("z");
            fieldOrder.add("r");
            return fieldOrder;
        }
    }

    class PTPJointParams extends Structure {
        public float[] velocity = new float[4];
        public float[] acceleration = new float[4];

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("velocity");
            fieldOrder.add("acceleration");
            return fieldOrder;
        }
    }

    class PTPCoordinateParams extends Structure {
        public float xyzVelocity;
        public float rVelocity;
        public float xyzAcceleration;
        public float rAcceleration;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("xyzVelocity");
            fieldOrder.add("rVelocity");
            fieldOrder.add("xyzAcceleration");
            fieldOrder.add("rAcceleration");
            return fieldOrder;
        }
    }


    class PTPJumpParams extends Structure {
        public float jumpHeight;
        public float zLimit;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("jumpHeight");
            fieldOrder.add("zLimit");
            return fieldOrder;
        }
    }

    class HOMECmd extends Structure {
        public int reserved;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("reserved");
            return fieldOrder;
        }
    }

    class HOMEParams extends Structure {
        public float x;
        public float y;
        public float z;
        public float r;

        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrder = new ArrayList<>();
            fieldOrder.add("x");
            fieldOrder.add("y");
            fieldOrder.add("z");
            fieldOrder.add("r");
            return fieldOrder;
        }
    }


    enum DobotResult {
        DobotConnect_NoError,
        DobotConnect_NotFound,
        DobotConnect_Occupied
    }

    int ConnectDobot(String portName, int baudrate, char type, char version);

    void DisconnectDobot();

    void SetCmdTimeout(int cmdTimeout);

    int SetQueuedCmdClear();

    int SetQueuedCmdStartExec();

    int SetQueuedCmdStopExec();

    int SetEndEffectorParams(EndEffectorParams endEffectorParams, boolean isQueued, IntByReference queuedCmdIndex);

    int DobotExec();

    // Pose
    int GetPose(Pose pose);

    // Jog functions
    int SetJOGCmd(JOGCmd jogCmd, boolean isQueued, IntByReference queuedCmdIndex);

    int SetJOGJointParams(JOGJointParams jogJointParams, boolean isQueued, IntByReference queuedCmdIndex);

    int SetJOGCoordinateParams(JOGCoordinateParams jogCoordinateParams, boolean isQueued, IntByReference queuedCmdIndex);

    int SetJOGCommonParams(JOGCommonParams jogCommonParams, boolean isQueued, IntByReference queuedCmdIndex);

    // Playback functions
    int SetPTPCmd(PTPCmd ptpCmd, boolean isQueued, IntByReference queuedCmdIndex);

    int SetPTPJointParams(PTPJointParams ptpJointParams, boolean isQueued, IntByReference queuedCmdIndex);

    int SetPTPCoordinateParams(PTPCoordinateParams ptpCoordinateParams, boolean isQueued, IntByReference queuedCmdIndex);

    int SetPTPJumpParams(PTPJumpParams ptpJumpParams, boolean isQueued, IntByReference queuedCmdIndex);

    int SetHOMECmd(HOMECmd homeCmd, boolean isQueued, IntByReference queuedCmdIndex);

    int GetQueuedCmdCurrentIndex(IntByReference queuedCmdIndex);

    int SetHOMEParams(HOMEParams homeParams, boolean isQueued, IntByReference queuedCmdIndex);
}