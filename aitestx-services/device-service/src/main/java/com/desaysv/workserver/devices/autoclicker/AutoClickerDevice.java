package com.desaysv.workserver.devices.autoclicker;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

@Slf4j
public class AutoClickerDevice extends Device implements IAutoClicker {

    private UsbHidClicker usbHidClicker;
    private boolean success;

    public AutoClickerDevice() {
        this(new DeviceOperationParameter());
    }

    public AutoClickerDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_AUTO_CLICKER;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.AutoClicker.AUTO_CLICKER;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        usbHidClicker = new UsbHidClicker();
        if (usbHidClicker.searchDevices() != null) {
            log.info("open usbClicker");
            return usbHidClicker.open();
        }
        return false;
    }

    @Override
    public boolean close() throws DeviceCloseException {

        if (usbHidClicker != null) {
            usbHidClicker.close();
            log.info("close usbClicker");
        }
        return false;
    }

    @Override
    public boolean clickOneChannelStart(AutoClickerDataPackage autoClickerDataPackage) throws InterruptedException {
        return clickOneChannel(autoClickerDataPackage, true);
    }

    @Override
    public OperationResult channelDown( int channel) {
        OperationResult operationResult = new OperationResult();
        boolean result = usbHidClicker.channelDown(channel);
        if (result) {
            operationResult.ok();
        }
        return operationResult;
    }

    @Override
    public OperationResult channelUp(int channel) {
        OperationResult operationResult = new OperationResult();
        boolean result = usbHidClicker.channelUp(channel);
        if (result) {
            operationResult.ok();
        }
        return operationResult;
    }

    @Override
    public OperationResult autoClickCheckPoint(AutoClickerCheckPoint autoClickerCheckPoint) {
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        Device device = deviceFinderManager.findDeviceByAliasName(autoClickerCheckPoint.getSerialName());
        if (device != null) {
            SerialPortDevice serialPortDevice = (SerialPortDevice) device;
            //TODO:暂时放在这里，现有已经满足，后续完善成一个完整动作
            operationResult.ok("设备操作成功");
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }


    private boolean clickOneChannel(AutoClickerDataPackage autoClickerDataPackage, boolean useSleep) throws InterruptedException {
        int times = autoClickerDataPackage.getTimes();
        int hold = autoClickerDataPackage.getHold();
        int interval = autoClickerDataPackage.getInterval();
        int num = autoClickerDataPackage.getNum();
        Timer timer = createTimer(num, interval, hold, times);
        timer.start();
        if (useSleep) {
            int sleepTime = times * (hold + interval);
            Thread.sleep(sleepTime);
        }
        return success;
    }

    private Timer createTimer(int num, int interval, int hold, int times) {
        success = true;
        return new Timer(interval, new ActionListener() {
            private int count = 0;

            @Override
            public void actionPerformed(ActionEvent e) {
                try {
                    if (open()) {
                        String message = String.format("开始点击%d号,时长%d毫秒", num, hold);
                        log.info(message);
                        usbHidClicker.clickChannel(num, hold);
                        count++;
                        if (count >= times) {
                            ((Timer) e.getSource()).stop(); // 停止计时器
                            usbHidClicker.close();
                        }
                    } else {
                        success = false;
                        ((Timer) e.getSource()).stop();
                    }
                } catch (DeviceOpenException | DeviceOpenRepeatException ex) {
                    success = false;
                    throw new RuntimeException(ex);
                }
            }

        });
    }
}
