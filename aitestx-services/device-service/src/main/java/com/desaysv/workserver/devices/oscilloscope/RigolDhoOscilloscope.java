package com.desaysv.workserver.devices.oscilloscope;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.oscilloscope.base.OscilloscopeDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

/**
 * @author: QinHao
 * @description:
 * @date: 2024/10/11 17:11
 */
@Slf4j
public class RigolDhoOscilloscope extends OscilloscopeDevice {

    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @JSONField(serialize = false)
    private JVisaInstrument instrument;

    public RigolDhoOscilloscope() {
        this(new DeviceOperationParameter());
    }

    public RigolDhoOscilloscope(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            visaResourceManager = new JVisaResourceManager();
        } catch (JVisaException e) {
            log.warn(e.getMessage(), e);
        }
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Oscilloscope.RIGOL_800;
    }

    public String sendAndReceiveString(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        return instrument.queryString(command);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (visaResourceManager != null) {
            try {
                instrument = visaResourceManager.openInstrument(getDeviceName());
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (instrument != null) {
            try {
                instrument.write(RigolSCPICommands.CLEAR_SCREEN);
                instrument.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (visaResourceManager != null) {
            try {
                visaResourceManager.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        return super.close();
    }

    private void checkInstrument() {
        Preconditions.checkNotNull(instrument, "仪器JVisaInstrument实例不存在");
    }

    /**
     * 初始化示波器到默认状态。
     */
    public void initializeOscilloscope() throws JVisaException {
        resetOscilloscope();
        clearWaveform();
    }

    private void resetOscilloscope() throws JVisaException {
        sendCommand(RigolSCPICommands.RESET_INSTRUMENTS);
    }

    private void clearWaveform() throws JVisaException {
        sendCommand(RigolSCPICommands.CLEAR_SCREEN);
    }

    private void setVerticalScale() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.CHANNEL_SCALE, 1, "0.1"));
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.CHANNEL_SCALE, 2, "0.1"));
    }

    private void setHorizontalTimebase() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.HORIZONTAL_SCALE, "0.00005"));
    }

    private void enableChannels() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.CHANNEL_DISPLAY, 1, "ON"));
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.CHANNEL_DISPLAY, 2, "ON"));
    }

    private void setTriggerMode() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.TRIGGER_MODE, "AUTO"));
    }

    private void setTriggerSource() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.TRIGGER_SOURCE, "CHANnel1"));
    }

    private void setTriggerLevel() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.TRIGGER_LEVEL, "0.5"));
    }

    private void setCoupling() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.COUPLING, 1, "DC"));
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.COUPLING, 2, "DC"));
    }

    private void setHorizontalTimebaseOffset() throws JVisaException {
        sendCommand(RigolSCPICommands.formatCommand(RigolSCPICommands.HORIZONTAL_OFFSET, "0"));
    }

    private void sendCommand(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        instrument.write(command);
    }
}