package com.desaysv.workserver.devices.bus.e2e.gac;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/6/24 16:09
 * @Version: 1.0
 * @Desc : 描述信息
 */
public class E2eGacTableManager {

    @Getter
    private static final E2eGacTableManager instance = new E2eGacTableManager();

    public Map<Integer, Integer> e2eMap = new ConcurrentHashMap<>();


    public E2eGacTableManager(){
        e2eMap.clear();
        e2eMap.put(Integer.parseInt("0x268".substring(2), 16), Integer.parseInt("0x011A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x122".substring(2), 16), Integer.parseInt("0x011B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x267".substring(2), 16), Integer.parseInt("0x0401".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x160".substring(2), 16), Integer.parseInt("0x0409".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x263".substring(2), 16), Integer.parseInt("0x0411".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x26A".substring(2), 16), Integer.parseInt("0x0419".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1AE".substring(2), 16), Integer.parseInt("0x0404".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3AB".substring(2), 16), Integer.parseInt("0x040C".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x16B".substring(2), 16), Integer.parseInt("0x0405".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x16A".substring(2), 16), Integer.parseInt("0x0415".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x303".substring(2), 16), Integer.parseInt("0x040D".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x170".substring(2), 16), Integer.parseInt("0x0301".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x360".substring(2), 16), Integer.parseInt("0x0309".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x179".substring(2), 16), Integer.parseInt("0x0311".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x2C0".substring(2), 16), Integer.parseInt("0x0319".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x368".substring(2), 16), Integer.parseInt("0x0321".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x370".substring(2), 16), Integer.parseInt("0x0329".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x178".substring(2), 16), Integer.parseInt("0x0331".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x180".substring(2), 16), Integer.parseInt("0x0339".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x188".substring(2), 16), Integer.parseInt("0x0341".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x338".substring(2), 16), Integer.parseInt("0x0349".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B8".substring(2), 16), Integer.parseInt("0x0351".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3FF".substring(2), 16), Integer.parseInt("0x0359".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x28D".substring(2), 16), Integer.parseInt("0x0342".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3A9".substring(2), 16), Integer.parseInt("0x034A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x284".substring(2), 16), Integer.parseInt("0x0352".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3A2".substring(2), 16), Integer.parseInt("0x0302".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x195".substring(2), 16), Integer.parseInt("0x030A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B7".substring(2), 16), Integer.parseInt("0x0312".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x32C".substring(2), 16), Integer.parseInt("0x031A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x198".substring(2), 16), Integer.parseInt("0x0322".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x378".substring(2), 16), Integer.parseInt("0x032A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B0".substring(2), 16), Integer.parseInt("0x0332".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x448".substring(2), 16), Integer.parseInt("0x033A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x38".substring(2), 16), Integer.parseInt("0x0024".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3BD".substring(2), 16), Integer.parseInt("0x0024".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x190".substring(2), 16), Integer.parseInt("0x011C".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1BE".substring(2), 16), Integer.parseInt("0x0421".substring(2), 16));
        e2eMap.put(Integer.parseInt("0X2C1".substring(2), 16), Integer.parseInt("0x0303".substring(2), 16));
        e2eMap.put(Integer.parseInt("0X369".substring(2), 16), Integer.parseInt("0x030B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x193".substring(2), 16), Integer.parseInt("0x0630".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x19B".substring(2), 16), Integer.parseInt("0x0638".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x393".substring(2), 16), Integer.parseInt("0x0640".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1A3".substring(2), 16), Integer.parseInt("0x0648".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x293".substring(2), 16), Integer.parseInt("0x0650".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x39B".substring(2), 16), Integer.parseInt("0x0658".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x29B".substring(2), 16), Integer.parseInt("0x0660".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B3".substring(2), 16), Integer.parseInt("0x0668".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x194".substring(2), 16), Integer.parseInt("0x0670".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1A4".substring(2), 16), Integer.parseInt("0x0678".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1B5".substring(2), 16), Integer.parseInt("0x0680".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1A7".substring(2), 16), Integer.parseInt("0x0121".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x361".substring(2), 16), Integer.parseInt("0x0144".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x278".substring(2), 16), Integer.parseInt("0x0145".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1A6".substring(2), 16), Integer.parseInt("0x0313".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x308".substring(2), 16), Integer.parseInt("0x002C".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x225".substring(2), 16), Integer.parseInt("0x0666".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x26B".substring(2), 16), Integer.parseInt("0x0429".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3C7".substring(2), 16), Integer.parseInt("0x0703".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x36F".substring(2), 16), Integer.parseInt("0x0704".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x17F".substring(2), 16), Integer.parseInt("0x0139".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x17E".substring(2), 16), Integer.parseInt("0x0117".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3AD".substring(2), 16), Integer.parseInt("0x0633".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x343".substring(2), 16), Integer.parseInt("0x06A3".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x2A".substring(2), 16), Integer.parseInt("0x0133".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x396".substring(2), 16), Integer.parseInt("0x0133".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x123".substring(2), 16), Integer.parseInt("0x0111".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x121".substring(2), 16), Integer.parseInt("0x0112".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x120".substring(2), 16), Integer.parseInt("0x0113".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x15F".substring(2), 16), Integer.parseInt("0x0706".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x1DB".substring(2), 16), Integer.parseInt("0x0705".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x270".substring(2), 16), Integer.parseInt("0x0114".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x288".substring(2), 16), Integer.parseInt("0x0115".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x29".substring(2), 16), Integer.parseInt("0x0025".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x2B8".substring(2), 16), Integer.parseInt("0x015F".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x339".substring(2), 16), Integer.parseInt("0x01E2".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x317".substring(2), 16), Integer.parseInt("0x01D1".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x309".substring(2), 16), Integer.parseInt("0x0147".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x318".substring(2), 16), Integer.parseInt("0x0143".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x156".substring(2), 16), Integer.parseInt("0x011D".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x280".substring(2), 16), Integer.parseInt("0x0123".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x31F".substring(2), 16), Integer.parseInt("0x012B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x38".substring(2), 16), Integer.parseInt("0x001B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B2".substring(2), 16), Integer.parseInt("0x0123".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3B1".substring(2), 16), Integer.parseInt("0x0124".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x0AF".substring(2), 16), Integer.parseInt("0x0669".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x401".substring(2), 16), Integer.parseInt("0x031B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3A4".substring(2), 16), Integer.parseInt("0x066A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x3DB".substring(2), 16), Integer.parseInt("0x066B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x2E3".substring(2), 16), Integer.parseInt("0x0702".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x35C".substring(2), 16), Integer.parseInt("0x036C".substring(2), 16));
    }

}
