package com.desaysv.workserver.devices.bus.tosun.can;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.tosun.TSCan;
import com.desaysv.workserver.entity.ActionSequenceExecutionContextInfo;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * TC1016底层接口
 */
@Component
@Lazy
@Scope("prototype")
public class TC1016 extends TSCan {
    @Autowired
    private ActionSequenceExecutionContextInfo context;
    public TC1016(){

    }
    public TC1016(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.TC1016;
    }

}
