package com.desaysv.workserver.devices.usbplug;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.desay.sv.service.Impl.ModbusServiceImpl;
import org.desay.sv.service.ModbusService;

/**
 * USB机械插拔设备
 */
@Slf4j
public class UsbPlugDevice extends PortDevice {
    private ModbusService modbusService;

    public UsbPlugDevice() {
        this(new DeviceOperationParameter());
    }

    public UsbPlugDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        modbusService = new ModbusServiceImpl(getDevicePortName(), 115200);
        int addr = SystemEnv.getUsbPlugAddress();
        modbusService.onServo(addr);
        modbusService.restoration();
        log.info("USB插拔设备已连接，设备地址:{}", addr);
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (modbusService != null) {
            modbusService.closePort();
        }
        return true;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_PLUG;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.UsbPlug.USB_PLUG;
    }

    public boolean returnHome() {
        if (modbusService == null) {
            return false;
        }
        modbusService.restoration();
        return true;
    }

    public boolean forward() {
        if (modbusService == null) {
            return false;
        }
        modbusService.goForward();
        return true;
    }

    public boolean backward() {
        if (modbusService == null) {
            return false;
        }
        modbusService.goBackward();
        return true;
    }

    public boolean alarmReset() {
        if (modbusService == null) {
            return false;
        }
        modbusService.alarmReset();
        return true;
    }

    @JSONField(serialize = false)
    public double getLocation() {
        if (modbusService == null) {
            return -1;
        }
        return modbusService.getLocation();
    }

    public boolean move(double targetPosition, double speed, double acceleration) {
        if (modbusService == null) {
            return false;
        }
        modbusService.move(targetPosition, speed, acceleration);
        return true;
    }

    public boolean plugIn(double pos) {
        if (modbusService == null) {
            return false;
        }
        return move(pos, 50, 0.5);
    }

    public boolean pullOut(double pos) {
        if (modbusService == null) {
            return false;
        }
        return move(pos, 50, 0.5);
    }

}
