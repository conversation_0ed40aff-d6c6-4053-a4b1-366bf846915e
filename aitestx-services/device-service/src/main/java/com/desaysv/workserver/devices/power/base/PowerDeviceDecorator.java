package com.desaysv.workserver.devices.power.base;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;

//TODO: 后续装饰器考虑改成jsonRPC进行方向调用，json格式: 设备名、方法名、方法参数 —— lwj

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-24 9:34
 * @description : 电源设备装饰类
 * @modified By :
 * @since : 2022-3-24
 */
@Deprecated
public class PowerDeviceDecorator extends PowerDevice {
    private final PowerDevice device;

    public PowerDeviceDecorator(PowerDevice device) {
        super(device.getDeviceOperationParameter());
        this.device = device;
    }

    @Override
    public Device getDevice() {
        return device;
    }

    @Override
    public int getNumberChannels() {
        return device.getNumberChannels();
    }

    @Override
    public String getDeviceModel() {
        return device.getDeviceModel();
    }

    @Override
    public float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        return device.fetchVoltage(deviceChannel, currentDirection);
    }

    @Override
    public float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        return device.fetchCurrent(deviceChannel, currentDirection);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return device.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return device.close();
    }

    @Override
    public boolean fetchOutput() {
        return device.fetchOutput();
    }

    @Override
    public boolean outputOn(Integer deviceChannel) {
        return device.outputOn(deviceChannel);
    }

    @Override
    public boolean outputOff(Integer deviceChannel) {
        return device.outputOff(deviceChannel);
    }

    @Override
    public boolean setVoltage(Integer deviceChannel, float voltage) {
        return device.setVoltage(deviceChannel, voltage);
    }

    @Override
    public boolean setVoltageWithUnit(Integer deviceChannel, String voltageWithUnit) throws OperationFailNotification {
        return device.setVoltageWithUnit(deviceChannel, voltageWithUnit);
    }

    @Override
    public boolean setCurrent(Integer deviceChannel, float current) {
        return device.setCurrent(deviceChannel, current);
    }

    @Override
    public CriticalVoltage searchMinimumCriticalVoltage(Integer deviceChannel, CriticalVoltage criticalVoltage)
            throws DeviceReadException {
        return device.searchMinimumCriticalVoltage(deviceChannel, criticalVoltage);
    }

}
