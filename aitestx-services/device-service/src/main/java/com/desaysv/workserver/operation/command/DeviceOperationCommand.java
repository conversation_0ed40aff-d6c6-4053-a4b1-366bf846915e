package com.desaysv.workserver.operation.command;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationCommand;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.DeviceOperationMethod;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 9:58
 * @description : 设备操作命令
 * @modified By :
 * @since : 2022-3-18
 */
@Slf4j
@Component
@Lazy
public class DeviceOperationCommand implements OperationCommand {

    @Autowired
    private DeviceFinderManager deviceFinderManager;

    @Override
    public OperationResult execute(ExecutionContext executionContext, Operation operation) {
        OperationResult operationResult = new OperationResult();
        Device device = null;
        String methodName = operation.getOperationMethod().getKeyword();
        OperationTarget operationTarget = operation.getOperationTarget();
        if (operationTarget != null) {
            if (StrUtils.isEmpty(operationTarget.getAliasName())) {
                device = deviceFinderManager.findDeviceByDeviceType(
                        operationTarget.getDeviceType(),
                        operationTarget.getDeviceIndex());
            } else {
                device = deviceFinderManager.findDeviceByAliasName(operationTarget.getAliasName());
            }
        }
        if (operationTarget != null && device != null) {
            operationResult.setOk(true);
            if (!methodName.contains(DeviceOperationMethod.fetchFeedbackData.getKeyword())) {
                // TODO：封装LOG过滤关键字，相同log只打印一次
//                    log.info("---------------------------------------");
//                    log.info("execute operationHeader:{}", operationHeader);
//                    log.info("getOperationHeader->id:{}", System.identityHashCode(operationHeader));
//                    log.info("getOperationHeader->getClientInfo->id:{}", System.identityHashCode(operationHeader.getClientInfo()));
//                    log.info("---------------------------------------");
                log.info("{}通道{}执行操作:{}{}", device.getDeviceName(), device.getChannel(), methodName, operation.getOperationObject() == null ? "" : "->" + operation.getOperationObject());
            }
            if (executionContext != null) {
                device.getExecutionContext().set(executionContext);
            }
            device.setChannel(operationTarget.getChannel());
            operationResult = callOperationMethod(device, operationTarget.getChannel(), operation);
        }
        return operationResult;
    }

}
