package com.desaysv.workserver.service;

import com.desaysv.workserver.model.DateFolderInfo;
import com.desaysv.workserver.model.TemplateImageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 图像展示服务
 * 处理图片文件夹扫描和图片数据组织
 */
@Slf4j
@Service
public class ImageDisplayService {

    private static final String BASE_PATH = "D:\\FlyTest\\data\\server\\system\\images\\";
    private static final Pattern TEMPLATE_FOLDER_PATTERN = Pattern.compile("(.+)@(\\d+\\.\\d+)@(.+)");

    /**
     * 获取所有日期文件夹
     *
     * @return 日期文件夹信息列表，按日期降序排列
     */
    public List<DateFolderInfo> getAllDateFolders() {
        File baseDir = new File(BASE_PATH);

        try {
            if (!baseDir.exists()) {
                log.error("Base directory does not exist: {}", BASE_PATH);
                // 创建目录
                boolean created = baseDir.mkdirs();
                if (created) {
                    log.info("Created base directory: {}", BASE_PATH);
                } else {
                    log.error("Failed to create base directory: {}", BASE_PATH);
                    return Collections.emptyList();
                }
            }

            if (!baseDir.isDirectory()) {
                log.error("Base path is not a directory: {}", BASE_PATH);
                return Collections.emptyList();
            }

            File[] files = baseDir.listFiles(File::isDirectory);
            if (files == null) {
                log.error("Failed to list directories in base path: {}", BASE_PATH);
                return Collections.emptyList();
            }

            if (files.length == 0) {
                log.info("No date folders found in: {}", BASE_PATH);
                return Collections.emptyList();
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            List<DateFolderInfo> folderInfos = new ArrayList<>();

            for (File file : files) {
                String name = file.getName();
                Date date = null;
                try {
                    date = dateFormat.parse(name);
                } catch (ParseException e) {
                    // 如果不是日期格式，使用文件最后修改时间
                    date = new Date(file.lastModified());
                    log.debug("Folder name {} is not a date format, using last modified time", name);
                }
                folderInfos.add(new DateFolderInfo(name, date));
            }

            // 按日期降序排序
            folderInfos.sort((f1, f2) -> f2.getDate().compareTo(f1.getDate()));
            log.info("Found {} date folders in {}", folderInfos.size(), BASE_PATH);
            return folderInfos;
        } catch (Exception e) {
            log.error("Error getting date folders: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取特定日期文件夹下的模板图片信息
     *
     * @param folderName 日期文件夹名称
     * @return 模板图片信息列表
     */
    public List<TemplateImageInfo> getTemplateImages(String folderName) {
        File dateFolder = new File(BASE_PATH + folderName);
        if (!dateFolder.exists() || !dateFolder.isDirectory()) {
            log.warn("Date folder does not exist: {}", dateFolder.getAbsolutePath());
            return Collections.emptyList();
        }

        File[] templateFolders = dateFolder.listFiles(File::isDirectory);
        if (templateFolders == null || templateFolders.length == 0) {
            return Collections.emptyList();
        }

        List<TemplateImageInfo> results = new ArrayList<>();
        for (File templateFolder : templateFolders) {
            String folderName1 = templateFolder.getName();
            Matcher matcher = TEMPLATE_FOLDER_PATTERN.matcher(folderName1);

            if (matcher.matches()) {
                String templateName = matcher.group(1);
                double similarity = Double.parseDouble(matcher.group(2));
                String uuid = matcher.group(3);

                // 只检查标准命名的文件: template.jpg 和 origin.jpg
                File templateImageFile = new File(templateFolder, "template.jpg");
                File originalImageFile = new File(templateFolder, "origin.jpg");

                String templateImagePath = null;
                String originalImagePath = null;

                if (templateImageFile.exists() && templateImageFile.isFile()) {
                    templateImagePath = folderName + "/" + folderName1 + "/template.jpg";
                    log.info("找到模板图片: {}", templateImagePath);
                } else {
                    log.warn("模板图片不存在: {}/{}/template.jpg", folderName, folderName1);
                }

                if (originalImageFile.exists() && originalImageFile.isFile()) {
                    originalImagePath = folderName + "/" + folderName1 + "/origin.jpg";
                    log.info("找到原图: {}", originalImagePath);
                } else {
                    log.warn("原图不存在: {}/{}/origin.jpg", folderName, folderName1);
                }

                results.add(new TemplateImageInfo(templateName, templateImagePath, originalImagePath, similarity, uuid));
            }
        }

        // 按模板名和相似度排序
        results.sort(Comparator.comparing(TemplateImageInfo::getTemplateName)
                .thenComparing(TemplateImageInfo::getSimilarity, Comparator.reverseOrder()));

        return results;
    }

    /**
     * 获取图片内容
     *
     * @param relativePath 图片相对路径
     * @return 图片字节数组
     */
    public byte[] getImageContent(String relativePath) {
        try {
            // URL解码路径
            String decodedPath = URLDecoder.decode(relativePath, StandardCharsets.UTF_8.name());
            log.info("解码后的相对路径: {}", decodedPath);

            // 构建完整路径
            Path path = Paths.get(BASE_PATH, decodedPath);
            log.info("完整文件路径: {}", path.toAbsolutePath());

            File imageFile = path.toFile();
            if (!imageFile.exists()) {
                log.error("图片文件不存在: {}", path.toAbsolutePath());

                // 尝试直接使用相对路径（不做解码）
                path = Paths.get(BASE_PATH, relativePath);
                log.info("尝试原始路径: {}", path.toAbsolutePath());

                imageFile = path.toFile();
                if (!imageFile.exists()) {
                    log.error("使用原始路径仍然找不到文件: {}", path.toAbsolutePath());

                    // 检查基础目录
                    File baseDir = new File(BASE_PATH);
                    log.info("基础目录是否存在: {}", baseDir.exists());
                    if (baseDir.exists() && baseDir.isDirectory()) {
                        // 列出基础目录下的所有文件和目录
                        File[] files = baseDir.listFiles();
                        if (files != null) {
                            log.info("基础目录下的内容: {}", Arrays.toString(files));
                        }
                    }

                    return new byte[0];
                }
            }

            if (!imageFile.isFile()) {
                log.error("路径不是文件: {}", path.toAbsolutePath());
                return new byte[0];
            }

            if (!imageFile.canRead()) {
                log.error("无法读取文件: {}", path.toAbsolutePath());
                return new byte[0];
            }

            byte[] imageBytes = Files.readAllBytes(path);
            log.info("成功读取图片，大小: {} 字节", imageBytes.length);
            return imageBytes;
        } catch (IOException e) {
            log.error("读取图片失败: {}", relativePath, e);
            return new byte[0];
        } catch (Exception e) {
            log.error("读取图片时发生意外错误: {}", relativePath, e);
            return new byte[0];
        }
    }
}
