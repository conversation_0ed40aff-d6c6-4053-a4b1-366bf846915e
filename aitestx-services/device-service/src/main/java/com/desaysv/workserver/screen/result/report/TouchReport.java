package com.desaysv.workserver.screen.result.report;

import com.desaysv.workserver.utils.StrUtils;
import lombok.Data;

import java.util.List;

@Data
public class TouchReport {

    public enum TouchType {
        POINT, LINE, CIRCLE, UNDEFINED
    }

    private Boolean ok;

    private String reportName; //报点名称
    private TouchType touchType; //触摸类型
    private int reportQuantity; //报点数量

    private double linearRatio;//线性度


    private int lowerReportQuantityLimit;
    private int upperReportQuantityLimit;

    private String reportSummary;//报点总结

    private List<? extends TouchSummary> touchSummaryList;

    private List<String> originalDataList;

    public boolean isOk() {
        if (ok != null) {
            return ok;
        }
        if (touchSummaryList == null) {
            return false;
        }
        if (touchSummaryList.isEmpty()) {
            return false;
        }
        return isAllSummaryOk();
    }

    private boolean isAllSummaryOk() {
        for (TouchSummary touchSummary : touchSummaryList) {
            if (!touchSummary.isOk()) {
                return false;
            }
        }
        return true;
    }

    public String getSummaryReport(String prefixSummary, boolean onlyFailRecord) {
        String prefix = prefixSummary == null || StrUtils.isEmpty(prefixSummary) ? "" : prefixSummary + " ";
        StringBuilder sb = new StringBuilder(prefix);
        if (!isAllSummaryOk()) {
            sb.append("详情:").append("\n");
            for (TouchSummary summary : touchSummaryList) {
                if (onlyFailRecord) {
                    if (!summary.isOk()) {
                        sb.append(summary.getDescription()).append("\n");
                    }
                } else {
                    sb.append(summary.getDescription()).append("\n");
                }
            }
        }

        return sb.toString();
    }
}
