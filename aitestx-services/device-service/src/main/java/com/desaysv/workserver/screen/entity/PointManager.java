package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.entity.PointInt;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class PointManager {
    /**
     * Alpha： p1,p2与横轴形成的角度
     *
     * @param p1 左下角顶点
     * @param p2 左上角顶点
     * @return p1, p2与横轴形成的角度
     */
    private static Double getAlpha(PreciousPoint2D p1, PreciousPoint2D p2) {
//      p2-p1,p2是原点
        BigDecimal delta_Y = p1.getY().subtract(p2.getY());
        BigDecimal delta_X = p1.getX().subtract(p2.getX());
        double a;
        if (delta_Y.compareTo(BigDecimal.valueOf(0)) == 0) {
            a = 90;
            return a;
        }
        // 按照坐标系k=-y/x
        BigDecimal k = (delta_X.multiply(BigDecimal.valueOf(-1)).divide(delta_Y, 10, RoundingMode.UP));
        double tanA = Math.atan(Double.parseDouble(String.valueOf(k)));
        a = tanA * 180 / Math.PI;
        // Math.atan 返回一个逆时针角度
        if (k.compareTo(BigDecimal.valueOf(0)) < 0) {
            a = 180 + a;
        }
        // 弧度=角度*Math.PI/180,atan返回的是一个逆时针角度的值,原点到一个点(坐标)的线段逆时针旋转的弧度值就是Math.atan2()的返回值
        return a;
    }

    /**
     * Beta和Alpha互为余角
     *
     * @param a alpha角度
     * @return 余角
     */
    private static Double getBeta(double a) {
        Double beta = null;
        if (a > 90) beta = a - 90;
        if (a < 90) beta = 90 - a;
        return beta;
    }

    /**
     * 获取斜率
     *
     * @param p1 起点
     * @param p2 终点
     * @return 斜率
     */
    private static BigDecimal getK(PreciousPoint2D p1, PreciousPoint2D p2) {
        // p2-p1, p2是原点
        BigDecimal delta_Y = p1.getY().subtract(p2.getY());
        BigDecimal delta_X = p1.getX().subtract(p2.getX());
        return (delta_X.multiply(BigDecimal.valueOf(-1)).divide(delta_Y, 20, RoundingMode.UP));
    }

    /**
     * @param k     斜率
     * @param point 直线上的任意一点
     * @return
     * @method getConstantOfLinearEquation :求出直线方程中的常量b
     */
    private static BigDecimal getConstantOfLinearEquation(BigDecimal k, PreciousPoint2D point) {
        BigDecimal px = point.getX();
        BigDecimal py = point.getY();
        return px.multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(py));
    }

    /**
     * pythagoreanTheorem 勾股定理 求出斜边的长度
     *
     * @param x1 p1的x坐标
     * @param x2 p2的x坐标
     * @param y1 p1的y坐标
     * @param y2 p2的y坐标
     */
    private static BigDecimal pythagoreanTheorem(BigDecimal x1, BigDecimal x2, BigDecimal y1, BigDecimal y2) {
        double X = Double.parseDouble(String.valueOf(x1.subtract(x2).abs()));
        double Y = Double.parseDouble(String.valueOf(y1.subtract(y2).abs()));
        return BigDecimal.valueOf(Math.sqrt(Math.pow(X, 2) + Math.pow(Y, 2)));
    }

    /**
     * 机械臂坐标点转化成屏幕点
     *
     * @param point           机械臂坐标点
     * @param pointLeftBottom 左下点
     * @param pointLeftTop    左上点
     * @param pointRightTop   右上点
     * @return 坐标点
     */
    public static PreciousPoint2D calculateScreenPointByAnyRobotCoordinatePoint(PreciousPoint2D point, PreciousPoint2D pointLeftBottom, PreciousPoint2D pointLeftTop, PreciousPoint2D pointRightTop) {
        double a = getAlpha(pointLeftBottom, pointLeftTop);
        BigDecimal x = null;
        BigDecimal y = null;
        if (a == 90) {
            x = pointLeftTop.getX().subtract(point.getX()).abs();
            y = pointLeftTop.getY().subtract(point.getY()).abs();
        }
        if (a != 90) {
            BigDecimal kLeft = getK(pointLeftBottom, pointLeftTop);
            BigDecimal kRight = getK(pointLeftTop, pointRightTop);
            BigDecimal bLeft = getConstantOfLinearEquation(kLeft, point);
            BigDecimal bLeftEdge = getConstantOfLinearEquation(kLeft, pointLeftBottom);
            BigDecimal bRight = getConstantOfLinearEquation(kRight, point);
            BigDecimal bRightEdge = getConstantOfLinearEquation(kRight, pointRightTop);
            PreciousPoint2D pointLeft = new PreciousPoint2D();
            PreciousPoint2D pointRight = new PreciousPoint2D();
            // -x=ky+b
            pointLeft.setY(BigDecimal.valueOf(-1).multiply((bLeftEdge.subtract(bRight)).divide(kLeft.subtract(kRight), 3, RoundingMode.FLOOR)));
            pointLeft.setX(BigDecimal.valueOf(-1).multiply(kLeft.multiply(pointLeft.getY()).add(bLeftEdge)));
            // System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
            // System.out.println("point" + point);
            // System.out.println(">>>" + pointLeft);

            pointRight.setY(BigDecimal.valueOf(-1).multiply((bLeft.subtract(bRightEdge)).divide(kLeft.subtract(kRight), 3, RoundingMode.FLOOR)));
            pointRight.setX(BigDecimal.valueOf(-1).multiply(kRight.multiply(pointRight.getY()).add(bRightEdge)));
            // System.out.println(">>>" + pointRight);
            // p1p2边上的点和屏幕内任意一点的距离的分解
            y = pythagoreanTheorem(point.getX(), pointLeft.getX(), point.getY(), pointLeft.getY());
            x = pythagoreanTheorem(point.getX(), pointRight.getX(), point.getY(), pointRight.getY());
        }
        return new PreciousPoint2D(x, y);
    }

    /**
     * 屏幕坐标转换为分辨率坐标
     *
     * @param resolutionWidth       分辨率宽度
     * @param resolutionHeight      分辨率高度
     * @param screenWidth           屏幕宽度
     * @param screenHeight          屏幕高度
     * @param screenCoordinatePoint 屏幕坐标点
     * @return 屏幕分辨率坐标
     */
    public static PointInt getResolutionCoordinate(int resolutionWidth,
                                                   int resolutionHeight,
                                                   BigDecimal screenWidth,
                                                   BigDecimal screenHeight,
                                                   PreciousPoint2D screenCoordinatePoint) {
        BigDecimal horizontalPixelsPerMillimetre = BigDecimal.valueOf(resolutionWidth).divide(screenWidth, 24, RoundingMode.FLOOR);
        BigDecimal verticalPixelsPerMillimetre = BigDecimal.valueOf(resolutionHeight).divide(screenHeight, 24, RoundingMode.FLOOR);
        PointInt resolutionCoordinatePoint = new PointInt();
        resolutionCoordinatePoint.setY(screenCoordinatePoint.getX().multiply(verticalPixelsPerMillimetre).setScale(0, RoundingMode.HALF_UP).intValue());
        resolutionCoordinatePoint.setX(screenCoordinatePoint.getY().multiply(horizontalPixelsPerMillimetre).setScale(0, RoundingMode.HALF_UP).intValue());
        return resolutionCoordinatePoint;
    }

    /**
     * 获取边距分解在x轴上的距离
     *
     * @param d 边距
     * @param b beta的简写  是角度
     * @return x轴上的距离
     */
    private static BigDecimal getXShadow(BigDecimal d, double b) {
//        Math.toRadians(b)角度转弧度
        BigDecimal x = d.multiply(BigDecimal.valueOf(Math.cos(Math.toRadians(b))));
//        10位小数以后向下取整
//        x = x.setScale(20, RoundingMode.UP);
        x = x.setScale(10, RoundingMode.DOWN);
        return x;
    }

    /**
     * 获取边距分解在y轴上的距离
     *
     * @param d 边距
     * @param b beta的简写  是角度
     * @return y轴上的距离
     */
    private static BigDecimal getYShadow(BigDecimal d, double b) {
        BigDecimal y = d.multiply(BigDecimal.valueOf(Math.sin(Math.toRadians(b))));
//        y = y.setScale(20, RoundingMode.UP);
        y = y.setScale(10, RoundingMode.DOWN);
        return y;
    }

    /**
     * 获取线段的走向
     *
     * @param startPoint
     * @param endPoint
     * @return 线段的走向
     */
    public static String getLineTrend(PreciousPoint2D startPoint, PreciousPoint2D endPoint, double a) {
        String trend = null;
        BigDecimal startX = startPoint.getX();
        BigDecimal startY = startPoint.getY();
        BigDecimal endX = endPoint.getX();
        BigDecimal endY = endPoint.getY();
        if (a < 90) {
            if (startX.compareTo(endX) > 0) {
                if (startY.compareTo(endY) < 0) trend = "toRightTop";
            }
            if (startX.compareTo(endX) < 0) {
                if (startY.compareTo(endY) > 0) trend = "toLeftBottom";
            }
        }
        if (a > 90) {
            if (startX.compareTo(endX) > 0) {
                if (startY.compareTo(endY) > 0) trend = "toLeftTop";
            }
            if (startX.compareTo(endX) < 0) {
                if (startY.compareTo(endY) < 0) trend = "toRightBottom";
            }
        }
        if (a == 90) {
            if (startX.compareTo(endX) < 0) {
                trend = "toBottom";
            } else {
                trend = "toTop";
            }
        }
        if (a == 0) {
            if (startY.compareTo(endY) < 0) {
                trend = "toRight";
            } else {
                trend = "toLeft";
            }
        }
        return trend;
    }

    /**
     * 分割线段
     *
     * @param startPoint    起点
     * @param endPoint      终点
     * @param segmentLength 分割线段长度
     * @return 线段数组
     */
    public static PreciousPoint2D[] splitLine(PreciousPoint2D startPoint, PreciousPoint2D endPoint, BigDecimal segmentLength) {
        double a = getAlpha(startPoint, endPoint);
        BigDecimal xLimit = endPoint.getX().subtract(startPoint.getX()).abs();
        String lineTrend = getLineTrend(startPoint, endPoint, a);
        PreciousPoint2D[] points;
        if (a != 90 && a != 0) {
            double b = getBeta(a);
            BigDecimal xShadow = getXShadow(segmentLength, b);
            BigDecimal yShadow = getYShadow(segmentLength, b);
            int num = Integer.parseInt((xLimit.divide(xShadow, 0, RoundingMode.FLOOR).toString()));
            if (xLimit.divide(xShadow, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            points = new PreciousPoint2D[num + 2];
            points[0] = startPoint;
            if (lineTrend.equals("toRightBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().add(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeftTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().subtract(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toRightTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().add(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeftBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().subtract(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        } else if (a == 0) {
            BigDecimal yLimit = endPoint.getY().subtract(startPoint.getY()).abs();
            int num = Integer.parseInt(yLimit.divide(segmentLength, 0, RoundingMode.FLOOR).toString());
            if (yLimit.divide(segmentLength, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            points = new PreciousPoint2D[num + 2];
            points[0] = startPoint;
            if (lineTrend.equals("toRight")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX();
                    BigDecimal yCoordinate = startPoint.getY().add(segmentLength.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeft")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX();
                    BigDecimal yCoordinate = startPoint.getY().subtract(segmentLength.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        } else {
            //a=90
            int num = Integer.parseInt(xLimit.divide(segmentLength, 0, RoundingMode.FLOOR).toString());
            if (xLimit.divide(segmentLength, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            points = new PreciousPoint2D[num + 2];

            points[0] = startPoint;
            if (lineTrend.equals("toTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(segmentLength.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY();
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(segmentLength.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY();
                    points[i + 1] = new PreciousPoint2D();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        }
//        System.out.println(Arrays.toString(points));
        return points;
    }

    /**
     * 分割圆
     *
     * @param pivot  圆心
     * @param radius 半径
     * @param degree 角度
     * @return
     */
    public static PreciousPoint2D[] splitCircle(PreciousPoint2D pivot, BigDecimal radius, double degree) {
        // 圆的参数方程

        int times = (int) (360 / degree);
        PreciousPoint2D[] circlePoints = new PreciousPoint2D[times];
        for (int i = 0; i < times; i++) {
            BigDecimal x = BigDecimal.valueOf(Math.sin(Math.toRadians(degree))).multiply(radius);
            BigDecimal y = BigDecimal.valueOf(Math.cos(Math.toRadians(degree))).multiply(radius);
            circlePoints[i] = new PreciousPoint2D(pivot.getX().add(x), pivot.getY().add(y));
        }
        return circlePoints;
    }

}
