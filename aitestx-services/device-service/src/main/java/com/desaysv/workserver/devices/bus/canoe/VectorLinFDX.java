package com.desaysv.workserver.devices.bus.canoe;

import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.BaseLinDevice;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.fdx.FdxDataProcessor;
import com.desaysv.workserver.devices.bus.fdx.FdxSocket;
import com.desaysv.workserver.devices.bus.interfaces.ILinSequence;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.StrUtils;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;

public class VectorLinFDX extends BaseLinDevice implements ILinSequence {
    private FdxSocket socket;

    public VectorLinFDX(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        socket = FdxSocket.getInstance();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.VECTOR_LIN;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        try {
            if (!socket.isCANoeRunning()) {
                FdxSocket.setInstanceNULL();
                socket = FdxSocket.getInstance();
                return socket.isCANoeRunning();
            }
        } catch (BusError e) {
            throw new DeviceOpenException(e);
        }
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean setLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_LIN_SIGNAL_LOG, signalName, signalValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinSignalInfo(deviceChannel, ecuNodeName, messageName, signalName, signalValue)) {
            ActionSequencesLoggerUtil.info(SET_LIN_SIGNAL_LOG, signalName, signalValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinSignalCounter()) {
            ActionSequencesLoggerUtil.info(SET_LIN_SIGNAL_LOG, signalName, signalValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        boolean result = socket.getReturnValue();
        ActionSequencesLoggerUtil.info(SET_LIN_SIGNAL_LOG, signalName, signalValue, result ? SUCCESS : FAIL, result ? SET_SUCCESS : GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
        return result;
    }

    @Override
    public boolean setLinSingleMsgControl(Integer deviceChannel, String ecuNodeName, String messageID, int messageStatus) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_LIN_SINGLE_MSG_STATUS_LOG, messageID, messageStatus, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinSingleMsgStatusInfo(deviceChannel, ecuNodeName, StrUtils.hexStrToInt(messageID), messageStatus)) {
            ActionSequencesLoggerUtil.info(SET_LIN_SINGLE_MSG_STATUS_LOG, messageID, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinSingleMsgStatusCounter()) {
            ActionSequencesLoggerUtil.info(SET_LIN_SINGLE_MSG_STATUS_LOG, messageID, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        boolean result = socket.getReturnValue();
        ActionSequencesLoggerUtil.info(SET_LIN_SINGLE_MSG_STATUS_LOG, messageID, messageStatus, result ? SUCCESS : FAIL, result ? SET_SUCCESS : GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
        return result;
    }

    @Override
    public boolean setLinAllMsgStatus(String ecuNodeName, int messageStatus) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_LIN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinEcuAllMsgStatusInfo(ecuNodeName, messageStatus)) {
            ActionSequencesLoggerUtil.info(SET_LIN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetLinEcuAllMsgStatusCounter()) {
            ActionSequencesLoggerUtil.info(SET_LIN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        boolean result = socket.getReturnValue();
        ActionSequencesLoggerUtil.info(SET_LIN_ECU_ALL_MSG_STATUS_LOG, messageStatus, result ? SUCCESS : FAIL, result ? SET_SUCCESS : GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
        return result;
    }

    @Override
    public double fetchLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws BusError {
        long sTime = System.currentTimeMillis();
        double fetchSignalValue = -1000.0;
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(FETCH_LIN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        if (!socket.sendGetLinSignalInfo(deviceChannel, ecuNodeName, messageName, signalName, 100)) {
            ActionSequencesLoggerUtil.info(FETCH_LIN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        if (!socket.sendGetLinSignalCounter()) {
            ActionSequencesLoggerUtil.info(FETCH_LIN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        boolean result = socket.getReturnValue();
        if (!result) {
            ActionSequencesLoggerUtil.info(FETCH_LIN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        fetchSignalValue = FdxDataProcessor.parseSignalValue(socket.reqLinSignalBytes());
        ActionSequencesLoggerUtil.info(FETCH_LIN_SIGNAL_LOG, signalName, fetchSignalValue, result ? SUCCESS : FAIL, SET_SUCCESS, System.currentTimeMillis() - sTime);
        return fetchSignalValue;
    }

    @Override
    public boolean setLinPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        return false;
    }

    @Override
    public boolean sendLinMessage(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        return false;
    }

    @Override
    public boolean setLinWakeUp(Integer deviceChannel, int wakeUpCommand) throws BusError {
        return false;
    }

    @Override
    public String readLinDataByIdHex(Integer deviceChannel, String messageId) {
        return null;
    }

    @Override
    public String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError {
        return null;
    }

    @Override
    public boolean fetchLinMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return lastCheckLinMsgID(deviceChannel, messageId, exist, 2000);  //默认持续检测2000ms
    }

    @Override
    public boolean lastCheckLinMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendLastCheckMsgIDInfo(deviceChannel, messageId, exist, milliSecond)) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetStartCheckLinMsg()) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_XCP_VAR_BTN_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setLinChannelMsgStatus(Integer deviceChannel, int status) {
        return false;
    }

}
