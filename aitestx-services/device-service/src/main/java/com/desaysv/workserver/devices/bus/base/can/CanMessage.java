package com.desaysv.workserver.devices.bus.base.can;

import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.reader.ObjectReader;
import com.desaysv.workserver.entity.BaseMessage;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Base64;
import java.util.List;

/**
 * CAN报文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanMessage extends BaseMessage {
    private double timestamp; // 时间戳
    private int arbitrationId; // 报文id
    private boolean extendedId; // 是否拓展帧id
    private boolean remoteFrame; // 是否远程帧
    private boolean errorFrame;// 是否错误帧
    private Integer channel; // 通道1开始，不能直接引用
    private boolean canFd; // 是否CanFD
    private String sendMethod; //发送方式
    private int dlc; // 数据长度
    @JSONField(deserializeUsing = HexStringToByteArrayReader.class)
    private byte[] data; // 报文数据
    private Float duration; //秒
    private float period; //秒
    private int sendTimes = -1; // 发送次数,默认一直发送
    private int framesPerSendNum = 1; //每次发送帧数
    private boolean loop;//循环发送
    private List<byte[]> loopDatas;//循环发送报文数据
    private boolean e2eEnabled;
    private String e2eType;
    private String direction;

    // 自定义的反序列化器
    public static class HexStringToByteArrayReader implements ObjectReader<byte[]> {
        @Override
        public byte[] readObject(com.alibaba.fastjson2.JSONReader jsonReader, java.lang.reflect.Type fieldType, Object fieldName, long features) {
            String dataString = jsonReader.readString();

            // 判断数据是否为 Base64 格式(兼容旧版本)
            if (dataString.matches("^[A-Za-z0-9+/]+={0,2}$") && dataString.length() % 4 == 0) {
                try {
                    return Base64.getDecoder().decode(dataString);
                } catch (IllegalArgumentException e) {
                    System.err.println("Base64 解码失败: " + dataString);
                    return new byte[0];
                }
            } else {
                try {
                    return ByteUtils.hexStringToByteArray(dataString);
                } catch (IllegalArgumentException e) {
                    System.err.println("Hex 解码失败: " + dataString);
                    return new byte[0];
                }
            }
        }
    }

    public void setDuration(Float duration) {
        if (duration == null || duration < 0) {
            this.duration = null;
        } else {
            this.duration = duration;
        }
    }

    public CanMessage() {

    }

    public CanMessage(int arbitrationId, byte[] data) {
        this(0.0f, arbitrationId, false, false, false, null, null, data, false);
    }

    /**
     * CAN报文
     *
     * @param timestamp     时间戳
     * @param arbitrationId 报文ID
     * @param extendedId    是否拓展ID
     * @param remoteFrame   是否远程帧
     * @param errorFrame    是否错误帧
     * @param channel       通道
     * @param dlc           报文长度
     * @param data          报文数据
     * @param canFd         是否CanFd
     */
    public CanMessage(float timestamp, int arbitrationId, boolean extendedId, boolean remoteFrame,
                      boolean errorFrame, Integer channel, Integer dlc, byte[] data, boolean canFd) {
        this.timestamp = timestamp;
        this.arbitrationId = arbitrationId;
        this.extendedId = extendedId;
        this.remoteFrame = remoteFrame;
        this.errorFrame = errorFrame;
        this.channel = channel;
        this.canFd = canFd;
        this.data = data;
        this.dlc = dlc == null ? data.length : dlc;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("Timestamp: %15.6f ", timestamp));
        sb.append(getHexId());
        sb.append(extendedId ? " X" : " S");
        sb.append(errorFrame ? "E" : " ");
        sb.append(remoteFrame ? "R" : " ");
        sb.append(canFd ? "F" : " ");
        sb.append(String.format("DLC: %2d ", dlc));
        sb.append(loop ? "loop" : " ");
        if (loop) {
            sb.append(" ");
            for (byte[] loopData : loopDatas) {
                for (byte loopDatum : loopData) {
                    sb.append(String.format("%02X", loopDatum));
                }
                sb.append(",");
            }
        } else {
            for (int i = 0; i < Math.min(dlc, data.length); i++) {
                sb.append(" ").append(String.format("%02X", data[i]));
            }
        }
        sb.append(String.format(" Channel: %d", channel));
        sb.append(String.format(" Period:%.0fms", period * 1000));
        return sb.toString();
    }

    public int length() {
        return dlc;
    }

    public String getHexId() {
        return String.format("0x%X", arbitrationId);
    }

    public String getOfficialHexId() {
        return String.format(extendedId ? "ID: %08X " : "ID: %04X", arbitrationId);
    }

}
