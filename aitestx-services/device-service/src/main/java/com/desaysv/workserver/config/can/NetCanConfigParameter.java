package com.desaysv.workserver.config.can;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/5/13 9:58
 * @Version: 1.0
 * @Desc : netCan配置信息
 */
@Data
public class NetCanConfigParameter implements Serializable {

    private int channel = 1; //通道1开始
    private String protocol = "CAN FD";//协议
    private boolean accelerate = true;//CANFD加速 "是"/"否"
    private String localPort = "4001";//本地端口
    private String workMode = "客户端";//工作模式
    private String ip = "*************";//ip地址
    private String workPort = "8000";//工作端口
    private boolean canFd;


}
