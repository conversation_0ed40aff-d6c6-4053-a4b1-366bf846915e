package com.desaysv.workserver.devices.tcpserver;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.tcpserver.interfaces.ITcpServer;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * @author: Qin<PERSON>ao
 * @description: TcpServerDevice
 * @date: 2024/9/4 10:29
 */
@Slf4j
public class TcpServerDevice extends Device implements ITcpServer {
    public static final List<String> IP_ADDRESS = new ArrayList<>();
    private TCPServerControl tcpServer;
    private String mustValue;
    private String forbidValue;
    private String judgeValue;
    private static final int EQUAL = 1;
    private static final int GREATER = 2;
    private static final int LESS = 3;
    private static final int GREATER_EQUAL = 4;
    private static final int LESS_EQUAL = 5;

    static {
        for (int i = 0; i < 2; i++) {
            IP_ADDRESS.add(DeviceParams.defaultLocalIP);
        }
        List<String> currentIpAddresses = getAllCurrentIpAddresses();
        for (int i = 0; i < 2; i++) {
            IP_ADDRESS.addAll(currentIpAddresses);
        }
    }

    private static List<String> getAllCurrentIpAddresses() {
        List<String> ipAddresses = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    if (!inetAddress.isLoopbackAddress() && inetAddress.isSiteLocalAddress()) {
                        ipAddresses.add(inetAddress.getHostAddress());
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ipAddresses;
    }

    public TcpServerDevice() {
        this(new DeviceOperationParameter());
    }

    public TcpServerDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TCP_SERVER;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.TcpServer.TCP_SERVER;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
//        tcpServer = TCPServerControl.newInstance(getDeviceName(), getDeviceOperationParameter().getPort());
        tcpServer = TCPServerControl.getInstance(getDeviceName(), getDeviceOperationParameter().getPort());
        tcpServer.startServer();
        tcpServer.receiveData();
        return tcpServer.isServerRunning();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        tcpServer.stopServer();
        return !tcpServer.isServerRunning();
    }

    @Override
    public OperationResult tcpServerSend(String messageText) {
        OperationResult operationResult = new OperationResult();
        if (tcpServer.sendData(messageText)) {
            operationResult.setMessage("发送成功：" + messageText);
            operationResult.ok();
        } else {
            operationResult.fail("发送失败：" + messageText);
        }
        return operationResult;
    }


    public OperationResult tcpServerSendAndJudge(TcpMessage tcpMessage) {
        OperationResult operationResult;
        String messageText = tcpMessage.getMessage();
        String receive = tcpMessage.getReceive();
        int judgeType = tcpMessage.getJudgeType();
        if (judgeType == 0) {
            tcpMustExistMonitorStart(receive);
        } else {
            tcpJudgeMonitorStart(receive);
        }
        operationResult = tcpServerSend(messageText);
        if (operationResult.isOk()) {
            try {
                Thread.sleep(tcpMessage.getOutTime() * 1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            if (judgeType == 0) {
                operationResult = tcpMustExistMonitorEnd();
            } else {
                operationResult = tcpJudgeMonitorEnd(judgeType, tcpMessage.getJudgeContent());
            }
        } else {
            operationResult.fail("发送失败：" + messageText);
        }
        return operationResult;
    }

    @Override
    public OperationResult tcpServerSend(TcpMessage tcpMessage) {
        OperationResult operationResult;
        if (tcpMessage.getReceive().isEmpty()) {
            operationResult = tcpServerSend(tcpMessage.getMessage());
        } else {
            if (tcpMessage.isJudge()) {
                operationResult = tcpServerSendAndJudge(tcpMessage);
            } else {
                operationResult = tcpServerSendAndJudge(tcpMessage);
            }
        }
        return operationResult;
    }

    @Override
    public boolean tcpServerJudge(String messageText) {
        List<String> recentDataHistory = tcpServer.getRecentDataHistory();
        return recentDataHistory.stream().anyMatch(data -> data.contains(messageText));
    }

    private void startDataCollection(String value, int type) {
        switch (type) {
            case 1:
                mustValue = value;
                break;
            case 2:
                forbidValue = value;
                break;
            case 3:
                judgeValue = value;
                break;
        }
        tcpServer.startCollectingData();
    }

    private OperationResult stopDataCollectionAndCheck(String value, boolean shouldContain) {
        OperationResult operationResult = new OperationResult();
        tcpServer.stopCollectingData();
        List<String> collectedData = tcpServer.getCollectedData();
        log.info("收集到的数据: {}", collectedData);
        boolean found = collectedData.stream().anyMatch(data -> data.contains(value));
        if (shouldContain) {
            if (found) {
                operationResult.setMessage("数据包含指定字符串：" + value);
                log.info("数据包含指定字符串：" + value);
                operationResult.ok();
            } else {
                operationResult.fail("数据不包含指定字符串：" + value);
            }
        } else {
            if (found) {
                operationResult.fail("数据包含禁止字符串：" + value);
                log.info("数据包含禁止字符串：" + value);
            } else {
                operationResult.setMessage("数据不包含禁止字符串：" + value);
                operationResult.ok();
            }
        }
        return operationResult;
    }

    private OperationResult stopDataCollectionAndJudge(String target, int judgeType, double judgeContent) {
        OperationResult operationResult = new OperationResult();
        tcpServer.stopCollectingData();
        List<String> collectedData = tcpServer.getCollectedData();
        log.info("收集到的数据: {}", collectedData);

        try {
            boolean allMatch = true;
            List<String> unmatchedData = new ArrayList<>();
            List<String> notSatisfyingData = new ArrayList<>();

            for (String data : collectedData) {
                if (data.contains(target)) {
                    String[] parts = data.split(target);
                    if (parts.length > 1) {
                        double receivedNumber = Double.parseDouble(parts[1].trim());
                        if (!compareNumbers(receivedNumber, judgeContent, judgeType)) {
                            allMatch = false;
                            notSatisfyingData.add(data + " (" + receivedNumber + " " + getComparisonOperator(judgeType) + " " + judgeContent + ")");
                            break;
                        }
                    } else {
                        allMatch = false;
                        log.info("数据格式不正确: {}", data);
                        break;
                    }
                } else {
                    allMatch = false;
                    unmatchedData.add(data);
                }
            }

            if (allMatch) {
                operationResult.setMessage("所有数据都满足条件");
                operationResult.ok();
            } else {
                StringBuilder failMessage = new StringBuilder("部分数据不满足条件");
                if (!unmatchedData.isEmpty()) {
                    failMessage.append(", 未匹配到目标字符串的数据: ").append(String.join(", ", unmatchedData));
                }
                if (!notSatisfyingData.isEmpty()) {
                    failMessage.append(", 不满足条件的数据: ").append(String.join(", ", notSatisfyingData));
                }
                operationResult.fail(failMessage.toString());
            }
        } catch (NumberFormatException e) {
            operationResult.fail("无法解析数字: " + e.getMessage());
        }

        return operationResult;
    }

    private String getComparisonOperator(int judgeType) {
        switch (judgeType) {
            case EQUAL:
                return "==";
            case GREATER:
                return ">";
            case LESS:
                return "<";
            case GREATER_EQUAL:
                return ">=";
            case LESS_EQUAL:
                return "<=";
            default:
                throw new IllegalArgumentException("未知的操作符: " + judgeType);
        }
    }


    private boolean compareNumbers(double receivedNumber, double target, int judgeType) {
        switch (judgeType) {
            case EQUAL:
                return receivedNumber == target;
            case GREATER:
                return receivedNumber > target;
            case LESS:
                return receivedNumber < target;
            case GREATER_EQUAL:
                return receivedNumber >= target;
            case LESS_EQUAL:
                return receivedNumber <= target;
            default:
                throw new IllegalArgumentException("未知的操作符: " + judgeType);
        }
    }

    public OperationResult tcpMustExistMonitorStart(String must) {
        startDataCollection(must, 1);
        return new OperationResult().ok();
    }

    public OperationResult tcpMustExistMonitorEnd() {
        return stopDataCollectionAndCheck(mustValue, true);
    }

    public OperationResult tcpJudgeMonitorStart(String judge) {
        startDataCollection(judge, 3);
        return new OperationResult().ok();
    }

    public OperationResult tcpJudgeMonitorEnd(int judgeType, double judgeContent) {
        return stopDataCollectionAndJudge(judgeValue, judgeType, judgeContent);
    }

    public OperationResult tcpForbidExistMonitorStart(String forbid) {
        startDataCollection(forbid, 2);
        return new OperationResult().ok();
    }

    public OperationResult tcpForbidExistMonitorEnd() {
        return stopDataCollectionAndCheck(forbidValue, false);
    }
}
