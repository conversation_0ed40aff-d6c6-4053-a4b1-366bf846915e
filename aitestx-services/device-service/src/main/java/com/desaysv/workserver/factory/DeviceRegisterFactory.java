package com.desaysv.workserver.factory;

import com.desaysv.workserver.base.manager.RegisterManager;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.factory.interfaces.AbstractDeviceRegisterFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 15:29
 * @description : 设备注册工厂
 * @modified By : lwj
 * @since : 2022-3-17
 */
@Slf4j
@Service
@Lazy
public class DeviceRegisterFactory extends RegisterManager implements AbstractDeviceRegisterFactory {
    private final Map<String, Device> deviceMap = new LinkedHashMap<>();

    @Autowired
    private DeviceFactory deviceFactory;

    @Override
    public Device registerDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceName = deviceRegisterForm.getDeviceName();
        Device device;
        if (!deviceMap.containsKey(deviceName)) {
            //设备从来没注册过，进行注册
            device = deviceFactory.createDevice(deviceRegisterForm);
            if (device != null) {
                log.info("新注册设备:{},实例:{}", deviceName, device);
                //FIXME：失败的设备不要放进deviceMap
                deviceMap.put(deviceName, device);
                registerToManager(deviceName, device);
                DeviceFileManager.getInstance().writeDeviceRegisterForm(deviceRegisterForm);
            }
        } else {
            device = deviceMap.get(deviceName);
            log.info("设备已注册过:{},实例:{}", deviceName, device);
        }
        return device;
    }

    @Override
    public List<Device> getAllDevices() {
        return new ArrayList<>(deviceMap.values());
    }

    @Override
    public Device getDevice(String deviceName) {
        //TODO:考虑改为deviceUniqueCode获取
        if (deviceName.contains("TOSUN_CAN")) {
            //获取TC开头的第一个设备
            for (Device device : deviceMap.values()) {
                if (device.getDeviceName().startsWith("TC")) {
                    return device.getDevice();
                }
            }
        }
        Device device = deviceMap.get(deviceName);
        return device != null ? device.getDevice() : null;
    }

    public boolean isRegistered(String deviceName) {
        return deviceMap.containsKey(deviceName);
    }

    @Override
    public boolean closeDevice(String deviceName) throws DeviceCloseException {
        boolean ok = false;
        Device device = getDevice(deviceName);
        if (device != null) {
            ok = device.closeForOperationResult().isOk();
        }
        if (ok) {
            log.info("已经断开设备:{}", deviceName);
        }
        return ok;
    }

    /**
     * 销毁设备时 释放设备资源
     */
    @Override
    public void disposeDevice(String deviceName) {
        Device device = getDevice(deviceName);
        if (device != null) {
            try {
                device.disposeForOperationResult().ok();
            } catch (Exception ignored) {
                log.warn("设备{}释放资源失败", deviceName);
            }
        }
    }

    @Override
    public boolean removeDevice(String deviceName) {
        deviceMap.remove(deviceName);
        return true;
    }

    @Override
    public boolean unregisterAndCloseDevice(String deviceName) {
        Device device = getDevice(deviceName);
        boolean isOk = false;
        if (device != null) {
            try {
                closeDevice(deviceName);
                // 释放设备资源
                disposeDevice(deviceName);
            } catch (DeviceCloseException ignored) {
            } finally {
                //TODO：考虑只是断开设备，而不是完全注销
                isOk = removeDevice(deviceName);
                if (isOk) {
                    log.info("已经注销设备:{}", deviceName);
                }
            }
        }
        return isOk;
    }

    @Override
    public boolean unregisterAndCloseAllDevices() {
        boolean isOk = true;
        List<String> deviceLists = new ArrayList<>(deviceMap.keySet());
        log.info("注销所有设备:{}", deviceLists);
        for (String deviceName : deviceLists) {
            isOk &= unregisterAndCloseDevice(deviceName);
        }
        return isOk;
    }

    @Override
    public boolean openDevice(Device device) {
        return device.openForOperationResult().isOk();
    }

    @Override
    public boolean closeDevice(Device device) throws DeviceCloseException {
        return device.closeForOperationResult().isOk();
    }

}
