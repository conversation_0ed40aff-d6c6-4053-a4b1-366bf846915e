package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.tcpserver.TcpServerDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.PingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: QinHao
 * @description: findAllTcpServer
 * @date: 2024/9/2 11:11
 */
@Component
@Slf4j
@Lazy
public class TcpServerFinder {
    public List<Device> findAllTcpServer(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        int portIndex = 1;

        try {
            for (String ipAddr : TcpServerDevice.IP_ADDRESS) {
                if (PingUtils.ping(ipAddr)) {
                    TcpServerDevice device = new TcpServerDevice();
                    device.setDeviceName(ipAddr + "(" + portIndex + ")");
                    device.setDeviceUniqueCode(device.getDeviceName());
                    int deviceIndex = Device.getDeviceModelIndex(deviceModel);
                    device.setAliasName(deviceModel + "#" + deviceIndex);
                    device.setDeviceOperationParameter(new DeviceOperationParameter());
                    devices.add(device);
                }
                portIndex++;
            }
        } catch (Exception e) {
            log.debug(e.getMessage(), e);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
        }
        return devices;
    }
}
