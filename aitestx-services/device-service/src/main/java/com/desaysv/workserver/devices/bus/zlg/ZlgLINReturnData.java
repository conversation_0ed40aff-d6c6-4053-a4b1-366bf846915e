package com.desaysv.workserver.devices.bus.zlg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ZlgLINReturnData {

    /**
     * 时间戳
     */
    public long timestamp;

    /**
     * 帧的标识符。
     */
    public int id;

    /**
     * LIN类型
     */
    private String eventType;

    /**
     * 流向 TX发送方向 RX接收方向
     */
    private String dir;

    /**
     * 负载数据
     */
    public byte[] data = new byte[64];
}
