package com.desaysv.workserver.devices.robot.type.mg400.entity;

import lombok.Data;

/**
 * 扩展轴运动到目标角度或位置
 */
@Data
public class MovJExtEntity {

    //必选参数：角度/距离：取决于高级设置中的类型：
    // - 类型为关节：则以输入参数为角度处理
    // - 类型为直线：则以输入参数为距离处理
    private double distanceOrAngle;

    private Integer speedE;//运动速度比例，取值范围：1~100

    private Integer accE; //运动加速度比例，取值范围：1~100

    //同步标识，取值范围：0或1。SYNC = 0表示异步执行，调用后立即返回，但
    //不关注指令执行情况；SYNC = 1表示同步执行，调用后，待指令执行完才返回
    private Integer sync;

    public void setSync(boolean sync) {
        this.sync = sync ? 1 : 0;
    }
}
