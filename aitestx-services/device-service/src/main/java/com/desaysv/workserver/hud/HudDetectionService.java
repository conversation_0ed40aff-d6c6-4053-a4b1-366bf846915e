package com.desaysv.workserver.hud;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.stream.StreamService;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class HudDetectionService {

    private final StreamService streamService;

    public HudDetectionService(StreamService streamService) {
        this.streamService = streamService;
    }

    /**
     * 执行HUD检测
     * @param request HUD检测请求对象，包含效果图片列表、相似度和相机UUID
     * @return HUD检测响应对象，包含效果图片文件列表、实拍图片文件列表和匹配结果列表
     * @throws Exception 抛出异常
     */
    public HudDetectionResponse detectHud(HudDetectionRequest request) throws Exception {
        deleteOldImages();
        log.info("开始检测");
        List<String> effectImages = request.getEffectImageList(); // 效果图片列表
        log.info("效果图片：{}",effectImages.toString());
        double similarity = request.getSimilarity(); // 相似度阈值
        log.info("相似度阈值：{}",similarity);
        String cameraUuid = request.getCameraUuid(); // 相机UUID
        log.info("相机uuid：{}",cameraUuid);

        List<String> realImages = new ArrayList<>(); // 实拍图片文件列表
        List<String> matchResults = new ArrayList<>(); // 匹配结果列表

        for (int i = 0; i < effectImages.size(); i++) {
            String effectImage = effectImages.get(i);
            try {
                log.info("开始投影第{}张照片",i+1);
                // 创建ProcessBuilder对象
                ProcessBuilder pb = new ProcessBuilder("lib\\PersistentPublishSubscribePublish\\PPSP.exe", "-n", Integer.toString(i+1));
                // 设置工作目录
                pb.directory(new java.io.File("lib\\PersistentPublishSubscribePublish"));
                // 重定向错误流到标准输出流
                pb.redirectErrorStream(true);
                // 开始进程
                Process process = pb.start();
                // 读取标准输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                boolean serialPortNotFound = false;
                while ((line = reader.readLine()) != null) {
                    log.info("Output from PPSP.exe -n {}: {}", i + 1, line);
                    if ("No available serial ports found.".equals(line)) {
                        serialPortNotFound = true;
                        break; // 如果找到了错误信息，可以选择跳出循环
                    }
                }
                if (serialPortNotFound) {
                    log.error("投影切换失败!!!");
                    throw new ProjectionSwitchException("投影切换失败");
                }
                try {
                    // 暂停5秒（5000毫秒）
                    Thread.sleep(5000);
                    log.info("等待了5秒，这是第 " + (i + 1) + " 次循环");
                } catch (InterruptedException e) {
                    // 处理中断异常
                    log.info("线程被中断");
                    // 可以选择恢复中断状态
                    Thread.currentThread().interrupt();
                }
                log.info("投影第{}张照片成功，开始拍摄实际照片",i+1);
                // 调用grab方法进行拍照（示例代码）
                String realImage = grabImage(cameraUuid);
                log.info("拍摄实际照片：{}",realImage);
                realImages.add(realImage);
                // 发送效果图片和实拍图片进行比较
                String matchResult = compareImages(effectImage, realImage, similarity);
                log.info("两张照片匹配结果：{}",matchResult);
                matchResults.add(matchResult);
            } catch (Exception e) {
                log.error("发生错误：", e);
                throw e;
            }
        }
        return new HudDetectionResponse(effectImages, realImages, matchResults);
    }

    // 自定义异常类
    class ProjectionSwitchException extends RuntimeException {
        public ProjectionSwitchException(String message) {
            super(message);
        }
    }

    /**
     * 使用相机UUID拍照
     * @param cameraUuid 相机UUID
     * @return 返回捕获的图片路径或文件名
     */
    public String grabImage(String cameraUuid) throws OperationFailNotification, IOException {
        log.info("调用相机");
        Frame frame = streamService.grab(cameraUuid);
        log.info("拍摄实际frame：{}", frame);

        // Step 1: 转换Frame对象为BufferedImage
        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage bufferedImage = converter.convert(frame);
        log.info("拍摄实际bufferedImage：{}", bufferedImage);

        // Step 2: 创建或获取hudPicture文件夹
        Path hudPictureDir = Paths.get("hudPicture");
        if (!Files.exists(hudPictureDir)) {
            Files.createDirectories(hudPictureDir);
            log.info("创建hudPicture文件夹");
        }

        // Step 3: 将BufferedImage保存为file
        File outputFile = new File(String.valueOf(hudPictureDir.resolve("hud_image_" + System.currentTimeMillis() + ".png").toFile()));
        ImageIO.write(bufferedImage, "PNG", outputFile);
        log.info("拍摄照片保存成功");

        // Step 4: 返回文件绝对路径
        return outputFile.getAbsolutePath();
    }

    /**
     * 删除超过15天的图片
     */
    private void deleteOldImages() {
        Path hudPictureDir = Paths.get("hudPicture");
        if (!Files.exists(hudPictureDir)) return; // 如果文件夹不存在则直接返回

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(hudPictureDir)) {
            for (Path entry : stream) {
                BasicFileAttributes attrs = Files.readAttributes(entry, BasicFileAttributes.class);
                Instant fileCreationTime = attrs.creationTime().toInstant();
                Instant halfAMonthAgo = Instant.now().minus(15, ChronoUnit.DAYS);

                if (fileCreationTime.isBefore(halfAMonthAgo)) {
                    Files.delete(entry);
                    log.info("删除过期照片: {}", entry.toString());
                }
            }
        } catch (IOException e) {
            log.error("删除过期照片时发生错误", e);
        }
    }

    /**
     * 比较效果图片和实拍图片
     * @param effectImage 效果图片路径
     * @param realImage 实拍图片路径
     * @param similarity 相似度阈值
     * @return 返回比较结果字符串
     */
    public String compareImages(String effectImage, String realImage, double similarity) {
        // 调用比较服务
        String url = "http://*************:8001/compare-images";
        try {
            // 使用effectImage、realImage和similarity发起POST请求
            URL postUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) postUrl.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("accept", "application/json");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=----Boundary123456789");

            try (OutputStream outputStream = conn.getOutputStream()) {
                String boundary = "----Boundary123456789";

                // 写入ui图片部分
                String uiPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"ui\"; filename=\"" + new File(effectImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(uiPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(effectImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入real图片部分
                String realPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"real\"; filename=\"" + new File(realImage).getName() + "\"\r\n" +
                        "Content-Type: image/png\r\n\r\n";
                outputStream.write(realPart.getBytes(StandardCharsets.UTF_8));
                Files.copy(Paths.get(realImage), outputStream);
                outputStream.write("\r\n".getBytes(StandardCharsets.UTF_8));

                // 写入param部分
                String paramPart = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"param\"\r\n\r\n" +
                        similarity + "\r\n" +
                        "--" + boundary + "--\r\n";
                outputStream.write(paramPart.getBytes(StandardCharsets.UTF_8));
            }

            // 读取响应
            int responseCode = conn.getResponseCode();
            log.info("responseCode：{}", responseCode);
            if (responseCode != HttpURLConnection.HTTP_OK) {
                // 处理错误情况
                return "Error: HTTP response code " + responseCode;
            }

            try (java.util.Scanner scanner = new java.util.Scanner(conn.getInputStream())) {
                String response = scanner.useDelimiter("\\A").next();
                log.info("匹配结果{}", response);

                // 使用 Gson 解析 JSON 响应
                JsonObject jsonObject = JsonParser.parseString(response).getAsJsonObject();
                boolean result = jsonObject.get("result").getAsBoolean();  // 提取 result 值
                log.info("result: {}", result);

                return Boolean.toString(result);  // 返回布尔值的字符串形式
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return "Error: " + e.getMessage();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "Error: " + e.getMessage();
        }
    }
}