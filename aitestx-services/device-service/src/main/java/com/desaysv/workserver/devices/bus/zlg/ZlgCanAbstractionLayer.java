package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.sun.jna.Pointer;

public class ZlgCanAbstractionLayer {
    public static final int SEND_OK = 0;

    public boolean blockingSend(CanMessage message, float timeout) {
        return true;
    }

    public boolean send(Pointer channelHandle, CanMessage message) {
//        System.out.println("send:" + message.getHexId() + "->" + ByteUtils.byteArrayToHexString(message.getData()));
        return ZlgApi.sendCanMessage(channelHandle, message);
    }

    public void sendPeriod(Pointer deviceHandle, CanMessage message) {
         ZlgApi.sendCanPeriodMessage(deviceHandle, message);
    }
}
