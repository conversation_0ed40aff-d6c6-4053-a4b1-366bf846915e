package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.entity.CanReplyInfo;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

import java.io.IOException;
import java.util.Map;

/**
 * CAN设备
 */
@Getter
public abstract class CanDevice extends BaseCanDevice implements TestProcessListener {

    public CanDevice() {
        this(new DeviceOperationParameter());
    }

    public CanDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    public abstract boolean sendAllPeriodicCanMessage(Integer deviceChannel) throws BusError;

    public boolean stopAllCanMessage() {
        return stopAllCanMessage(null);
    }

    public abstract boolean stopAllCanMessage(Integer deviceChannel);

    public abstract boolean pauseAllCanMessage(Integer deviceChannel);

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(getDeviceName(), this);
        }
    }

    @Override
    public boolean close() throws DeviceCloseException {
        TestProcessManager.removeTestProcessListener(getDeviceName());
        return true;
    }

    /**
     * 发送报文
     *
     * @param message 报文
     */
    public abstract void send(CanMessage message) throws BusError;

    /**
     * 停发报文
     *
     * @param message 报文
     * @return 是否停发报文成功
     */
    protected abstract void stopSend(CanMessage message);

    protected abstract CyclicTask sendCanMessage(Integer deviceChannel, CanMessage message) throws BusError;

    protected abstract OperationResult canReplay(CanReplyInfo canReplyInfo) throws BusError, InterruptedException, IOException;

    protected abstract CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId);

    protected abstract CyclicTask stopCanMessage(Integer deviceChannel, String messageId);

    /**
     * 前端获取后端保存的DeviceOperationParameter数据
     *
     * @param DeviceAliasName
     * @return
     */
    public Map<String, CanConfigParameter> getDeviceOperationParameterByServer(String DeviceAliasName) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        return deviceConfig.getConfigParameters();
    }
}
