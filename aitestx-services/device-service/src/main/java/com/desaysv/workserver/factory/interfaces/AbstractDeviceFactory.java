package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 18:39
 * @description : 抽象设备工厂接口
 * @modified By :
 * @since : 2022-3-16
 */
public interface AbstractDeviceFactory extends AbstractDeviceCreator {

    Device createAndroidDevice(DeviceRegisterForm deviceRegisterForm);

    Device createQnxDevice(DeviceRegisterForm deviceRegisterForm);

    Device createPowerDevice(DeviceRegisterForm deviceRegisterForm);

    Device createUsbSwtichDevice(DeviceRegisterForm deviceRegisterForm);

    Device createCameraDevice(DeviceRegisterForm deviceRegisterForm);

    Device createSerialDevice(DeviceRegisterForm deviceRegisterForm);

    Device createRobotDevice(DeviceRegisterForm deviceRegisterForm);

    Device createDaqDevice(DeviceRegisterForm deviceRegisterForm);

    Device createCanDevice(DeviceRegisterForm deviceRegisterForm);

    Device createEthernetDevice(DeviceRegisterForm deviceRegisterForm);

    Device createTestBoxDevice(DeviceRegisterForm deviceRegisterForm);

    Device createAutoClickerDevice(DeviceRegisterForm deviceRegisterForm);

    Device createPlugDevice(DeviceRegisterForm deviceRegisterForm);

    Device createElectricRelayDevice(DeviceRegisterForm deviceRegisterForm);

    Device createResistanceDevice(DeviceRegisterForm deviceRegisterForm);

    Device createTcpServerDevice(DeviceRegisterForm deviceRegisterForm);

    Device createSoundCardDevice(DeviceRegisterForm deviceRegisterForm);

    Device createSpeakerDevice(DeviceRegisterForm deviceRegisterForm);

    Device createOscilloscopeDevice(DeviceRegisterForm deviceRegisterForm);

    Device createSignalGeneratorDevice(DeviceRegisterForm deviceRegisterForm);

    Device createElectronicLoadDevice(DeviceRegisterForm deviceRegisterForm);


}
