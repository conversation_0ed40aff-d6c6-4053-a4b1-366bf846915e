package com.desaysv.workserver.devices.bus.utils;

import com.desaysv.workserver.devices.bus.model.CANMessage;

import java.lang.reflect.Method;

public class ModelConvertUtil {
    /**
     * 获取B1~BX的值
     *
     * @param canMessage
     * @param index
     * @return
     */
    public static String getBFieldValueForCanMessage(CANMessage canMessage, int index) {
        try {
            String methodName = "getB" + index;
            Method method = canMessage.getClass().getMethod(methodName);
            return (String) method.invoke(canMessage);
        } catch (Exception e) {
            e.printStackTrace();
            return "00";
        }
    }


}
