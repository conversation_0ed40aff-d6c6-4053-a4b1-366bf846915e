package com.desaysv.workserver.devices.robot.type.mg400.entity;

import lombok.Data;

@Data
public class RobotReply {

    private String message;

    private boolean ok;
    private Long replyDuration;

    private Long motionDuration;

    public boolean isFailed() {
        return !ok;
    }
    public static RobotReply ok() {
        return ok("");
    }

    public static RobotReply ok(String message) {
        RobotReply robotReply = new RobotReply();
        robotReply.setOk(true);
        robotReply.setMessage(message);
        return robotReply;
    }

    public static RobotReply fail() {
        return fail("");
    }
    public static RobotReply fail(String message) {
        RobotReply robotReply = new RobotReply();
        robotReply.setOk(false);
        robotReply.setMessage(message);
        return robotReply;
    }
}
