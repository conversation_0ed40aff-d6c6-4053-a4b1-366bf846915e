package com.desaysv.workserver.devices.device_udp;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import static com.desaysv.workserver.utils.ByteUtils.decimalToLittleEndianBytes;

@Slf4j
public class XcpCommander {
    private static final byte SET_ADDRESS_CODE = (byte) 0xF6;
    private static final byte READ_CODE = (byte) 0xF5;
    private static final byte WRITE_CODE = (byte) 0xF0;
    private static final byte SUCCESS_CODE = (byte) 0xFF;
    private static final int COMMAND_LENGTH = 8;

    public XcpCommander() {
    }

    // 公共方法：发送命令并验证响应
    public byte[] sendCommand(byte[] command) {
        byte[] response = null;
        try {
            response = CommandUdpClient.getInstance().sendAndReceiveData(command);
        } catch (UdpError e) {
            log.error("sendCommand error: {}", e.getMessage());
        }
        return response;
    }

    // 公共方法：构建标准8字节命令包
    public byte[] buildXcpCommandPacket(byte[] baseCommand) {
        byte[] packet = new byte[COMMAND_LENGTH];
        System.arraycopy(baseCommand, 0, packet, 0, Math.min(baseCommand.length, COMMAND_LENGTH));
        return packet;
    }

    public byte[] wrapCommand(byte[] command) {
        int length = command.length;
        byte[] wrapped = new byte[4 + length];  // 预分配最终数组
        // 数据长度
        wrapped[0] = (byte) (length & 0xFF);
        wrapped[1] = (byte) ((length >> 8) & 0xFF);

        // 写入两个保留字节
        wrapped[2] = 0x00;
        wrapped[3] = 0x00;

        // 拷贝原始数据
        System.arraycopy(command, 0, wrapped, 4, length);
        return wrapped;
    }

    // 公共方法：验证响应是否成功
    public boolean isSuccessResponse(byte[] response) {
        return response != null && response.length > 4 && response[4] == SUCCESS_CODE;
    }

    // 构建地址数据包
    public byte[] buildAddressPacket(String address) {
        byte[] addressBytes = hexToLittleEndianBytes(address);
        return ByteBuffer.allocate(1 + 3 + 4)
                .put(SET_ADDRESS_CODE)
                .put(new byte[3])
                .put(addressBytes)
                .array();
    }

    // 构建写入数据包
    public byte[] buildWritePacket(int size, byte[] command) {
        int capacity = size <= 6 ? 8 : 2 + size;
        return ByteBuffer.allocate(capacity)
                .put(WRITE_CODE)
                .put((byte) size)
                .put(command)
                .array();
    }




    // 构建读取数据包
    public byte[] buildReadPacket(int size) {
        int capacity = size <= 6 ? 8 : size + 2;
        return ByteBuffer.allocate(capacity)
                .put(READ_CODE)
                .put((byte) size)
                .array();
    }

    /**
     * 将32位十六进制地址字符串转换为小端序字节数组
     * @param hexAddress 格式如 "0x4000b49c"
     * @return 小端序字节数组
     * @throws IllegalArgumentException 输入格式错误时抛出
     */
    public static byte[] hexToLittleEndianBytes(String hexAddress) {
        // 移除前缀并验证格式
        String hex = hexAddress.replaceFirst("^0x", "").replaceFirst("^0X", "");

        // 验证有效性
        if (hex.length() != 8) {
            throw new IllegalArgumentException("必须是32位地址，实际长度: " + hex.length() * 4 + "位");
        }
        if (!hex.matches("[0-9a-fA-F]+")) {
            throw new IllegalArgumentException("包含非法字符: " + hexAddress);
        }

        try {
            // 转换为32位数值
            long address = Long.parseLong(hex, 16);

            // 生成小端字节序
            return ByteBuffer.allocate(4)
                    .order(ByteOrder.LITTLE_ENDIAN)
                    .putInt((int) (address & 0xFFFFFFFFL))  // 确保取低32位
                    .array();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("转换失败: " + hexAddress, e);
        }
    }


    public byte[] parseResponse(byte[] responseBytes) {
        // 基础校验
        if (responseBytes == null || responseBytes.length < 5) {
            throw new IllegalArgumentException("无效响应数据");
        }
        // 解析有效数据长度（小端序）
        int dataLength = Byte.toUnsignedInt(responseBytes[0])
                | (Byte.toUnsignedInt(responseBytes[1]) << 8);

        // 校验数据完整性（前4字节头 + 数据长度）----数据长度第一位是状态位，如果为0xFF则表示成功
        int expectedLength = 4 + dataLength;
        if (responseBytes.length < expectedLength) {
            throw new IllegalArgumentException("数据长度不足，预期长度: " + expectedLength);
        }
        // 检查状态位（第5字节）
        int statusIndex = 4;
        if (responseBytes[statusIndex] != (byte) 0xFF) {
            System.out.println("错误响应，状态码: 0x"
                    + String.format("%02X", responseBytes[statusIndex]));
            return null;
        }
        // 有效数据长度 = xcp数据反馈-1个byte的状态位
        int validDataLength = dataLength-1;
        byte[] validData = new byte[validDataLength];
        System.arraycopy(responseBytes, statusIndex + 1, validData, 0, validDataLength);
        //validData处理成无符号的
        for (int i = 0; i < validData.length; i++) {
            validData[i] = (byte) (validData[i] & 0xFF);
        }
        log.info("有效数据长度: {}, 实际数据:{} " , validDataLength, bytesToHex(validData));
        return validData;
    }


    /**
     * 字节数组转十六进制字符串 (调试用)
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    public static String bytesToHexString(byte[] bytes) {
        StringBuilder hex = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            int v = b & 0xFF;
            hex.append(String.format("%02x", v));
        }
        return hex.toString();
    }


    /**
     * 将小端序的 byte 数组转换为十进制整数
     * @param data 小端序字节数组（如 {(byte) 0xAC, (byte) 0x2E}）
     * @return 十进制整数（如 11948）
     */
    public static int convertLittleEndianToInt(byte[] data) {
        // 将每个字节转为无符号值
        int[] unsignedBytes = new int[data.length];
        for (int i = 0; i < data.length; i++) {
            unsignedBytes[i] = data[i] & 0xFF; // 关键的无符号转换[6](@ref)
        }

        // 小端序转大端序：反转数组
        int result = 0;
        for (int i = 0; i < unsignedBytes.length; i++) {
            result |= unsignedBytes[i] << (i * 8); // 低位在前，高位在后[7](@ref)
        }
        return result;
    }


    public static void main(String[] args) {
        XcpCommander xcpCommander = new XcpCommander();
//        byte[] bytes = xcpCommander.buildAddressPacket("0x4000b49c");
//        System.out.println(bytesToHex(bytes));
//
//        byte[] testData = {
//                (byte) 0x02, (byte) 0x00, // 长度字段（小端序，0x0001）
//                (byte) 0x00, (byte) 0x04, // 头信息
//                (byte) 0xFF,              // 状态位
//                (byte) 0x01               // 有效数据
//        };
//
//        byte[] bytes1 = xcpCommander.parseResponse(testData);
//        System.out.println(bytesToHex(bytes1));
//
//        byte[] bytes2 = xcpCommander.buildReadPacket(8);
//        System.out.println(bytesToHex(bytes2));
//        byte[] bytes3 = xcpCommander.buildWritePacket(8, new byte[]{0x06, 0x07});
//        System.out.println(bytesToHex(bytes3));
//
//        byte[] data1 = {
//                (byte) 0xac, (byte) 0x2e
//        };
//        byte[] data = {
//                -84, 46
//        };
//        System.out.println("data33==" + convertLittleEndianToInt(data1));
//        System.out.println("data33==" + convertLittleEndianToInt(data));
        byte[] command = decimalToLittleEndianBytes(1000, 2);
        byte[] writeCommand = xcpCommander.buildWritePacket(4, command);
        byte[] fullCommand = xcpCommander.wrapCommand(writeCommand);
        log.info("fullCommand:{}", Hex.encodeHexString(fullCommand));
    }

}