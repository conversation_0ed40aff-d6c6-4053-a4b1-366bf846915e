package com.desaysv.workserver.stream.rtsp;

import com.desaysv.workserver.filemanager.AppPathManager;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;

/**
 * RTSP服务线程
 */
@Slf4j
@Component
@Lazy
public class RtspServer extends Thread {
    //    private final static String rtspServerPath = "D:\\rtsp-simple-server";
    private final static String rtspServerExecutable = "rtsp-simple-server.exe";
    private final static String rtspServerTestCommand = "ffmpeg -y -frames 1 snapshot.png -rtsp_transport tcp -i ";
    private boolean start;
    private boolean exit = false;

    public boolean isStart() {
        return start;
    }

    public RtspServer() {

    }

    public void close() {
        exit = true;
    }

    @Override
    public void run() {
        runRtspServer();
    }


    private void runRtspServer() {
        try {
            if (CommandUtils.findProcess(AppPathManager.rtspServerExecutable)) {
                CommandUtils.killProcess(AppPathManager.rtspServerExecutable);
            }
            start = true;
            String line;
            BufferedReader reader = CommandUtils.getProcessBufferReader(
                    AppPathManager.rtspServerExecutable,
                    null,
                    AppPathManager.rtspServerPath);
            while (!exit && (line = reader.readLine()) != null) {
                log.info(line);
            }
            reader.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        start = false;
    }

//    public static void main(String[] args) {
//        RtspServer rtspServer = new RtspServer();
//        rtspServer.runRtspServer();
//    }

}
