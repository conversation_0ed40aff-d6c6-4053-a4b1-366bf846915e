package com.desaysv.workserver.monitor;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.ISoundFetchDevice;
import com.desaysv.workserver.entity.BooleanComparator;
import com.desaysv.workserver.entity.MonitorDataPackage;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.utils.StrUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 测量比较器（音量等）
 */

public interface SoundMeasurementComparator extends ISoundFetchDevice {

    Logger log = LoggerFactory.getLogger(SoundMeasurementComparator.class.getSimpleName());

    default BooleanComparator compare(String condition, float value, String textPattern) {
        BooleanComparator comparator = new BooleanComparator();

        if (condition.contains("&")) {
            String expr1 = condition.split("&")[0];
            String expr2 = condition.split("&")[1];
            boolean r1 = StrUtils.judgeExpressResult(value + expr1);
            boolean r2 = StrUtils.judgeExpressResult(value + expr2);
            comparator.setPass(r1 && r2);
        } else {
            comparator.setPass(StrUtils.judgeExpressResult(value + condition));
        }

        if (!comparator.isPass()) {
            comparator.setMessage(String.format(textPattern + ", 不符合条件%s", value, condition));
        } else {
            comparator.setMessage(String.format(textPattern + ", 符合条件%s", value, condition));
        }
        return comparator;
    }

    default BooleanComparator compareVolume(String condition, float volume) {
        BooleanComparator comparator;
        try {
            comparator = compare(condition, volume, "当前音量%f dB");
        } catch (Exception e) {
            comparator = new BooleanComparator();
            comparator.setPass(false);
            comparator.setMessage(e.getMessage());
        }
        return comparator;
    }


    default OperationResult measurementMonitor(Integer deviceChannel, MeasureIndicator measureIndicator, MonitorDataPackage monitorDataPackage) {
        long duration = monitorDataPackage.getDuration() * 1000L;
        int interval = monitorDataPackage.getInterval() == 0 ? 1 : monitorDataPackage.getInterval();
        long startTime = System.currentTimeMillis();
        BooleanComparator comparator = new BooleanComparator();
        comparator.setPass(false);
        long deltaTime = 0;
        OperationResult operationResult = new OperationResult();
        if (duration != 0) {
            startRecording(deviceChannel);//开始录音
        }
        while (deltaTime <= duration) {
            if (measureIndicator.equals(MeasureIndicator.VOLUME)) {
                comparator = compareVolume(deviceChannel, monitorDataPackage);
            } else {
                comparator.setMessage("测量类型错误");
                break;
            }
            if (comparator.isPass()) {
                break;
            }
            if (duration == 0) {
                break;
            }
            log.info("测量比较器:{}", comparator);
            try {
                Thread.sleep(interval * 1000L);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                break;
            }
            deltaTime = System.currentTimeMillis() - startTime;
        }
        if (duration != 0) {
            stopRecording();
        }
        if (!comparator.isPass()) {
            comparator.setMessage(comparator.getMessage() + "\r" + (duration == 0 ? "" : saveRecordingToFile()));
        }
        operationResult.setOk(comparator.isPass());
        operationResult.setMessage(comparator.getMessage());
        operationResult.setData(comparator);
        return operationResult;
    }

    default BooleanComparator compareVolume(Integer deviceChannel, MonitorDataPackage monitorDataPackage) {
        try {
            float volume = fetchVolume(deviceChannel, monitorDataPackage.getCurrentDirection());
            log.info("当前音量:{}dB", volume);
            String condition = monitorDataPackage.getCondition();
            BooleanComparator comparator = compareVolume(condition, volume);
            log.info("音量比较{}的结果:{}", condition, comparator);
            return comparator;
        } catch (DeviceReadException e) {
            BooleanComparator booleanComparator = new BooleanComparator();
            booleanComparator.setPass(false);
            booleanComparator.setMessage(e.getMessage());
            return booleanComparator;
        }
    }

}
