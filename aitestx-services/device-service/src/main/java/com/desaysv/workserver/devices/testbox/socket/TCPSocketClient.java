package com.desaysv.workserver.devices.testbox.socket;

import lombok.extern.slf4j.Slf4j;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.hexStringToHexBytes;
import static com.desaysv.workserver.utils.ByteUtils.byteArrayToHexString;

@Slf4j
public class TCPSocketClient {
    private Socket socket;
    private OutputStream outputStream;
    private InputStream inputStream;
    public boolean socketConnect(String ipAddress, int port, int connectionTimeout) {
        try {
            // 创建一个未连接的 Socket
            socket = new Socket();
            // 设置连接超时时间为500毫秒
            socket.connect(new InetSocketAddress(ipAddress, port), connectionTimeout);
//            socket.setSoTimeout(1000);   //此方法设置的是读取操作（如inputStream.read()）的阻塞超时时间。若1秒内未收到数据，会抛出SocketTimeoutException。
            outputStream = socket.getOutputStream();
            inputStream = socket.getInputStream();
        } catch (Exception e) {
            log.error("Connection failed:", e);
            return false;
        }
        return true;
    }

//    public String sendData(String data) {
//        try {
//            if (outputStream != null) {
//                byte[] bytes = hexStringToByteArray(data);
//                outputStream.write(bytes);
//                outputStream.flush();
//            }
//            // 获取输入流以读取数据
//            try  {
//                // 创建一个缓冲区来存储接收到的数据
//                byte[] buffer = new byte[1024];
//                int bytesRead;
//                // 尝试读取响应
//                if ((bytesRead = inputStream.read(buffer)) != -1) {
//                    // 将接收到的数据截断到实际读取的长度，并转换为16进制字符串
//                    byte[] receivedBytes = Arrays.copyOf(buffer, bytesRead);
//                    return byteArrayToHexString(receivedBytes);
//                }
//            } catch (SocketTimeoutException e) {
//                // 如果超时时间1秒，未读到数据，打印处理异常，返回null
//                log.error("Read timed out:{}", e);
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("Failed to send data:{}", e);
//            return null;
//        }
//        return null;
//    }

    public String sendData(String data) {
        try {
            // 清空输入缓冲区残留数据
            while (inputStream.available() > 0) {
                inputStream.skip(inputStream.available());
            }
            // 发送数据（原逻辑）
            if (outputStream != null) {
                byte[] bytes = hexStringToHexBytes(data);
                log.info("发送TCP数据：{}", byteArrayToHexString(bytes));
                outputStream.write(bytes);
                outputStream.flush();
            }
            // 读取响应（增加超时循环）
            ByteArrayOutputStream byteBuffer = new ByteArrayOutputStream();
            byte[] tmp = new byte[1024];
            long startTime = System.currentTimeMillis();
            log.info("开始读取tcp响应....");
            // 总超时30ms，不断去读tcp的数据，实际测试过，30ms可以读到3次数据了
            while (System.currentTimeMillis() - startTime < 50) {
                int available = inputStream.available();
                if (available > 0) {
                    log.info("读取到tcp数据：{}" , byteArrayToHexString(byteBuffer.toByteArray()));
                    int bytesRead = inputStream.read(tmp, 0, Math.min(available, tmp.length));
                    byteBuffer.write(tmp, 0, bytesRead);
                    break;
                } else {
                    Thread.sleep(5); // 避免CPU空转
                }
            }
            log.info("读取tcp响应完成....");
            return byteArrayToHexString(byteBuffer.toByteArray());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return null;
        }
    }


    public void socketDisConnect() {
        try {
            try {
                if (socket != null) {
                    socket.shutdownInput();
                    socket.shutdownOutput();
                    socket.close();
                }
            } catch (IOException e) {
                log.error("Failed to close socket: {} " , e.getMessage());
            }
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("Failed to close input stream: {} " , e.getMessage());
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Failed to close output stream: {} " , e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Failed to disconnect: {} " , e.getMessage());
        }
    }

}
