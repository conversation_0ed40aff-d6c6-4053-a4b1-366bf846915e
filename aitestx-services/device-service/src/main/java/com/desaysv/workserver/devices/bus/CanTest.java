package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.zlg.ZlgCan;
import com.desaysv.workserver.devices.bus.zlg.ZlgCanFd200U;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class CanTest {
    public static void main(String[] args) {
        ZlgCan bus = new ZlgCanFd200U(new DeviceOperationParameter());
        bus.setDevicePort(0);
        bus.setChannel(0);
        byte[] avmData = new byte[]{0x00, 0x08, 0x00, 0x00};
        byte[] exitAvmData = new byte[]{0x00, 0x04, 0x00, 0x00};
        byte[] LHMRadarData = new byte[]{0x3B, 0x3B, 0x01, 0x3B, 0x1B, 0x1B, 0x1B, 0x3B};
        byte[] exitLHMRadarData = new byte[]{0x3B, 0x3B, 0x3B, 0x3B, 0x1B, 0x1B, 0x1B, 0x3B};
        CanMessage avmMessage = new CanMessage(0x301, avmData);
        CanMessage exitAvmMessage = new CanMessage(0x301, exitAvmData);
        CanMessage radarMessage = new CanMessage(0x440, LHMRadarData);
        CanMessage exitRadarMessage = new CanMessage(0x440, exitLHMRadarData);
        avmMessage.setChannel(1);
        exitAvmMessage.setChannel(1);
        radarMessage.setChannel(1);
        exitRadarMessage.setChannel(1);
        try {
            bus.open();
            CanConfigParameter canConfigParameter = new CanConfigParameter();
            canConfigParameter.setChannel(1);
            bus.openChannel(canConfigParameter);
            bus.sendPeriodic(radarMessage, 0.02f, null);
            bus.sendPeriodic(avmMessage, 0.02f, null);
            log.info("进入倒车：{}", bus.getPeriodTasks());
            TimeUnit.HOURS.sleep(1);
            log.info("当前任务列表：{}", bus.getPeriodTasks());
            bus.sendPeriodic(exitRadarMessage, 0.02f, 2f);
            bus.sendPeriodic(exitAvmMessage, 0.02f, 6f);
            log.info("退出倒车：{}", bus.getPeriodTasks());
            Thread.sleep(10000);
            bus.close();
            log.info("结束：{}", bus.getPeriodTasks());
        } catch (DeviceOpenException | InterruptedException | DeviceCloseException e) {
            log.error(e.getMessage(), e);
        }
    }

}
