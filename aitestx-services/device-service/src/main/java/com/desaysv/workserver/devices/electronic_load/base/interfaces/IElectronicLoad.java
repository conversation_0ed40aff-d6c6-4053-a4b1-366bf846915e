package com.desaysv.workserver.devices.electronic_load.base.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.*;

public interface IElectronicLoad {
    Logger log = LogManager.getLogger(IElectronicLoad.class.getSimpleName());

    boolean setInputState(String statusCommand);

    boolean setHighLevelCC(float current);

    boolean setLowLevelCC(float current);

    boolean setHighLevelCV(float voltage);

    boolean setLoweLevelCV(float voltage);

    boolean setLoweLevelCR(float resistance);

    boolean setLoweLevelCP(float power);

    float fetchLoadCurrent() throws DeviceReadException;

    float fetchLoadVoltage() throws DeviceReadException;

    float fetchLoadResistance() throws DeviceReadException;

    float fetchLoadPower() throws DeviceReadException;

    float fetchLoadTemperature() throws DeviceReadException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).SET_INPUT_STATUS_ON_OFF"})
    default ActualExpectedResult SetInputStateInfo(Integer deviceChannel, String statusCommand) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean isOpen = statusCommand.equalsIgnoreCase("ON");
        boolean pass = setInputState(isOpen ? "ON" : "OFF");
        actualExpectedResult.put("SetInputStateInfo", pass, statusCommand);
        log.info("设置恒流:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).SET_CC"})
    default ActualExpectedResult SetCC(Integer deviceChannel, String currentWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float current = parseCurrent(currentWithUnit);
        boolean pass = setHighLevelCC(current);
        actualExpectedResult.put("SetCC", pass, currentWithUnit);
        log.info("设置恒流:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).SET_CV"})
    default ActualExpectedResult SetCV(Integer deviceChannel, String voltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float voltage = getVoltage(voltageWithUnit);
        boolean pass = setHighLevelCV(voltage);
        actualExpectedResult.put("SetCV", pass, voltageWithUnit);
        log.info("设置恒压:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).GET_CC"})
    default ActualExpectedResult GetCC(Integer deviceChannel, String currentWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float getCurrent = -1;
        String unit = "";
        try {
            unit = getUnit(currentWithUnit, "A");
            getCurrent = valueUnitConvert(fetchLoadCurrent(), "A", unit);
            float current = parseCurrent(currentWithUnit, unit);
            pass = getCurrent == current;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCC", pass, String.format("%s%s", getCurrent, unit));
        log.info("获取恒流:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).GET_CV"})
    default ActualExpectedResult GetCV(Integer deviceChannel, String voltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float getVoltage = -1;
        String unit = "";
        try {
            unit = getUnit(voltageWithUnit, "V");
            getVoltage = valueUnitConvert(fetchLoadVoltage(), "V", unit);
            float voltage = getVoltage(voltageWithUnit);
            pass = getVoltage == voltage;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCC", pass, String.format("%s%s", getVoltage, unit));
        log.info("获取恒压:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).GET_CC_RANGE"})
    default ActualExpectedResult getCCRange(Integer deviceChannel, String currentBaseValueWithUnit, String rangeWithUnit) {

        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float current = -1;
        String unit = "";
        float getCurrent = -1;
        try {
            unit = getUnit(currentBaseValueWithUnit, "A");
            current = fetchLoadCurrent();
            getCurrent = valueUnitConvert(current, "A", unit);
            float currentBaseValue = parseCurrent(currentBaseValueWithUnit, unit);
            float range = parseCurrent(rangeWithUnit, unit);
            pass = getCurrent >= currentBaseValue - range && getCurrent <= currentBaseValue + range;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("getCCRange", pass, String.format("%s%s", getCurrent, unit));
        log.info("获取恒流:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ElectronicLoadRegexRule).GET_CV_RANGE"})
    default ActualExpectedResult GetCVRange(Integer deviceChannel, String voltageBaseValueWithUnit, String rangeWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float getVoltage = -1;
        String unit = "";
        try {
            unit = getUnit(voltageBaseValueWithUnit, "V");
            getVoltage = valueUnitConvert(fetchLoadVoltage(), "V", unit);
            float voltageBaseValue = getVoltage(voltageBaseValueWithUnit, unit);
            float range = getVoltage(rangeWithUnit, unit);
            pass = getVoltage >= voltageBaseValue - range && getVoltage <= voltageBaseValue + range;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCVRange", pass, String.format("%s%s",getVoltage, unit));
        log.info("获取恒压:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }
}
