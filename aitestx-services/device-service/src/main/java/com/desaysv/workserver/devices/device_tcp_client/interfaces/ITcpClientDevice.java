package com.desaysv.workserver.devices.device_tcp_client.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.device_udp.DIDInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.python.bouncycastle.util.encoders.Hex;
import java.util.Map;

public interface ITcpClientDevice {
    Logger log = LogManager.getLogger(ITcpClientDevice.class.getSimpleName());

    Map<String, DIDInfo> getDIDInfo(String items);
    boolean reconnectTcpClientDevice(int tryTimes);
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_DID_INFO"})
    default ActualExpectedResult compareDIDCommand(Integer deviceChannel, String items, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        Map<String, DIDInfo> didMap = getDIDInfo(items);
        DIDInfo didInfo = didMap.get(items);
        boolean pass = command.equalsIgnoreCase(didInfo.getParseData());
        actualExpectedResult.put("compareDIDCommand", pass, String.format("TCP指令返回：%s, 转换成字符：%s", Hex.toHexString(didInfo.getReceiveDataBytes()), didInfo.getParseData().replaceAll("_", "-")));
        log.info("发送指令到TCP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SEND_RECONNECT_TIMES_COMMAND"})
    default ActualExpectedResult sendReconnectCommand(Integer deviceChannel, int tryTimes) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = reconnectTcpClientDevice(tryTimes);
        actualExpectedResult.put("sendPingCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }
}
