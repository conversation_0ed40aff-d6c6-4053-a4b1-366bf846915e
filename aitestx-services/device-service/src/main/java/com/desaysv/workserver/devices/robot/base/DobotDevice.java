package com.desaysv.workserver.devices.robot.base;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.devices.robot.RobotDevice;
import com.desaysv.workserver.devices.robot.vision.RobotSafePoint;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 16:59
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
public abstract class DobotDevice extends RobotDevice {
    public static String smartTouchKey = "smartTouch";

    @Getter
    @JSONField(serialize = false)
    private final Map<String, RobotTouchEvent> touchEventMap = new HashMap<>();

    public boolean isTouchEventKeyValid(String key) {
        return touchEventMap.containsKey(key);
    }

    public void addTouchEvent(String key, RobotTouchEvent touchEvent) {
        touchEventMap.putIfAbsent(key, touchEvent);
    }

    public void removeTouchEvent(String key) {
        touchEventMap.remove(key);
    }

    public void clearTouchEvents() {
        touchEventMap.clear();
    }

    @Setter
    @Getter
    private RobotSafePoint robotSafePoint;

    @Getter
    private final Integer usingCameraPort = 0;

    public DobotDevice() {
        super();
    }

    public DobotDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public boolean close() throws DeviceCloseException {
        clearTouchEvents();
        return true;
    }


}
