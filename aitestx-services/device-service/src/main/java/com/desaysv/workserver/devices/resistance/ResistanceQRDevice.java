package com.desaysv.workserver.devices.resistance;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.interfaces.IResistorBoard;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.fazecast.jSerialComm.SerialPort;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ResistanceQRDevice extends PortDevice implements IResistorBoard {

    private static SerialPort serialPort;

    public ResistanceQRDevice() {

    }

    public ResistanceQRDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        serialPort = SerialPort.getCommPort(getDevicePortName());
        if (!serialPort.isOpen()) {
            serialPort.openPort(1000);
        } else {
            return true;
        }
        serialPort.setFlowControl(SerialPort.FLOW_CONTROL_DISABLED);
        serialPort.setComPortParameters(115200,
                8,
                SerialPort.ONE_STOP_BIT,
                SerialPort.NO_PARITY);
        serialPort.setComPortTimeouts(SerialPort.TIMEOUT_READ_BLOCKING | SerialPort.TIMEOUT_WRITE_BLOCKING, 1000, 1000);
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (serialPort != null && serialPort.isOpen()) {
            serialPort.closePort();
        }
        return true;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_RESISTANCE;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Resistance.QR10X;
    }


    private OperationResult sendCommand(String command) {
        OperationResult operationResult = new OperationResult();
        if (serialPort != null && serialPort.isOpen()) {
            try {
                serialPort.writeBytes(command.getBytes(), command.length());
                Thread.sleep(50);
                byte[] response = new byte[1024];
                int numRead = serialPort.readBytes(response, response.length);
                String responseString = new String(response, 0, numRead);
                operationResult.setResult(responseString);
                return operationResult.ok();
            } catch (Exception e) {
                log.error("发送命令时出错：{}", command, e);
                operationResult.setMessage("发送命令时出错：" + command);
                return operationResult.fail();
            }
        } else {
            log.error("串口未打开");
            operationResult.setMessage("串口未打开");
            return operationResult.fail();
        }
    }

    private OperationResult changeResistance(int resistance) {
        String command = QR10XCommands.SET_SP + resistance + "\r\n";
        return sendCommand(command);
    }

    private OperationResult readResistance() {
        String command = QR10XCommands.QUERY_SET_SP + "\r\n";
        return sendCommand(command);
    }

    private OperationResult setResistanceStepIncrement(int resistance) {
        String command = QR10XCommands.SET_SP_INCREMENT + resistance + "\r\n";
        return sendCommand(command);
    }

    private OperationResult setResistanceStepDecrement(int resistance) {
        String command = QR10XCommands.SET_SP_DECREMENT + resistance + "\r\n";
        return sendCommand(command);
    }

    private OperationResult setLimit(int resistance) {
        String command = QR10XCommands.SET_MIN_OUTPUT_LIMIT + resistance + "\r\n";
        return sendCommand(command);
    }

    private OperationResult readLimit() {
        String command = QR10XCommands.QUERY_MIN_OUTPUT_LIMIT + "\r\n";
        return sendCommand(command);
    }


    @Override
    public boolean writeResistanceBoardCard(Integer deviceChannel, int resistance) {
        log.info("QR10X设置阻值{}Ω", resistance);
        return false;
    }
}
