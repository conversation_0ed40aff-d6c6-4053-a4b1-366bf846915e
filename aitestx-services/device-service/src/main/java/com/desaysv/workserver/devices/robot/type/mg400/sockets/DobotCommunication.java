package com.desaysv.workserver.devices.robot.type.mg400.sockets;

import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 18:23
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
@Data
public abstract class DobotCommunication {

    private DobotEventHandler dobotEventHandler;

    private Socket socketClient;

    private String ip = "";

    public final static String SEND_ERROR = ":send error";

    public abstract int getDefaultPort();

    public abstract int getSoTimeout();

    public boolean isSocketClosed() {
        return socketClient == null || socketClient.isClosed();
    }

    protected boolean connectSocket() {
        return connectSocket(false);
    }

    protected boolean reconnectSocket() {
        return connectSocket(true);
    }

    public void closeSocket() {
        if (socketClient != null && !socketClient.isClosed()) {
            try {
                socketClient.close();
            } catch (IOException e) {
                log.error("IOException", e);
            }
        }
    }

    protected boolean connectSocket(boolean reconnect) {
        try {
            if (reconnect) {
                log.info("重新连接Socket {}", getDefaultPort());
                closeSocket();
                if (dobotEventHandler != null) {
                    dobotEventHandler.reconnect();
                }
            }
            socketClient = new Socket(getIp(), getDefaultPort());
            int timeout = getSoTimeout();
            if (timeout > 0) {
                //超时未读取到数据则报错SocketTimeoutException
                socketClient.setSoTimeout(timeout);
            }
            return true;
        } catch (IOException e) {
            log.error("IOException", e);
            return false;
        }
    }

    public boolean isSocketConnected() {
        return socketClient != null && socketClient.isConnected();
    }

    protected RobotReply sendCmdToRobot(String cmd, int timeout) {
        return sendCmdToRobot(cmd, timeout, true);
    }

    protected RobotReply sendCmdToRobot(String cmd, int timeout, boolean enableLog) {
        if (cmd == null) {
            return RobotReply.fail();
        }
        RobotReply robotReply = new RobotReply();
        if (isSocketClosed()) {
            String message = String.format("Socket %d does not connected, cmd: %s", getDefaultPort(), cmd);
            log.warn(message);
            robotReply.setOk(false);
            robotReply.setMessage(message);
            return robotReply;
        }
        if (!sendData(cmd, enableLog)) {
            robotReply.setOk(false);
            robotReply.setMessage(cmd + SEND_ERROR);
            return robotReply;
        }

        return waitReply(timeout, enableLog);
    }

    public boolean connectVirtually() {
        socketClient = new Socket();
        return true;
    }

    public boolean connect(String ip) {
        this.ip = ip;
        if (isSocketConnected()) {
            return true;
        }
        return connectSocket();
    }

    public boolean disconnect() {
        if (!isSocketClosed()) {
            try {
                socketClient.shutdownOutput();
                socketClient.shutdownInput();
                socketClient.close();
                socketClient = null;
                log.info("{} closed", getClass().getSimpleName());
                return true;
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        } else {
            log.warn("当前机械臂ip未连接");
        }
        return false;
    }

    public boolean sendData(String str) {
        return sendData(str, true);
    }

    private boolean handleException(Exception e, String reSendData) {
        log.error("Handle exception:", e);
        try {
            log.info("Socket information:{}", CommandUtils.executeCommandToString("netstat -ano | findstr ***********"));
        } catch (IOException ex) {
            log.error("Netstat exception:", e);
        }
        if (reconnectSocket()) {
            //重连成功，继续发送
            if (reSendData != null) {
                try {
                    log.info("Re send to Dobot:{}:{}", reSendData, socketClient);
                    socketClient.getOutputStream().write((reSendData).getBytes());
                } catch (IOException ex) {
                    log.error("Retry write data->IOException:", e);
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public boolean sendData(String data, boolean enableLog) {
        if (isSocketClosed()) {
            return false;
        }
        try {
            if (enableLog) {
                log.info("Send to Dobot:{} --> {}", data, socketClient);
            }
            socketClient.getOutputStream().write((data).getBytes());
        } catch (IOException e) {
            return handleException(e, data);
        }
        return true;
    }

    public RobotReply waitReply(int timeout) {
        return waitReply(timeout, true);
    }

    public RobotReply waitReply(int timeout, boolean enableLog) {
        RobotReply robotReply = new RobotReply();
        String reply = "";
        long deltaMills = 0;
        try {
            if (enableLog) {
                deltaMills = System.currentTimeMillis();
                log.info("{} waitReply {}s", getDefaultPort(), timeout / 1000);
            }
            if (socketClient.getSoTimeout() != timeout) {
                socketClient.setSoTimeout(timeout);
            }
            byte[] buffer = new byte[1024];  //缓冲
            //每次读取的长度（正常情况下是1024，最后一次可能不是1024，如果传输结束，返回-1）
            int len = getSocketClient().getInputStream().read(buffer);
            if (len != -1) {
                reply = new String(buffer, 0, len, StandardCharsets.UTF_8);
            }
//            ErrorInfoHelper.getInstance().parseResult(reply);
            robotReply.setMessage(reply);
            robotReply.setOk(true);
            if (enableLog) {
                deltaMills = System.currentTimeMillis() - deltaMills;
                log.info("{} reply finish({}ms): {}", getDefaultPort(), deltaMills, reply);
                robotReply.setReplyDuration(deltaMills);
            }
        } catch (IOException e) {
            handleException(e, null);
//            if (enableLog) {
//                log.error("WaitReply->IOException:", e);
//                log.info("Socket Information:{}", socketClient);
//                try {
//                    log.info("Port Information:\n{}", CommandUtils.executeCommandToString("netstat -ano | findstr ***********"));
//                } catch (IOException ex) {
//                    ex.printStackTrace();
//                }
//            }
            robotReply.setOk(false);
            robotReply.setMessage(e.getMessage());
        }
        return robotReply;
    }
}
