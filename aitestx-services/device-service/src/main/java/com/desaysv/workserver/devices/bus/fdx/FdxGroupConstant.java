package com.desaysv.workserver.devices.bus.fdx;

public class FdxGroupConstant {

    public static final String SET_NODE_CHANNEL = "NodeChannel";
    public static final String SET_NAME_NODES = "NameNodes";
    public static final String SET_XCP_NODES = "xcpNode";
    // GetCanSignal
    public static final String GET_CAN_SIGNAL_ID = "GetCanSignal";
    public static final String GET_CAN_SIG_NAME = "ILcontrolGetSigName";
    public static final String CAN_MSG_NAME = "ILcontrolMsgName";
    public static final String GET_CAN_SIGNAL_COUNTER_ID = "GetCanSignalCounter";

    // SetCanSignal
    public static final String SET_CAN_SIGNAL_ID = "SetCanSignal";
    public static final String SET_CAN_SIG_NAME = "ILcontrolSigName";
    public static final String SET_CAN_SIGNAL_COUNTER_ID = "SetCanSignalCounter";

    // SetCanSingleMsgControl
    public static final String SET_CAN_SINGLE_MSG_CONTROL_ID = "SetCanSingleMsgControl";
    public static final String SET_CAN_SINGLE_MSG_CONTROL_COUNTER_ID = "SetCanSingleMsgControlCounter";

    // SetCanAllMsgControl
    public static final String SET_CAN_ALL_MSG_CONTROL_ID = "SetCanAllMsgControl";
    public static final String SET_CAN_ALL_MSG_CONTROL_COUNTER_ID = "SetCanAllMsgControlCounter";

    // SetCanMsgCycleTime
    public static final String SET_CAN_MSG_CYCLE_TIME_ID = "SetCanMsgCycleTime";
    public static final String SET_CAN_MSG_CYCLE_TIME_COUNTER_ID = "SetCanMsgCycleTimeCounter";

    //SetCanMsgDLC
    public static final String SET_CAN_MSG_DLC_ID = "SetCanMsgDLC";
    public static final String SET_CAN_MSG_DLC_COUNTER_ID = "SetCanMsgDLCCounter";

    // SetCanChecksumRolling
    public static final String SET_CAN_CHECKSUM_ROLLING_ID = "SetCanChecksumRolling";
    public static final String CHECKSUM_ROLLING_MSG_ID = "csRollingMsgID";
    public static final String SET_CAN_CHECKSUM_ROLLING_COUNTER_ID = "SetCanChecksumRollingCounter";

    // SetCanPTS
    public static final String SET_CAN_PTS_ID = "SetCanPTS";
    public static final String SET_CAN_PTS_DATE_TX = "PtsControlDateTx";
    public static final String SET_CAN_PTS_COUNTER_ID = "SetCanPTSCounter";

    // GetCanPTS
    public static final String GET_CAN_PTS_ID = "GetCanPTS";

    //Upgrade/PtsSwVersion
    public static final String PTS_SW_VERSION = "PtsSwVersion";
    public static final String SET_ADDRESS = "SetAddress";
    public static final String SEND_UDS_DATA = "SendUdsData";
    public static final String SET_UPGRADE = "SetUpgrade";
    public static final String SET_UPGRADE_COUNTER = "SetUpgradeCounter";

    //GetCanXCP
    public static final String GET_CAN_XCP_ID = "GetCanXCP";
    public static final String GET_CAN_XCP_NAME = "getCanXcpName";
    public static final String GET_CAN_XCP_COUNTER = "getCanXcpCounter";

    // SetCanXCP
    public static final String SET_CAN_XCP_ID = "SetCanXCP";
    public static final String SET_CAN_XCP_NAME = "xcpName";
    public static final String SET_CAN_XCP_COUNTER = "SetCanXCPCounter";

    // SetLinSignal
    public static final String SET_LIN_SIGNAL_ID = "SetLinSignal";
    public static final String LIN_MSG_NAME = "ILcontrolLinMsgName";
    public static final String SET_LIN_SIG_NAME = "ILcontrolLinSigName";
    public static final String SET_LIN_SIGNAL_COUNTER_ID = "SetLinSignalCounter";

    // GetLinSignal
    public static final String GET_LIN_SIGNAL_ID = "GetLinSignal";
    public static final String GET_LIN_SIG_NAME = "ILcontrolLinGetSigName";
    public static final String GET_LIN_SIGNAL_COUNTER_ID = "GetLinSignalCounter";

    //SetLinSingleMsgControl
    public static final String SET_LIN_SINGLE_MSG_CONTROL_ID = "SetLinSingleMsgControl";
    public static final String SET_LIN_SINGLE_MSG_CONTROL_COUNTER_ID = "SetLinSingleMsgControlCounter";
    public static final String SET_LIN_FrameID = "ILcontrolLinFrameID";

    // SetLinAllMsgControl
    public static final String SET_LIN_ALL_MSG_CONTROL_ID = "SetLinAllMsgControl";
    public static final String SET_LIN_ALL_MSG_CONTROL_COUNTER_ID = "SetLinAllMsgControlCounter";

    //ReturnValue
    public static final String RETURN_VALUE_ID = "ReturnValue";
    public static final String RETURN_VALUE = "returnValue";

    //getUDSRecUdsData
    public static final String GET_UDS_REC_UDS_DATA = "GetUDSRecUdsData";
    public static final String GET_UDS_REC_UDS_DATA_DESCRIBE = "GetUDSRecUdsDataDescribe";
    public static final String GET_UDS_REC_UDS_DATA_DESCRIBE22 = "GetUDSRecUdsDataDescribe22";

    // SetUDSLogName
    public static final String SET_START_UDS_TEST = "SetStartUDSTest";
    public static final String SET_UDS_LOG_NAME = "SetUDSLogName";
    //public static final String SET_UDS_LOGGING_STATUS = "SetUDSLoggingStatus";
    public static final String SET_UDS_START_LOGGING = "SetStartLogging";
    public static final String SET_UDS_STOP_LOGGING = "SetStopLogging";
    public static final String GET_UDS_FINISH = "UDSFinishFlag";
    public static final String GET_UDS_KEY = "SetUdsKey";

    public static final String GET_UDS_LOG_PATH = "SetUdsLogPath";


    //SetUdsKeepSession-----groupId：XX
    public static final String SET_UDS_KEEP_SESSION_ID =  "SetUdsKeepSession";

    public static final String SET_RESET_CHARGE = "SetResetCharge";
    public static final String SET_TEMPERATURE = "SetTemperature";
    public static final String SET_TEMPERATURE_COUNTER = "SetTemperatureCounter";

    public static final String SET_POWER_STATUS = "SetPowerStatus";
    public static final String SET_INIT = "SetINIT";

    // SetCanLogName
    public static final String SET_CAN_LOG_NAME = "SetCANLogName";
    public static final String SET_CAN_LOGGING_STATUS = "SetCANLoggingStatus";

    //SET_XCP_FUN_SWITCH---groupId：54
    public static final String SET_XCP_FUN_SWITCH = "SetXcpFunSwitch";
    //SET_XCP_VAR---groupId：55
    public static final String SET_XCP_VAR = "SetXcpVar";
    public static final String SET_XCP_VAR_NAME = "XcpVarName";
    public static final String SET_XCP_VAR_VALUE = "SetVarValue";
    //SET_XCP_BTN---groupId：56
    public static final String SET_XCP_BTN = "XcpVarSetBotn";
    //CHECK_XCP_VAR---groupId：57
    public static final String CHECK_XCP_VAR = "CheckXcpVar";
    public static final String GET_XCP_VAR_VALUE = "GetVarValue";
    //GET_XCP_BTN---groupId：58
    public static final String GET_XCP_BTN = "XcpVarGetBotn";
    //SetKeyPosition---groupId：59
    public static final String SET_KEY_POSITION = "SetKeyPosition";
    //SetKeyLock---groupId：60
    public static final String SET_KEY_LOCK = "SetKeyLock";
    //SetKeyUnlock---groupId：61
    public static final String SET_KEY_UNLOCK = "SetKeyUnlock";
    //SetKeyFindCar---groupId：62
    public static final String SET_KEY_FIND_CAR = "SetKeyFindCar";
    //SetKeyTallgate---groupId：63
    public static final String SET_KEY_TALLGATE = "SetKeyTallgate";
    //SetKeyLowVoltage---groupId：77
    public static final String SET_KEY_LOW_VOLTAGE = "SetKeyLowVoltage";
    //SetRDefogSts-----groupId：64
    public static final String SET_RDEFOGSTS = "SetRDefogSts";
    //CheckMsg-----groupId：65
    public static final String SET_CHECK_MSG = "CheckMsg";
    public static final String MSG_CHANNEL = "Msg_Channel";
    public static final String MSG_ID = "Msg_ID";
    public static final String MSG_CHECK_STATUS = "MsgCheckStatus";
    public static final String CHECK_TIME = "CheckTime";
    //StartCheckMsg-----groupId：66
    public static final String SET_START_CHECK_MSG = "StartCheckMsg";
    //StartCheckLINMsg----groupId：69
    public static final String SET_START_CHECK_LIN_MSG = "StartCheckLINMsg";
    //SetMirrorFoldSts-----groupId：67
    public static final String SET_MIRRORFOLD_STS = "SetMirrorFoldSts";
    //SetLampSwitch-----groupId：68
    public static final String SET_LAMP_SWITCH = "SetLampSwitch";

    //SetXcpFindTheKey-----groupId：69
    public static final String SET_XCP_FIND_THE_KEY = "SetXcpFindTheKey";
    public static final String FIND_KEY_TIME = "XcpFindKeyTime";
    public static final String FIND_THE_KEY = "XcpFindTheKey";
    //SetXcpFindNoKey-----groupId：70
    public static final String SET_XCP_FIND_NO_KEY = "SetXcpFindNoKey";
    public static final String FIND_NO_KEY = "XcpFindNoKey";
    //CheckTurnLamp------groupId：71
    public static final String CHECK_TURN_LAMP_ID = "CheckTurnLamp";
    public static final String LIGHT_TYPE = "LightType";
    public static final String WORK_TIME = "WorkTime";
    public static final String CYCLE_NUMBER = "CycleNumber";
    //StartCheckTurnLight ------groupId：72
    public static final String START_CHECK_TURN_LIGHT_ID = "startCheckTurnLight";
    public static final String START_CHECK_TURN_LIGHT = "start_CheckTurnLight";
    //CheckFourDoor------groupId：73
    public static final String CHECK_FOUR_DOOR_ID = "CheckFourDoor";
    public static final String CHECK_DOOR_LOCK_TYPE = "FourDoorLock_Type";
    //StartCheckFourDoorLock------groupId：74
    public static final String START_CHECK_FOUR_DOOR_LOCK_ID = "StartCheckFourDoorLock";
    public static final String START_CHECK_FOUR_DOOR = "FourDoorLock_check";
    //SendEventMsg-----groupId：75
    public static final String SEND_EVENT_MSG_ID = "SendEventMsg";
    public static final String MSG_TIME = "Msg_Time";
    public static final String MSG_COUNTER = "SendMsg_Counter";
    public static final String MSG_DATA = "Msg_Data";
    //CheckVoltage-----groupId：76
    public static final String CHECK_VOLTAGE_ID = "CheckVoltage";
    public static final String PIN_NUMBER = "PinNumber";
    public static final String PIN_ALIVE_TIME = "PinAliveTime";
    public static final String PIN_NO_ALIVE_TIME = "PinNoAliveTime";
    public static final String WORK_CYCLE_NUMBER = "WorkCycleNumber";
    //startCheckPin-----groupId：77
    public static final String START_CHECK_PIN_ID = "startCheckPin";
}

