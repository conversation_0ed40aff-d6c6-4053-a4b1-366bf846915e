package com.desaysv.workserver.devices;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.context.AlgorithmInvoker;
import com.desaysv.workserver.entity.TemplateImageConfig;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import org.bytedeco.javacv.Frame;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 表示支持捕获能力的接口
 */
public interface CaptureSupport {
    AlgorithmInvoker algorithmInvoker = SpringContextHolder.getBean("algorithmInvoker", AlgorithmInvoker.class);

    default boolean testSimilarity() {
        return false;
    }

    default VisionResult visionMatch() {
        AtomicReference<Frame> staticLiveFrame = new AtomicReference<>();
        AtomicReference<Frame> templateFrame = new AtomicReference<>();
        return new VisionResult();
    }

    Frame grabFrame();

    RectSize getSize();

    default VisionResult calculateSimilarity(TemplateImageConfig templateImageConfig) throws OperationFailNotification {
        AtomicReference<Frame> staticLiveFrame = new AtomicReference<>();
        AtomicReference<Frame> templateFrame = new AtomicReference<>();

        Frame grabbedInitialFrame = grabFrame();
        if (grabbedInitialFrame == null) {
            // 在实际应用中，这里可以添加日志记录
            // log.warn("In calculateSimilarity, grabFrame() returned null for template: {}", templateImageConfig.getTemplateName());
            throw new OperationFailNotification("Failed to grab initial live frame (grabFrame returned null).");
        }

        //拍摄当前画面
        try (Frame frame = grabbedInitialFrame) { // frame 现在保证非null
            staticLiveFrame.set(frame.clone());
        } catch (Exception e) { // 捕获通用异常以查看 clone() 是否在本地崩溃前抛出任何Java异常
            // 在实际应用中，这里可以添加日志记录
            // log.error("Exception during live frame cloning: " + e.getMessage(), e);
            // 对于其他异常 (包括可能的NPE或clone()本身的其他问题)
            throw new OperationFailNotification("An unexpected exception occurred during live frame cloning: " + e.getClass().getName() + " - " + e.getMessage(), e);
        }

        Frame currentLiveFrameSnapshot = staticLiveFrame.get();
        if (currentLiveFrameSnapshot == null) {
            // 如果 clone() 操作成功且没有抛出异常, clone() 不应该返回 null。
            // 所以这一步主要是为了防止 clone() 异常导致 staticLiveFrame 未被设置。
            throw new OperationFailNotification("Cloned live frame is null, which is unexpected after a successful clone operation.");
        }

        //设置模板画面
        // 注意：currentLiveFrameSnapshot 是第一次克隆的结果。
        // ImageUtils.crop 应该返回一个新的 Frame 对象，或者不修改输入的 Frame。
        // 此处假设 ImageUtils.crop 返回新的 Frame，或者其对输入的 Frame 的修改是可接受的。
        try (Frame croppedFrame = ImageUtils.crop(currentLiveFrameSnapshot, templateImageConfig.getRoi().toRect(getSize()))) {
            if (croppedFrame == null) {
                throw new OperationFailNotification("Frame cropping for template resulted in null.");
            }
            templateFrame.set(croppedFrame.clone());
        } // sourceForCrop 和 croppedFrame 会在此处关闭。
        // 这意味着 currentLiveFrameSnapshot（第一次克隆的帧）也会被关闭。
        // 如果 VisionEventHandler 后续需要通过 staticLiveFrame.get() 使用它，则此处关闭它是有问题的。
        // 为了简化，暂时维持此逻辑，但请注意这个潜在的生命周期问题。
        // 更安全的做法可能是不将 sourceForCrop 放入 try-with-resources，除非确定 VisionEventHandler 不需要它。
        // 或者 VisionEventHandler 总是使用 staticLiveFrame.get().clone()。

        VisionAlgorithm visionAlgorithm = new VisionAlgorithm();
        visionAlgorithm.setOnlyTestSimilarity(true);
        visionAlgorithm.setAlgorithmName(templateImageConfig.getAlgorithm());
        //进行图像识别
        VisionResult visionResult = algorithmInvoker.handleAlgorithm(visionAlgorithm,
                new VisionEventHandler() {
                    @Override
                    public Frame captureLiveFrame() {
                        return staticLiveFrame.get();
                    }

                    @Override
                    public Frame getTemplateFrame(String templateName) {
                        return templateFrame.get();
                    }

                    @Override
                    public AbsoluteRoiRect getAbsoluteRoiRect(String templateName) {
                        return null;
                    }
                });
        staticLiveFrame.get().close();
        templateFrame.get().close();
        return visionResult;
    }

    default OperationResult testSimilarity(TemplateImageConfig templateImageConfig) throws OperationFailNotification {
        OperationResult operationResult = new OperationResult();
        List<String> visionAlgorithmList = new ArrayList<>();
        String visionAlgorithm = templateImageConfig.getAlgorithm();
        if (visionAlgorithm.equals(AlgorithmSets.allAlgorithmMatching)) {
            //所有图像匹配方法均测试一遍
            visionAlgorithmList.addAll(AlgorithmSets.allAlgorithmSets);
        } else {
            //只测试单一图像匹配方法
            visionAlgorithmList.add(visionAlgorithm);
        }
        //运行图像识别
        Map<String, VisionResult> visionResultMap = new HashMap<>(); //TODO： 后续考虑转换成一个类来管理
        for (String algorithmName : visionAlgorithmList) {
            templateImageConfig.setAlgorithm(algorithmName);
            VisionResult visionResult = calculateSimilarity(templateImageConfig);
            visionResultMap.put(algorithmName, visionResult);
        }
        //处理识别结果
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, VisionResult> entry : visionResultMap.entrySet()) {
            sb.append(String.format("%s:%s", entry.getKey(), entry.getValue().getMessage())).append("\n");
        }
        boolean isOk = visionResultMap.values().stream().allMatch(VisionResult::isPassed);
        operationResult.setOk(isOk);
        operationResult.setMessage(sb.toString());
        operationResult.setData(visionResultMap);
        return operationResult;
    }
}
