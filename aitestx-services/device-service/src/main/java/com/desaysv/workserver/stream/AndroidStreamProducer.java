package com.desaysv.workserver.stream;


//TODO:统一PortStreamProducer和AndroidStreamProducer
public abstract class AndroidStreamProducer extends StreamProducer {

    public AndroidStreamProducer() {
    }

    public AndroidStreamProducer(String url) {
        super(url);
    }

    public AndroidStreamProducer(String url, int w, int h) {
        super(url, w, h);
    }

    public AndroidStreamProducer(int w, int h) {
        super(w, h);
    }

    protected abstract void startStream(String deviceModel, String serialNumber) throws Exception;

    public abstract String pushStream(String deviceModel, String serialNumber) throws Exception;
}
