package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 11:31
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
public interface AbstractRobotFactory extends AbstractDeviceCreator {

    Device createDobotMG400(DeviceRegisterForm deviceRegisterForm);


    Device createDobotMagician(DeviceRegisterForm deviceRegisterForm);
}
