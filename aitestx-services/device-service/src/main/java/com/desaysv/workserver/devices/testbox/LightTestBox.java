package com.desaysv.workserver.devices.testbox;

import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.interfaces.light.ILightTestBoardBox;
import com.desaysv.workserver.devices.testbox.light.LightTestBoxConfig;
import com.desaysv.workserver.devices.testbox.light.ParseResult;
import com.desaysv.workserver.devices.testbox.light.TestBoxDeviceConfig;
import com.desaysv.workserver.devices.testbox.socket.TCPSocketClient;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.desaysv.workserver.devices.testbox.State.HL_ACQUIRE_BOARD_CARD;
import static com.desaysv.workserver.devices.testbox.State.getStatusValue;
import static com.desaysv.workserver.devices.testbox.TestBoxUtils.*;
import static com.desaysv.workserver.devices.testbox.light.ResponseParser.checkSingleChannelAcquisitionBroadCardResult;
import static com.desaysv.workserver.devices.testbox.light.ResponseParser.checkExecutionWriteBroadcastResult;
import static com.desaysv.workserver.utils.ByteUtils.*;


@Slf4j
public class LightTestBox extends TestBox implements ILightTestBoardBox {
    private TCPSocketClient tcpSocketClient;

    public LightTestBox(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    public LightTestBox() {

    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TEST_BOX;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.TestBox.LIGHT_TEST_BOX;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String deviceName = getDeviceName();
        String[] str = deviceName.split(":");
        String ip = str[0];
        String port = str[1];
        tcpSocketClient = new TCPSocketClient();
        return tcpSocketClient.socketConnect(ip, Integer.parseInt(port), 5000);
    }

    @Override
    public boolean close() throws DeviceCloseException {
        if (tcpSocketClient != null) {
            tcpSocketClient.socketDisConnect();
        }
        return true;
    }

    @Override
    public boolean writeRelaySwitchBoardCard(int chassisNumber, int slotNumber, int channelNumber, String command) throws BoardCardTransportException {
        if (!isValidCommand(command)) {
            log.warn("无效的命令: {}", command);
            return false;
        }
        String switchHexString = getSwitchHexValue(command);
        String data = buildRelaySwitchCommand(chassisNumber, slotNumber, channelNumber, switchHexString);
        if (data == null) return false;
        return checkExecutionWriteBroadcastResult(sendCommand(data));
    }



    @Override
    public boolean writeTriStateOutputBoardCard(int chassisNumber, int slotNumber, int channelNumber, int state) throws BoardCardTransportException {
        if (!isValidState(state)) {
            log.warn("无效的状态值: {}", state);
            return false;
        }
        String stateHexString = getStateHexValue(state);
        String data = buildTriStateOutputCommand(chassisNumber, slotNumber, channelNumber, stateHexString);
        if (data == null) return false;
        return checkExecutionWriteBroadcastResult(sendCommand(data));
    }

    @Override
    public List<PWMEntity> fetchPWMInputBoardCard(int chassisNumber, int slotNumber, int channelNumber) throws BoardCardTransportException {
        String command = buildPWMInputCommand(chassisNumber, slotNumber, channelNumber, PWM_INPUT_DATA);
        ParseResult parseResult = checkSingleChannelAcquisitionBroadCardResult(sendCommand(command));
        if (parseResult == null) return null;
        return parseResult.getPwmDataList();
    }

    @Override
    public List<Float> fetchVoltageByChannel(int chassisNumber, int slotNumber, int channelNumber) throws BoardCardTransportException {
        if (isSimulated()) {
            return Collections.singletonList((float) RandomUtil.randomDouble(1, 10));
        }
        String data = buildGetVoltageCommand(chassisNumber, slotNumber, channelNumber);
        ParseResult parseResult = checkSingleChannelAcquisitionBroadCardResult(sendCommand(data));
        if (parseResult == null) return null;
        return parseResult.getVoltages();
    }

    @Override
    public boolean setVoltAcquisitionBoardCardInit() {
        Map<String, TestBoxDeviceConfig> allLightTestBoxConfigMap = LightTestBoxConfig.getInstance().getLightTestBoxConfigMap();
        log.info("setAcquisitionBoardCardInit data: {}", allLightTestBoxConfigMap.size());
        Map<String, TestBoxDeviceConfig> filteredConfigMap = allLightTestBoxConfigMap.entrySet().stream()
                .filter(entry ->
                        entry.getKey().toLowerCase().contains("init".toLowerCase())
                                && HL_ACQUIRE_BOARD_CARD.equals(entry.getValue().getBoardType())
                )
                .collect(Collectors.toMap(
                        Map.Entry::getKey,           // Key提取规则
                        Map.Entry::getValue,          // Value提取规则
                        (oldVal, newVal) -> oldVal,   // 键冲突时保留旧值（根据业务需求调整）
                        () -> new LinkedHashMap<>((int) (allLightTestBoxConfigMap.size() / 0.75) + 1) // 初始化容量优化
                ));
        log.info("filteredConfigMap size: {}", filteredConfigMap.size());
        boolean initPass = true;
        for (TestBoxDeviceConfig config : filteredConfigMap.values()) {
            Integer chassisNumber = Integer.valueOf(config.getChassisNumber());
            Integer slotNumber = Integer.valueOf(config.getSlotNumber());
            Integer channelNumber = Integer.valueOf(config.getChannelNumber());
            String pullUpDownState = getStatusValue(HL_ACQUIRE_BOARD_CARD, config.getPullUpDownState());
            String command = buildVoltAcquisitionCommand(chassisNumber, slotNumber, channelNumber, pullUpDownState);
            if (command == null || !checkExecutionWriteBroadcastResult(sendCommand(command))) {
                initPass = false;
            }
        }
        return initPass;
    }

    @Override
    public boolean setVoltageBoardCardStatus(int chassisNumber, int slotNumber, int channelNumber, int status) throws BoardCardTransportException {
        String pullUpDownState = String.format("%02X",  status);
        String command = buildVoltAcquisitionCommand(chassisNumber, slotNumber, channelNumber, pullUpDownState);
        return command != null && checkExecutionWriteBroadcastResult(sendCommand(command));
    }

    // 解析反馈数据并返回电压值
    private float parseVoltageFromResponse(String response) throws BoardCardTransportException {
        if (response == null || response.length() < 8) { // 假设反馈数据至少包含 8 个字符
            log.error("无效的反馈数据: {}", response);
            throw new BoardCardTransportException("无效的反馈数据");
        }
        try {
            // 假设反馈数据的最后 4 个字符表示电压值（十六进制）
            String voltageHex = response.substring(response.length() - 4);
            int voltageInt = Integer.parseInt(voltageHex, 16); // 将十六进制转换为整数
            return voltageInt / 1000.0f; // 假设电压值的单位为毫伏，转换为伏特
        } catch (NumberFormatException e) {
            log.error("反馈数据解析失败: {}", response, e);
        }
        return -1;
    }

    @Override
    public boolean writePWMOutputBoardCard(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, PWMEntity pwmEntity) throws BoardCardTransportException {
        int frequency = (int) pwmEntity.getFrequency();
        int dutyCycle = (int) pwmEntity.getDutyCycle();
        String frequencyHexString = convertIntToFourHexBytes(frequency);
        String dutyCycleHexString = convertIntToTwoHexBytes(dutyCycle);
        String command = buildPWMOutCommand(chassisNumber, slotNumber, channelNumber, pullUpNumber, frequencyHexString, dutyCycleHexString);
        if (command == null) return false;
        return checkExecutionWriteBroadcastResult(sendCommand(command));
    }

    @Override
    public boolean writeResistanceBoardCard(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, int resistance) throws BoardCardTransportException {
        if (!judgeResistanceMinValue(pullUpNumber, resistance)) return false;
        String resistanceHexString = convertIntToSixHexBytes(resistance);
        String command = buildResistanceCommand(chassisNumber, slotNumber, channelNumber, pullUpNumber, resistanceHexString);
        if (command == null) return false;
        return checkExecutionWriteBroadcastResult(sendCommand(command));
    }


    private String sendCommand(String command) {
        if (tcpSocketClient == null) {
            return null;
        }
        try {
            byte checksum = calculateChecksumValue(hexStringToByteArr(command));
            String checksumHexString = String.format("%02X", checksum);
            String commandData = String.format("%s%s", command, checksumHexString);
            String res = tcpSocketClient.sendData(commandData);
            log.info("发送命令:{}， 接收到反馈：{}", commandData, res);
            if(res == null || res.isEmpty()) return null;
            //统一处理响应帧头（去掉帧头和数据长度,共两个byte，字串长度为4），不要影响之前的解析部分，因为帧头和数据长度是后面增加的
            return res.replaceAll("\\s+", "");
        } catch (Exception e) {
            log.error("命令发送失败: {}", e.getMessage(), e);
            return null;
        }
    }

}
