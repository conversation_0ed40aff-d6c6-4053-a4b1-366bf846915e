package com.desaysv.workserver.devices.daq.art_daq;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.base.DaqDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.monitor.MeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * USB3200N控制库
 */
@Slf4j
public class USB3200N extends DaqDevice implements MeasurementComparator {

    private final ArtDaqApi artDaqApi;

    public USB3200N() {
        this(new DeviceOperationParameter());
    }

    public USB3200N(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        artDaqApi = new ArtDaqApi();
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String deviceName = getDeviceName();
        log.info("正在连接USB3200N->{}", deviceName);
        return artDaqApi.init(deviceName);
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        artDaqApi.release();
        return true;
    }

    @Override
    public float fetchCurrent(Integer deviceChannel, String currentDirection) {
        return 0;
    }

    @Override
    public float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        try {
            return (float) artDaqApi.read();
        } catch (ArtDaqApi.ArtDaqError e) {
            throw new DeviceReadException(e);
        }
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Daq.USB3200N;
    }

}