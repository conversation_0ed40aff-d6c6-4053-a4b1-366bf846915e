package com.desaysv.workserver.common.aspect;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.common.annotation.DeviceMock;
import com.desaysv.workserver.common.annotation.OperationTargetAliasMap;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.devices.power.DefaultPower;
import com.desaysv.workserver.entity.Device;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Aspect
@Component
@Slf4j
@Lazy
public class DeviceMockAop {

    @Autowired
    private AppConfig appConfig;

    @Value("${appConfig.mockEnabled}")
    private boolean mockEnabled;

    @Pointcut("@annotation(com.desaysv.workserver.common.annotation.DeviceMock)")
    public void deviceMock() {
    }

    @Data
    private static class MockTarget {
        private Class<? extends OperationTarget> clazz;
    }

    @Around("deviceMock()")
    public Object aroundDeviceMock(ProceedingJoinPoint joinPoint) {
        if (mockEnabled || appConfig.isMockEnabled()) {
            Class<?> targetCls = joinPoint.getTarget().getClass();
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method;
            try {
                method = targetCls.getDeclaredMethod(methodSignature.getName(), methodSignature.getParameterTypes());
                DeviceMock mock = method.getAnnotation(DeviceMock.class);
                if (mock.enable()) {
                    OperationTargetAliasMap[] targetAliasMaps = mock.alias();
                    Map<String, MockTarget> mockTargetMap = new HashMap<>();
                    for (OperationTargetAliasMap aliasMap : targetAliasMaps) {
                        MockTarget mockTarget = new MockTarget();
                        mockTarget.setClazz(aliasMap.targetClazz());
                        mockTargetMap.put(aliasMap.deviceModel(), mockTarget);
                    }
                    Object[] args = joinPoint.getArgs();
                    String deviceModel = (String) args[0];

                    List<OperationTarget> operationTargets = new ArrayList<>();
                    int portNumber = 0;
                    for (int i = 0; i < mock.numbers(); i++) {
                        MockTarget mockTarget = mockTargetMap.get(deviceModel);
                        if (mockTarget == null) {
                            continue;
                        }
                        Class<? extends OperationTarget> mockClazz = mockTarget.getClazz();
                        String prefix = "MOCK_" + mockClazz.getSimpleName() + "_";
                        OperationTarget target = mockClazz.newInstance();
                        if (target instanceof Device) {
                            Device device = (Device) target;
                            if (PortDevice.class.isAssignableFrom(mockClazz)) {
                                device.setDevicePort(portNumber++);
                            }
                            if (DefaultPower.class.isAssignableFrom(mockClazz)) {
                                ((DefaultPower) device).setCommProtocol(mock.commProtocol());
                            }
                            device.setSimulated(true);
                            device.setDeviceUniqueCode(prefix + (i + 1));
                            int deviceIndex = Device.getDeviceModelIndex(deviceModel);
                            device.setDeviceName(deviceModel + "_" + "Mock" + "_" + deviceIndex);
                            device.setAliasName(deviceModel + "#" + deviceIndex);
                            operationTargets.add(device);
                        } else {
                            operationTargets.add(target);
                        }
                    }

                    operationTargets.removeIf(operationTarget -> {
                        if (operationTarget instanceof Device) {
                            Device d = (Device) operationTarget;
                            return Device.isLocked(d.getDeviceName(), d.getDeviceModel());
                        }
                        return false;
                    });
                    return operationTargets;
                }
            } catch (NoSuchMethodException | InstantiationException | IllegalAccessException e) {
                log.error(e.getMessage(), e);
            }
        }
        try {
            return joinPoint.proceed(joinPoint.getArgs());
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

}
