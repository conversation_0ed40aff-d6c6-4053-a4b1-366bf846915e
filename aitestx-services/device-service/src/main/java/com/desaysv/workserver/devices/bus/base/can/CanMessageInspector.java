package com.desaysv.workserver.devices.bus.base.can;

import cantools.dbc.DbcReader;
import cantools.dbc.Message;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.devices.bus.DbcUtils;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
public class CanMessageInspector {
    public static OperationResult inspectMessage(CanBus canBus, Integer deviceChannel, CanMessageInspectorRequest canMessageInspectorRequest) throws BusError {
        OperationResult operationResult = new OperationResult();
        if (canMessageInspectorRequest == null) {
            return operationResult.fail("CAN报文检测参数为空");
        }
        String idOrName = canMessageInspectorRequest.getMessageName();
        String expectedMessageData = canMessageInspectorRequest.getMessageData();
        Integer messageTimeOut = canMessageInspectorRequest.getTimeout();
        messageTimeOut = Optional.ofNullable(messageTimeOut)
                .filter(timeOut -> timeOut > 0) // 过滤掉非正数
                .map(timeOut -> timeOut * 1000) // 转换为毫秒
                .orElse(2000); // 默认值为2000毫秒

        if (idOrName == null || idOrName.trim().isEmpty()) {
            return operationResult.fail("报文ID或名称不能为空");
        }

        CanConfig deviceConfig = canBus.getDeviceConfig();
        if (deviceConfig == null) {
            return operationResult.fail("设备配置未找到");
        }

        try {
            byte[] data;
            if (isHexadecimal(idOrName)) {
                // 十六进制数据
                data = canBus.readDataByIdHex(deviceChannel, Integer.decode(idOrName), false, messageTimeOut);
                if (data.length == 0) {
                    data = canBus.readDataByIdHex(deviceChannel, Integer.decode(idOrName), true, messageTimeOut);
                }
            } else {
                DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
                if (dbcConfig == null) {
                    return operationResult.fail(String.format("通道 %d 的DBC配置未找到", deviceChannel));
                }
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                Message message = null;
                for (DbcReader reader : dbcReaders) {
                    message = reader.getBus().getMessageMap().get(idOrName);
                    if (message != null) {
                        break;
                    }
                }
                if (message == null) {
                    return operationResult.fail(String.format("CAN通道%d报文%s未找到", deviceChannel, idOrName));
                }
                data = canBus.readDataByIdHex(deviceChannel, message.getIntegerId(), message.isFd(), messageTimeOut);
            }
            if (expectedMessageData == null || expectedMessageData.trim().isEmpty()) {
                if (data.length > 0) {
                    String msg = String.format("检测到报文 %s", idOrName);
                    log.info(msg);
                    return operationResult.ok(msg);
                } else {
                    String msg = String.format("没有检测到报文 %s", idOrName);
                    log.warn(msg);
                    return operationResult.fail(msg);
                }
            }

            String actualReceiveData = ByteUtils.byteArrayToHexString(data);
            String actual = actualReceiveData.replaceAll("\\s+", "").toUpperCase();

            String expected = expectedMessageData.replaceAll("\\s+", "").toUpperCase();
            if (actual.equals(expected)) {
                String msg = String.format("检测到报文 %s 数据:%s", idOrName, actualReceiveData);
                log.info(msg);
                return operationResult.ok(msg);
            } else {
                String msg = String.format("报文 %s 数据不匹配，期望:%s，实际:%s", idOrName, expectedMessageData, actualReceiveData);
                log.warn(msg);
                return operationResult.fail(msg);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return operationResult.fail("CAN报文检测出错: " + e.getMessage());
        }
    }

    /*
     * 校验是否是16进制数据
     * */
    public static boolean isHexadecimal(String messageName) {
        // 正则表达式匹配16进制数字，支持以0x或0X开头
        String hexPattern = "^(0[xX])?[0-9A-Fa-f]+$";
        return messageName.matches(hexPattern);
    }
}
