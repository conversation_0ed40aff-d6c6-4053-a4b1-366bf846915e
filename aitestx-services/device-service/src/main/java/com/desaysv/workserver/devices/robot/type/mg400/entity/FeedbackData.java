package com.desaysv.workserver.devices.robot.type.mg400.entity;

import com.desaysv.workserver.devices.robot.base.RobotPose;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-21 9:37
 * @description :
 * @modified By :
 * @since : 2022-7-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedbackData extends RobotPose {

    private double j1;
    private double j2;
    private double j3;
    private double j4;

//    private double x;
//    private double y;
//    private double z;
//    private double r;
//    private double slideRail;

    private Integer xyzSpeedFactor;

    private Integer xyzAccelerationSpeedFactor;

    private Integer modeCode;

    private String mode;

    private Integer user;

    private String digitalInputs;

    private String digitalOutputs;

    private String errorMessage;
    
}
