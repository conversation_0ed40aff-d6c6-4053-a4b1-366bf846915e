package com.desaysv.workserver.devices.I2C;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.I2C.interfaces.IUsbI2C;
import com.desaysv.workserver.devices.serial.SerialDevice;
import com.desaysv.workserver.devices.serial.text_match.SerialJudgeParameter;
import com.desaysv.workserver.devices.serial.text_match.WaitFilterParameter;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class I2CDevice extends SerialDevice implements IUsbI2C {
    public I2CDevice() {
        this(new DeviceOperationParameter());
    }

    public I2CDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SERIAL;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.UsbI2C.USB_I2C;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        //return UsbIICDLL.USB2UARTSPIIICDll.INSTANCE.OpenUsb(0) == 1;

        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        //return UsbIICDLL.USB2UARTSPIIICDll.INSTANCE.CloseUsb(0) == 0;
        return true;
    }

    @Override
    public boolean mustExistMonitorStart(String text) {
        return false;
    }

    @Override
    public OperationResult mustExistMonitorEnd() {
        return null;
    }

    @Override
    public boolean forbidExistMonitorStart(String text) {
        return false;
    }

    @Override
    public OperationResult forbidExistMonitorEnd() {
        return null;
    }

    @Override
    public boolean waitFilter(WaitFilterParameter waitFilterParameter) {
        return false;
    }

    @Override
    public boolean judgeText(SerialJudgeParameter serialJudgeParameter) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean setI2CParameter(IICConfig iicConfig) {
//        int result = UsbIICDLL.USB2UARTSPIIICDll.INSTANCE.ConfigIICParam(iicConfig.getRate(), iicConfig.getClkSLevel(), iicConfig.getUsbIndex());
//        return result == 0;
        return true;
    }

    @Override
    public boolean sendAndMatch(MessageText messageText) throws OperationFailNotification {
        return true;
    }

    @Override
    public String readPortFeedback(boolean isHex, String compareMessage, int timeoutMilliseconds) {
        return "";
    }

    @Override
    public boolean sendAndMatch(IICMessage iicMessage) {
        IICConfig iicConfig = iicMessage.getIicConfig();
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.IICSendAndRcvData(iicConfig.getStartBit(),
                iicConfig.getStopBit(), iicMessage.getSendBuf(),
                iicMessage.getRcvBuf(), iicMessage.getSlen(),
                iicMessage.getRlen(), iicMessage.getUsbIndex());
        return result >= 0;
    }

    public boolean send(IICMessage iicMessage) {
        IICConfig iicConfig = iicMessage.getIicConfig();
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.IICSendData(
                iicConfig.getStartBit(), iicConfig.getStopBit(), iicMessage.getSendBuf(),
                iicMessage.getSlen(), iicConfig.getUsbIndex());
        return result >= 0;
    }

    @Override
    public boolean receive(IICMessage iicMessage) {
        IICConfig iicConfig = iicMessage.getIicConfig();
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.IICRcvData(
                iicConfig.getStopBit(), iicMessage.getRcvBuf(),
                iicMessage.getRlen(), iicConfig.getUsbIndex());
        return result >= 0;
    }

    @Override
    public boolean setI2CIO(IICConfig iicConfig) {
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.IOSetAndRead(
                iicConfig.getIoNum(), iicConfig.getIoDir(),
                iicConfig.getIoBit(), iicConfig.getUsbIndex());
        return result == 0 || result == 1;


    }

    @Override
    public boolean pwmOutputBegin(IICConfig iicConfig) {
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.PWMOut(
                iicConfig.getPreDivision(), iicConfig.getPeriod(),
                iicConfig.getPulse(), iicConfig.getPulseNum(),
                iicConfig.getUsbIndex());
        return result == 0;
    }

    @Override
    public boolean pwmOutputStop(IICConfig iicConfig) {
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.PWMClose(iicConfig.getUsbIndex());
        return result == 0;
    }

    @Override
    public boolean adcGather(IICConfig iicConfig) {
        int result = UsbIicDll.Usb2UartSpiIicDll.INSTANCE.GetADCVal(iicConfig.getAdcNum(), iicConfig.getUsbIndex());
        return result >= 0;
    }

    public boolean checkSlaveAddress(IICMessage iicMessage) {
        return true;
    }

    public boolean registerSend(IICMessage iicMessage) {
        return true;
    }

    public boolean registerRead(IICMessage iicMessage) {
        return true;
    }

    public boolean directSend(IICMessage iicMessage) {
        return true;
    }

    public boolean directRead(IICMessage iicMessage) {
        return true;
    }


}
