package com.desaysv.workserver.devices.testbox.interfaces;

import com.desaysv.workserver.devices.testbox.PWMEntity;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;

import java.util.List;
import java.util.Map;

public interface ITestBoardBox
        extends IRelaySwitchBoard, IResistorBoard, IPwmOutBoard, IPwmInBoard, ITriStateOutputBoard, IVoltageAcquireBoard {

    List<Integer> fetchResistanceBoardCard() throws BoardCardTransportException;

    Map<String, Boolean> fetchRelayBoardCard() throws BoardCardTransportException;

    Map<String, PWMEntity> fetchPWMOutputBoardCard() throws BoardCardTransportException;

    List<Integer> fetchTriStateOutputBoardCard() throws BoardCardTransportException;

    Integer fetchResistorByChannel(Integer deviceChannel) throws BoardCardTransportException;

    Integer fetchRelayBoardCardByChannel(Integer deviceChannel) throws BoardCardTransportException;

}
