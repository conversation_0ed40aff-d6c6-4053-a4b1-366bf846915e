package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.oscilloscope.RigolDhoOscilloscope;
import com.desaysv.workserver.devices.oscilloscope.SDS5000XOscilloscope;
import com.desaysv.workserver.devices.oscilloscope.SDS6000XOscilloscope;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractOscilloscopeFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class OscilloscopeFactory implements AbstractOscilloscopeFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Oscilloscope.RIGOL_800:
                return createRigolDHODevice(deviceRegisterForm);
            case DeviceModel.Oscilloscope.SDS_5000X:
                return createSDS5000XDevice(deviceRegisterForm);
            case DeviceModel.Oscilloscope.SDS_6000X:
                return createSDS6000XDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createRigolDHODevice(DeviceRegisterForm deviceRegisterForm) {
        return new RigolDhoOscilloscope(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createSDS5000XDevice(DeviceRegisterForm deviceRegisterForm) {
        return new SDS5000XOscilloscope(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createSDS6000XDevice(DeviceRegisterForm deviceRegisterForm) {
        return new SDS6000XOscilloscope(deviceRegisterForm.getDeviceOperationParameter());
    }
}
