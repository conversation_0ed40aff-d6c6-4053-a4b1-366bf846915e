package com.desaysv.workserver.devices.camera.basler;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 14:33
 * @description :
 * @modified By :
 * @since : 2022-4-12
 */
@Getter
@Slf4j
public class BaslerCamera extends CameraDevice {
    private final String deviceModel = DeviceModel.Camera.USB_CAMERA;

//    private final int width = 1624;
//    private final int height = 1234;
    /**
     * puA1600-60uc
     * <a href="https://www.baslerweb.cn/zh-cn/shop/pua1600-60uc/">...</a>
     * Basler_GenICam_Source_860BB310-5D01-11D0-BD3B-00A0C911CE86
     *
     * @device:sw:{860BB310-5D01-11D0-BD3B-00A0C911CE86}\Basler GenICam Source
     */
    private final static int width = 1600;
    private final static int height = 1200;

    public BaslerCamera() {
        this(new DeviceOperationParameter());
    }

    public BaslerCamera(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
//        DeviceOperationParameter parameter = deviceOperationParameter;
        deviceOperationParameter.setWidth(width);
        deviceOperationParameter.setHeight(height);
    }

}
