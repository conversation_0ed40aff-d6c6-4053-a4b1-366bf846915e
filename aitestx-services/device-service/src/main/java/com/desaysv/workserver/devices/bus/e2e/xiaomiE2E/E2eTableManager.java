package com.desaysv.workserver.devices.bus.e2e.xiaomiE2E;

import java.util.Vector;
import lombok.Getter;

public class E2eTableManager {
	@Getter
    private final static E2eTableManager instance = new E2eTableManager();
	@Getter
	private final Vector<XiaomiE2eData> ve2eList = new Vector<>();

	private E2eTableManager() {
        ve2eList.add(new XiaomiE2eData("0x062D", "0x150", "Byte0", "Byte1~8"));
		ve2eList.add(new XiaomiE2eData("0x070A", "0x150", "Byte9", "Byte10~11"));
		ve2eList.add(new XiaomiE2eData("0x0628", "0x151", "Byte0", "Byte1~9"));
		ve2eList.add(new XiaomiE2eData("0x0629", "0x151", "Byte10", "Byte11~17"));
		ve2eList.add(new XiaomiE2eData("0x0624", "0x161", "Byte0", "Byte1"));
		ve2eList.add(new XiaomiE2eData("0x0713", "0x198", "Byte25", "Byte26~27"));
		ve2eList.add(new XiaomiE2eData("0x0705", "0x1E7", "Byte12", "Byte13~17"));
		ve2eList.add(new XiaomiE2eData("0x2023", "0x226", "Byte9", "Byte10~12"));
		ve2eList.add(new XiaomiE2eData("0x0D01", "0x268", "Byte3", "Byte4~10"));
		ve2eList.add(new XiaomiE2eData("0x0D02", "0x268", "Byte11", "Byte12~16"));
		ve2eList.add(new XiaomiE2eData("0x2008", "0x2DD", "Byte0", "Byte1~4"));
		ve2eList.add(new XiaomiE2eData("0x0100", "0x350", "Byte0", "Byte1~2"));
		ve2eList.add(new XiaomiE2eData("0x0626", "0xB3", "Byte0", "Byte1~26"));
		ve2eList.add(new XiaomiE2eData("0x201A", "0xF1", "Byte0", "Byte1~2"));
		ve2eList.add(new XiaomiE2eData("0x2002", "0xF1", "Byte4", "Byte5~6"));
		ve2eList.add(new XiaomiE2eData("0x2026", "0xF1", "Byte7", "Byte8~14"));
		ve2eList.add(new XiaomiE2eData("0x200C", "0xF1", "Byte19", "Byte20~21"));
	}
}