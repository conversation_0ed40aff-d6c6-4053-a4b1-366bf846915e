package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractElectricRelayFactory extends AbstractDeviceCreator {
    Device createJYDAM1600CDevice(DeviceRegisterForm deviceRegisterForm);

    Device createJZQWL_IO_1BXRC32Device(DeviceRegisterForm deviceRegisterForm);

    Device createZQWL_IO_1CX5R4Device(DeviceRegisterForm deviceRegisterForm);

    Device createZQWL_IO_1BX3C8Device(DeviceRegisterForm deviceRegisterForm);
}
