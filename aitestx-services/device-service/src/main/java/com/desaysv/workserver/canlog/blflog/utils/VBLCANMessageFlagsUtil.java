package com.desaysv.workserver.canlog.blflog.utils;

public class VBLCANMessageFlagsUtil {
    // 位掩码定义
    private static final int DIR_MASK = 0x0F;
    private static final int RTR_MASK = 0x80;
    private static final int WU_MASK = 0x40;
    private static final int NERR_MASK = 0x20;

    // 获取方向标志
    public static int getDirection(byte flags) {
        return flags & DIR_MASK;
    }

    // 获取RTR标志
    public static boolean isRTR(byte flags) {
        return (flags & RTR_MASK) != 0;
    }

    // 获取WU标志
    public static boolean isWU(byte flags) {
        return (flags & WU_MASK) != 0;
    }

    // 获取NERR标志
    public static boolean isNERR(byte flags) {
        return (flags & NERR_MASK) != 0;
    }

    // 设置标志值
    public static byte setFlags(int dir, boolean rtr) {
        byte flags = (byte)(dir & DIR_MASK);
        if (rtr) {
            flags |= RTR_MASK;
        }
        return flags;
    }

    /**
     * 设置标志值
     * @param dir 流向 0:RX 1:TX
     * @param rtr 远程传输 true:RTR false:No RTR
     * @param wu 唤醒信息(高压)
     * @param nerr 单线操作(NERR)
     * @return
     */
    public static byte setFlagsExtended(int dir, boolean rtr, boolean wu, boolean nerr) {
        byte flags = (byte)(dir & DIR_MASK);
        if (rtr) {
            flags |= RTR_MASK;
        }
        if (wu) {
            flags |= WU_MASK;
        }
        if (nerr) {
            flags |= NERR_MASK;
        }
        return flags;
    }
}
