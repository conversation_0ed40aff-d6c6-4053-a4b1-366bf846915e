package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.PWMEntity;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ILightPwmOutBoard {
    Logger log = LogManager.getLogger(ILightPwmOutBoard.class.getSimpleName());

    /**
     * 写入PWM输出（10个通道）
     *
//     * @param deviceChannel 板卡通道
     * @param pwmEntity
     */
    boolean writePWMOutputBoardCard(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, PWMEntity pwmEntity) throws BoardCardTransportException;


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).CHANGE_LIGHT_PWM_OUT_VALUE"})
    default void writePwmOut(Integer deviceChannel, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, float frequency, float duty) throws OperationFailNotification {
        log.info("PWM输出板卡{}-{}-{}，设置频率{}HZ和占空比{}%", deviceChannel, slotNumber, channelNumber, frequency, duty);
        PWMEntity pwmEntity = new PWMEntity(frequency, duty);
        try {
            boolean isWriteOk = writePWMOutputBoardCard(deviceChannel, slotNumber, channelNumber, pullUpNumber, pwmEntity);
            log.info("PWM输出板卡设置:%s", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info("PWM输出板卡设置:%s", isWriteOk ? "成功" : "失败");
            if (!isWriteOk) {
                throw new OperationFailNotification("PWM输出板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("PWM输出板卡读取失败:" + e.getMessage());
        }
    }
}