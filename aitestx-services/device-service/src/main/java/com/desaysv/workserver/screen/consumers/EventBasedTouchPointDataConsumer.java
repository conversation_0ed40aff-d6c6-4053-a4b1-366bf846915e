package com.desaysv.workserver.screen.consumers;

import com.desaysv.workserver.utils.ByteUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * 事件触发的报点数据消费者
 */
@Slf4j
public class EventBasedTouchPointDataConsumer implements Consumer<byte[]> {

    @Getter
    private final List<byte[]> touchPointBuffer = new ArrayList<>();

    private boolean collectStarted = false; //是否采集

    @Override
    public void accept(byte[] bytes) {
        if (collectStarted) {
            log.info("收集报点数据:{}", ByteUtils.byteArrayToHexString(bytes));
            touchPointBuffer.add(bytes);
        }
    }

    /**
     * 开始报点采集
     */
    public void startCollect() {
        touchPointBuffer.clear();
        collectStarted = true;
    }

    /**
     * 结束报点采集
     */
    public List<byte[]> endCollect() {
        collectStarted = false;
        return new ArrayList<>(touchPointBuffer);
    }

}
