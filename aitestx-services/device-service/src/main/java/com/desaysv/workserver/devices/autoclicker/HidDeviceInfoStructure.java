package com.desaysv.workserver.devices.autoclicker;

import com.sun.jna.Structure;
import com.sun.jna.WString;

import java.util.Arrays;
import java.util.List;

public class HidDeviceInfoStructure extends Structure implements Structure.ByReference {
    public String path;
    public short vendor_id;
    public short product_id;
    public WString serial_number;
    public short release_number;
    public WString manufacturer_string;
    public WString product_string;
    public short usage_page;
    public short usage;
    public int interface_number;
    public HidDeviceInfoStructure next;

    public HidDeviceInfoStructure() {
    }

    public HidDeviceInfoStructure next() {
        return this.next;
    }

    public boolean hasNext() {
        return this.next != null;
    }

    protected List<String> getFieldOrder() {
        return Arrays.asList("path", "vendor_id", "product_id", "serial_number", "release_number", "manufacturer_string", "product_string", "usage_page", "usage", "interface_number", "next");
    }

    public String show() {
        String str = "HidDevice\n";
        str = str + "\tpath:" + this.path + ">\n";
        str = str + "\tvendor_id: " + Integer.toHexString(this.vendor_id) + "\n";
        str = str + "\tproduct_id: " + Integer.toHexString(this.product_id) + "\n";
        str = str + "\tserial_number: " + this.serial_number + ">\n";
        str = str + "\trelease_number: " + this.release_number + "\n";
        str = str + "\tmanufacturer_string: " + this.manufacturer_string + ">\n";
        str = str + "\tproduct_string: " + this.product_string + ">\n";
        str = str + "\tusage_page: " + this.usage_page + "\n";
        str = str + "\tusage: " + this.usage + "\n";
        str = str + "\tinterface_number: " + this.interface_number + "\n";
        return str;
    }
}