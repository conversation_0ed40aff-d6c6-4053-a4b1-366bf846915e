package com.desaysv.workserver.operation.command;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationCommand;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.ImageOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.context.AlgorithmInvoker;
import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.devices.camera.base.VisionRecognizeRequest;
import com.desaysv.workserver.devices.camera.config.BackTrackConfig;
import com.desaysv.workserver.devices.camera.config.CameraConfig;
import com.desaysv.workserver.devices.camera.config.FailVideoConfig;
import com.desaysv.workserver.devices.camera.config.TakePhotoConfig;
import com.desaysv.workserver.devices.robot.base.DefaultDobotDevice;
import com.desaysv.workserver.devices.robot.base.DobotDevice;
import com.desaysv.workserver.devices.robot.base.RobotTouchEvent;
import com.desaysv.workserver.entity.*;
import com.desaysv.workserver.exceptions.UnSupportedOperationMethod;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.factory.DeviceFileManager;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.finder.CameraDeviceFinder;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.manager.ImageFileManager;
import com.desaysv.workserver.model.TemplatePicture;
import com.desaysv.workserver.model.roi.*;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.operation.parameter.RobotImagePoint;
import com.desaysv.workserver.service.TemplateImageService;
import com.desaysv.workserver.service.TemplateRoiService;
import com.desaysv.workserver.stream.StreamService;
import com.desaysv.workserver.utils.*;
import com.desaysv.workserver.utils.command.detect.Detection;
import com.desaysv.workserver.utils.command.detect.DetectionResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.bytedeco.opencv.global.opencv_imgproc.rectangle;

/**
 * 图像操作指令
 */
@Slf4j
@Component
@Lazy
public class ImageOperationCommand implements OperationCommand, TestProcessListener {

    @Autowired
    private AlgorithmInvoker algorithmInvoker;

    @Autowired
    private CameraDeviceFinder cameraDeviceFinder;

    @Autowired
    private StreamService streamService;

    @Autowired
    private TemplateRoiService templateRoiService;

    @Autowired
    private TemplateImageService templateImageService;

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    @Autowired
    private DeviceFinderManager deviceFinderManager;
    @Getter
    @Autowired
    private ActionSequenceExecutionContextInfo actionSequenceExecutionContextInfo;
    private final ImageAlgorithmConfig imageAlgorithmConfig;
    private final static String robotName = "Dobot_MG400#1";
    private double pixelCoefficient;
    @Setter
    private String allImageStorageFolderName = "";
    private static final String DETECT_URL = "http://10.219.78.136:8000/detect";
    private final OkHttpClient client = new OkHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();
    @FunctionalInterface
    interface OperationMethodConsumer<T> {
        void accept(T t) throws UnSupportedOperationMethod, OperationFailNotification, IOException;
    }

    private final Map<OperationMethod, OperationMethodConsumer<ImageOperationContext>> operationMethodMap = new HashMap<>();


    @Data
    public static class ImageOperationContext {
        private String projectName;
        private TemplateImageConfig templateImageConfig;
        private Device device;
        private boolean onlyTestSimilarity;
        private Operation operation;
        private OperationResult operationResult;
    }

    public ImageOperationCommand() {
        TestProcessManager.addTestProcessListener(this);
        imageAlgorithmConfig = new ImageAlgorithmConfig();
        initializeOperationMethodMap();
    }

    private boolean checkIfTemplateExist(String templateName) {
        return true;
    }

    /**
     * Frame列表对比
     *
     * @param liveFrames Frame列表
     * @param threshold  阈值
     * @return 识别结果
     */
    public VisionResult compareFrames(Frame[] liveFrames, float threshold, String visionAlgorithmType) throws OperationFailNotification {
        if (liveFrames.length < 2) {
            return new VisionResult();
        }
        VisionAlgorithm visionAlgorithm = new VisionAlgorithm();
        if ("模板匹配".equals(visionAlgorithmType)) {
            visionAlgorithm.setAlgorithmName(AlgorithmSets.cvTemplateMatching);
        } else if ("精准匹配".equals(visionAlgorithmType)) {
            visionAlgorithm.setAlgorithmName(AlgorithmSets.strictTemplateMatching);
        } else if ("像素匹配".equals(visionAlgorithmType)) {
            visionAlgorithm.setAlgorithmName(AlgorithmSets.perPixelTemplateMatching);
        } else if ("颜色匹配".equals(visionAlgorithmType)) {
            visionAlgorithm.setAlgorithmName(AlgorithmSets.colorMatching);
        }
        visionAlgorithm.setMustExist(true);
        visionAlgorithm.setThreshold(threshold);
        return algorithmInvoker.handleAlgorithm(visionAlgorithm, new VisionEventHandler() {
            @Override
            public Frame captureLiveFrame() {
                return liveFrames[0];
            }

            @Override
            public Frame getTemplateFrame(String templateName) {
                return liveFrames[1];
            }

            @Override
            public AbsoluteRoiRect getAbsoluteRoiRect(String templateName) {
                return null;
            }
        });
    }

    /**
     * 增加机械臂校准
     *
     * @param robotDevice 机械臂
     * @return 操作结果
     */
    public void addRobotCalibration(DobotDevice robotDevice) {
        //智能校准和点击
        List<Device> deviceList = cameraDeviceFinder.findAllPhysicalCameras(false);
        if (!deviceList.isEmpty()) {
            Device cameraDevice = deviceList.get(0);
            log.info("启用相机校准:{}", cameraDevice.getDeviceName());
            OperationResult operationResult = cameraDevice.openForOperationResult();
            if (operationResult.isFailed()) {
                return;
            }
            Frame[] liveFrames = new Frame[2];
            if (!robotDevice.isTouchEventKeyValid(DobotDevice.smartTouchKey)) {
                RobotTouchEvent event = new RobotTouchEvent() {
                    @Override
                    public OperationResult beforeTouch(DobotDevice dobotDevice, ScaledRoiRect scaledRoiRect) throws FrameGrabberException {
                        log.info("触摸前拍照:{}", scaledRoiRect);
                        Frame frame = streamService.grab(cameraDevice, scaledRoiRect);
                        final Frame copyFrame = frame.clone();
                        liveFrames[0] = copyFrame;
                        return new OperationResult();
                    }

                    @Override
                    public OperationResult afterTouch(DobotDevice dobotDevice, ScaledRoiRect scaledRoiRect) throws FrameGrabberException, OperationFailNotification {
                        log.info("触摸后拍照:{}", scaledRoiRect);
                        OperationResult operationResult = new OperationResult();
                        Frame frame = streamService.grab(cameraDevice, scaledRoiRect);
                        liveFrames[1] = frame;
                        VisionResult visionResult = compareFrames(liveFrames, 0.8f, AlgorithmSets.perPixelTemplateMatching);
                        if (visionResult.isPassed()) {
                            log.info("前后图片一致，机械臂进行校准");
                            operationResult.setRetryTimes(3);
                            operationResult.setRetry(true);
                            operationResult.setMessage("触摸前后拍摄图像无变化:" + visionResult.getMessage());
                        } else {
                            log.info("前后图片不一致，机械臂无需进行校准");
                        }
                        operationResult.setOk(!visionResult.isPassed());
                        operationResult.setData(visionResult.simpleResult());
                        return operationResult;
                    }
                };
                robotDevice.addTouchEvent(DobotDevice.smartTouchKey, event);
                log.info("机械臂已设置智能图像校准");
            }
        }
    }

    /**
     * 准备图像指令
     *
     * @param executionContext 执行上下文
     * @param operation        图像操作
     * @return 图像操作准备信息
     */
    private ImageOperationContext prepareContext(ExecutionContext executionContext, Operation operation) {
        OperationTarget operationTarget = operation.getOperationTarget();
        DeviceRegisterForm registerForm = DeviceFileManager.getInstance().readDeviceRegisterForm(operationTarget);
        Device device = registerForm == null ? null : deviceRegisterManager.getDevice(registerForm.getDeviceName());
        final OperationResult operationResult = new OperationResult();
        operationResult.setOk(true);
        if (device == null) {
            //TODO:根据deviceType查找所有端口并连接
            //TODO：迁移到@DeviceFinder.java
            List<Device> deviceList = deviceFinderManager.getCameraDeviceFinder().findAllPhysicalCameras(operationTarget.getDeviceModel(), false);
            Optional<Device> connectedDevice = deviceList.stream().filter(d -> d.getAliasName().equals(operationTarget.getAliasName())).findFirst();
            if (connectedDevice.isPresent()) {
                assert registerForm != null;
                device = deviceRegisterManager.registerDevice(registerForm);
                if (executionContext != null) {
                    device.getExecutionContext().set(executionContext);
                }
                OperationResult devOperationResult = device.openForOperationResult();
                operationResult.setOk(devOperationResult.isOk());
                operationResult.setMessage(devOperationResult.getMessage());
                operationResult.setData(devOperationResult.getData());
            } else {
                String warning = operationTarget.getAliasName() + "设备未找到";
                log.warn(warning);
                operationResult.setOk(false);
                operationResult.setMessage(warning);
            }
        }
        ImageOperationContext imageOperationContext = new ImageOperationContext();
        imageOperationContext.setOperation(operation);
        Object operationObject = imageOperationContext.getOperation().getOperationObject();
        //这里要避免影响
        if (!(operationObject instanceof Boolean) && !(operationObject instanceof String) && !(operationObject instanceof Integer)) {
            final TemplateImageConfig templateImageConfig = TemplateImageConfig.buildByJsonObject(operationObject);
            imageOperationContext.setTemplateImageConfig(templateImageConfig);
        }
        imageOperationContext.setDevice(device);
        imageOperationContext.setOperationResult(operationResult);
        assert executionContext != null;
        imageOperationContext.setProjectName(executionContext.getProjectName());
        return imageOperationContext;
    }

    /**
     * 获取机械臂
     *
     * @param robotAliasName 机械臂别名
     * @return 操作结果
     */
    private Device getRobot(String robotAliasName) {
        return deviceFinderManager.findDeviceByAliasName(robotAliasName);
    }

    /**
     * 机械臂视觉引导点击
     *
     * @param pixelPoint            像素点
     * @param imageOperationContext 操作上下文
     * @return 操作结果
     */
    public OperationResult robotVisionGuideClick(PointInt pixelPoint, ImageOperationContext imageOperationContext) {
        TemplateImageConfig templateImageConfig = imageOperationContext.getTemplateImageConfig();
        OperationTarget guideDevice = templateImageConfig.getGuideDevice();
        Device device = getRobot(guideDevice.getAliasName());

        if (device != null) {
            DefaultDobotDevice robotDevice = (DefaultDobotDevice) device;
            robotDevice.visionTouch(pixelPoint);
            return OperationResult.staticOk();
        }
        return OperationResult.staticFail("设备不存在");
    }

    /**
     * 准备图像算法
     *
     * @return 操作步骤
     */
    private VisionAlgorithm prepareVisionAlgorithm(VisionRecognizeRequest visionRecognizeRequest) {
        VisionAlgorithm visionAlgorithm = new VisionAlgorithm();
        final String templateName = visionRecognizeRequest.getTemplateName();
        visionAlgorithm.setTemplateName(templateName);
        visionAlgorithm.setAlgorithmConfig(imageAlgorithmConfig);
        //配置图像算法
        if (visionRecognizeRequest.getAlgorithm() == null) {
            visionAlgorithm.setAlgorithmName(VisionAlgorithm.DEFAULT_ALGORITHM);
        } else {
            visionAlgorithm.setAlgorithmName(visionRecognizeRequest.getAlgorithm());
        }
        visionAlgorithm.setMatchedText(visionRecognizeRequest.getMatchedText());
        visionAlgorithm.setColorMatchEnabled(visionRecognizeRequest.isColorMatchEnabled());
        visionAlgorithm.setRecognizedDuration(visionRecognizeRequest.getRecognizedDuration());
        visionAlgorithm.setTimeout(visionRecognizeRequest.getTimeout());
        visionAlgorithm.setPreWaitTime(visionRecognizeRequest.getPreWaitTime());
        visionAlgorithm.setThreshold(visionRecognizeRequest.getThreshold());
        visionAlgorithm.setMustExist(visionRecognizeRequest.isMustExist());
        visionAlgorithm.setOnlyTestSimilarity(visionRecognizeRequest.isOnlyTestSimilarity());
        visionAlgorithm.setRoiEnlargePercent(visionRecognizeRequest.getRoiEnlargePercent());
        return visionAlgorithm;
    }


    /**
     * 处理图像识别结果
     *
     * @param visionResult
     * @param projectName
     * @param device
     * @param distanceEnabled
     * @return
     */
    public VisionResult handleVisionResult(VisionResult visionResult, String projectName, Device device, boolean distanceEnabled) {
        String templateImageName = visionResult.getTemplateName();
        if (StringUtils.isEmpty(templateImageName)) {
            log.warn("无法保存失败图片，图像模板名为空");
            return visionResult;
        }
        //模板图片
        File templateImageFile = templateImageService.getTemplateFile(device, templateImageName, projectName);
        visionResult.getFileVisionResult().setTemplateImagePath(templateImageFile.getAbsolutePath());

        if (!visionResult.isPassed() && SystemEnv.isSaveFailImage()) {
            //保存失败的图片
            try {
                ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
                String timestamp = StrUtils.simpleDateFileName();
                //失败图片
                String failImageName = templateImageName + "_失败图片_" + timestamp;
                visionResult.getFileVisionResult().setCaptureImagePath(imageFileManager.getImageFileOfFail(templateImageName, failImageName).getAbsolutePath());
                String failImageLog = String.format("保存失败图片:%s", failImageName);
                log.debug(failImageLog);
                TestCaseLoggerUtils.getLogger().info(failImageLog);
                Frame displacementFrame = null;
                if (visionResult.getBestMatch() != null) {
                    //实际ROI图片
                    String roiImageName = templateImageName + "_ROI_" + timestamp;
                    String captureRoiPath = imageFileManager.getImageFileOfRoi(templateImageName, roiImageName).getAbsolutePath();
                    visionResult.getFileVisionResult().setCaptureRoiPath(captureRoiPath);
                    log.debug("保存ROI图片:{}", roiImageName);

                    try (Frame roiFrame = ImageUtils.crop(visionResult.getOriginalFrame(), visionResult.getBestMatch().toRect())) {
                        imageFileManager.saveImageFile(new File(captureRoiPath).getParentFile(), roiImageName, roiFrame);
                    }
                    if (distanceEnabled && pixelCoefficient > 0) {
                        RectangleDisplacement rd = new RectangleDisplacement();
                        displacementFrame = rd.calculateDisplacement(templateImageFile.getAbsolutePath(), captureRoiPath, pixelCoefficient);
                    }
                }
                if (displacementFrame != null) {
                    imageFileManager.saveFailImageFile(templateImageName, failImageName, displacementFrame, visionResult.isLargeImage());
                    displacementFrame.close();
                } else {
                    imageFileManager.saveFailImageFile(templateImageName, failImageName, visionResult.getEventFrame(), visionResult.isLargeImage());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        // 检查是否需要保存所有捕获的图像
        if (SystemEnv.isSaveALlCaptureImage() && !StrUtils.isEmpty(allImageStorageFolderName)) {
            log.info("记录所有图像识别的过程图片:{}", allImageStorageFolderName);
            double score = visionResult.getScore();

            // 创建主文件夹
            File baseFolder = new File(String.format("D:\\FlyTest\\data\\server\\system\\images\\%s", allImageStorageFolderName));
            if (!baseFolder.exists()) {
                baseFolder.mkdirs();
            }

            // 创建子文件夹（使用模板名称和分数）
            String subFolderName = String.format("%s@%s@%s", templateImageName, score, UUID.randomUUID());
            File subFolder = new File(baseFolder, subFolderName);
            if (!subFolder.exists()) {
                subFolder.mkdirs();
            }

            try {
                // 保存模板图片
                File templateDestFile = new File(subFolder, "template.jpg");
                FileUtils.copyFile(templateImageFile, templateDestFile);
                Frame liveFrame = visionResult.getOriginalFrame();
                if (liveFrame != null) {
                    try (Mat imgDisplayMat = Java2DFrameUtils.toMat(liveFrame)) {
                        rectangle(imgDisplayMat, visionResult.getRoiRect(), new Scalar(0, 0, 255, 0), 2, 0, 0);
                        try (Frame originalFrame = Java2DFrameUtils.toFrame(imgDisplayMat)) {
                            // 保存原图
                            if (originalFrame != null) {
                                File originalImageFile = new File(subFolder, "origin.jpg");
                                // 将Frame保存为图片文件
                                saveFrameToFile(originalFrame, originalImageFile);
                            }
                        }
                    }
                }

            } catch (IOException e) {
                log.error("保存图片失败", e);
            }
        }
        if (visionResult.getRoiRect() != null) {
            visionResult.getRoiRect().close();
        }

        if (visionResult.getEnlargeRoiRect() != null) {
            visionResult.getEnlargeRoiRect().close();
        }
        return visionResult;
    }

    /**
     * 处理模型识别图像结果
     *
     * @param visionResult
     * @param projectName
     * @param device
     * @return
     */
    public VisionResult handleModelVisionResult(VisionResult visionResult, String projectName, Device device) {
        String templateImageName = visionResult.getTemplateName();
        if (StringUtils.isEmpty(templateImageName)) {
            log.warn("图像模板名为空，无法保存失败图片");
            return visionResult;
        }

        if (!visionResult.isPassed() && SystemEnv.isSaveFailImage()) {
            //保存失败的图片
            try (Frame failFrame = visionResult.getEventFrame()) {
                ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
                String timestamp = StrUtils.simpleDateFileName();
                //失败图片
                String failImageName = templateImageName + "_失败图片_" + timestamp;
                visionResult.getFileVisionResult().setCaptureImagePath(imageFileManager.getImageFileOfFail(templateImageName, failImageName).getAbsolutePath());
                String failImageLog = String.format("保存失败图片:%s", failImageName);
                log.debug(failImageLog);
                TestCaseLoggerUtils.getLogger().info(failImageLog);

                if (visionResult.getBestMatch() != null) {
                    //实际ROI图片
                    String roiImageName = templateImageName + "_ROI_" + timestamp;
                    String captureRoiPath = imageFileManager.getImageFileOfRoi(templateImageName, roiImageName).getAbsolutePath();
                    visionResult.getFileVisionResult().setCaptureRoiPath(captureRoiPath);
                    log.debug("保存ROI图片:{}", roiImageName);

                    try (Frame roiFrame = ImageUtils.crop(visionResult.getOriginalFrame(), visionResult.getBestMatch().toRect())) {
                        imageFileManager.saveImageFile(new File(captureRoiPath).getParentFile(), roiImageName, roiFrame);
                    }
                }
                imageFileManager.saveFailImageFile(templateImageName, failImageName, failFrame, visionResult.isLargeImage());
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return visionResult;
    }

    // 保存Frame到文件的辅助方法
    private void saveFrameToFile(Frame frame, File file) throws IOException {
        BufferedImage image = Java2DFrameUtils.toBufferedImage(frame);
        ImageIO.write(image, "jpg", file);
    }

    private VisionResult getVisionResult(ImageOperationContext imageOperationContext) throws OperationFailNotification {
        //处理图像方法
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        ImageOperationMethod operationMethod = (ImageOperationMethod) imageOperationContext.getOperation().getOperationMethod();
        if (!imageOperationContext.isOnlyTestSimilarity()) {
            boolean mustExist;
            if (operationMethod.isEquals(ImageOperationMethod.MUST_APPEAR)
                    || operationMethod.isEquals(ImageOperationMethod.WAIT_APPEAR)
                    || operationMethod.isEquals(ImageOperationMethod.VISION_GUIDE_CLICK)
                    || operationMethod.isEquals(ImageOperationMethod.WAIT_CLICK)) {
                mustExist = true;
            } else if (operationMethod.isEquals(ImageOperationMethod.MUST_DISAPPEAR)
                    || operationMethod.isEquals(ImageOperationMethod.WAIT_DISAPPEAR)) {
                mustExist = false;
            } else {
                throw new OperationFailNotification(String.format("未知的图像请求方法:%s", operationMethod));
            }
            visionRecognizeRequest.setMustExist(mustExist);
        }
        final TemplateImageConfig templateImageConfig = imageOperationContext.getTemplateImageConfig();
        visionRecognizeRequest.setProject(imageOperationContext.getProjectName());
        visionRecognizeRequest.setTemplateName(templateImageConfig.getTemplateName());
        visionRecognizeRequest.setThreshold(templateImageConfig.getThreshold());
        visionRecognizeRequest.setAlgorithm(templateImageConfig.getAlgorithm());
        visionRecognizeRequest.setTimeout(templateImageConfig.getTimeout());
        visionRecognizeRequest.setMatchedText(templateImageConfig.getMatchedText());
        visionRecognizeRequest.setImageSize(templateImageConfig.getImageSize());
        visionRecognizeRequest.setRoi(templateImageConfig.getRoi());
        visionRecognizeRequest.setOnlyTestSimilarity(imageOperationContext.isOnlyTestSimilarity());
        visionRecognizeRequest.setColorMatchEnabled(templateImageConfig.isColorMatchEnabled());
        visionRecognizeRequest.setRecognizedDuration(templateImageConfig.getRecognizedDuration());
        return getVisionResult(imageOperationContext.getDevice(), visionRecognizeRequest);
    }

    private AbsoluteRoiRect getTemplateRoi(ImageOperationContext imageOperationContext) throws OperationFailNotification {
        String templateName = imageOperationContext.getTemplateImageConfig().getTemplateName();
        TemplatePicture templatePicture = templateImageService.getTemplate(templateName,
                imageOperationContext.getDevice().getDeviceUniqueCode(),
                imageOperationContext.getProjectName());
        log.info("获取模板图片:{}", templatePicture);
        if (templatePicture == null) {
            throw new OperationFailNotification(String.format("模板图片\"%s\"不存在，请重新截取", templateName));
        }
        //设置ROI
        PercentTemplateRoi percentTemplateRoi = templateRoiService.getPercentRoiByTemplateUUID(templatePicture.getTemplatePictureUUID());
        //TODO：考虑优化去除streamService.getPictureSize的操作，减少运行耗时
        TemplateRoi templateRelativeRoi = TemplateRoi.fromPercentTemplateRoi(percentTemplateRoi, streamService.getImageSize(imageOperationContext.getDevice()));
        log.info("图像模板相对ROI:{}", templateRelativeRoi);
        return templateRelativeRoi.toRoiRect();
    }

    public VisionResult getVisionResult(Device device, VisionRecognizeRequest visionRecognizeRequest) throws OperationFailNotification {
        //准备算法
        VisionAlgorithm visionAlgorithm = prepareVisionAlgorithm(visionRecognizeRequest);
        //准备ROI
//        prepareTemplateRoi(device, visionRecognizeRequest, visionAlgorithm);
        log.info("{}图像识别阈值:{}, 要求{}, 图像识别算法:{}{}{}",
                visionRecognizeRequest.getTemplateName() == null ? "" : String.format("模板图片:%s,", visionRecognizeRequest.getTemplateName()),
                visionAlgorithm.getThreshold(),
                visionAlgorithm.isMustExist() ? "必须出现" : "不允许出现",
                visionAlgorithm.getAlgorithmName(),
                visionAlgorithm.getRoiEnlargePercent() == null ? "" : String.format("，ROI放大比例:%s%%", visionAlgorithm.getRoiEnlargePercent()),
                visionAlgorithm.isColorMatchEnabled() ? "，颜色匹配" : "，灰度匹配"
        );
        if (visionAlgorithm.getTimeout() > 0) {
            log.info("图像识别超时设定为{}s", visionAlgorithm.getTimeout());
        }
        if (visionAlgorithm.getPreWaitTime() > 0) {
            log.info("图像识别前等待{}s", visionAlgorithm.getPreWaitTime());
            try {
                Thread.sleep((long) (visionAlgorithm.getPreWaitTime() * 1000));
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
        AtomicReference<Frame> staticLiveFrame = new AtomicReference<>();
        AtomicReference<Frame> templateFrame = new AtomicReference<>();
        if (visionRecognizeRequest.isOnlyTestSimilarity()) {
            //拍摄当前画面
            try (Frame frame = streamService.grab(device)) {
                staticLiveFrame.set(frame.clone());
            } catch (FrameGrabberException e) {
                throw new OperationFailNotification(e);
            }
            try (Frame frame = streamService.cropWithRoi(device, staticLiveFrame.get(), visionRecognizeRequest.getRoi())) {
                templateFrame.set(frame.clone());
            }
        }
        // 视频处理部分
        if (visionRecognizeRequest.isVideoEnabled()) {
            try {
                //录制视频
                long recordingTime = (long) visionRecognizeRequest.getRecognizedDuration();
                Queue<Frame> frameQueue = streamService.recordFrames(device.getDeviceUniqueCode(), recordingTime);
                visionAlgorithm.setFrameQueue(frameQueue);
                visionAlgorithm.setFrameRate(streamService.getFrameRate(device.getDeviceUniqueCode()));
                ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, visionRecognizeRequest.getProject());
                visionAlgorithm.setVideoFailFilePath(imageFileManager.getImageFileOfFailPath(visionRecognizeRequest.getTemplateName()));
                visionAlgorithm.setVideoFailROIFilePath(imageFileManager.getImageFileOfFailPath(visionRecognizeRequest.getTemplateName()));
            } catch (OperationFailNotification e) {
                log.error("视频处理错误: {}", e.getMessage());
            }
        }
        //进行图像识别
        VisionResult visionResult = algorithmInvoker.handleAlgorithm(visionAlgorithm,
                new VisionEventHandler() {
                    @Override
                    public Frame captureLiveFrame() throws FrameGrabberException {
                        if (visionRecognizeRequest.isOnlyTestSimilarity()) {
                            return staticLiveFrame.get();
                        } else {
                            Frame resultFrame = null;
                            try (Frame originalFrame = streamService.grab(device)) {
                                resultFrame = originalFrame.clone();
                                savePassImage(device, originalFrame, visionRecognizeRequest.getProject());
                                return resultFrame;
                            }
                        }
                    }

                    @Override
                    public Frame getTemplateFrame(String templateName) throws OperationFailNotification {
                        if (visionRecognizeRequest.isOnlyTestSimilarity()) {
                            return templateFrame.get();
                        } else {
                            try {
                                return templateImageService.getTemplateFrame(device, templateName, visionRecognizeRequest.getProject());
                            } catch (FileNotFoundException e) {
                                throw new OperationFailNotification(e);
                            }
                        }
                    }

                    @Override
                    public AbsoluteRoiRect getAbsoluteRoiRect(String templateName) {
                        AbsoluteRoiRect absoluteRoiRect = null;
                        if (!StrUtils.isEmpty(templateName)) {
                            //有模板图片
                            ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, visionRecognizeRequest.getProject());
                            TemplateRoi roi = imageFileManager.getRoi(templateName);
                            if (roi != null) {
                                //有模板roi json记录
                                absoluteRoiRect = roi.toRoiRect();
                            }
                        }

                        if (absoluteRoiRect == null) {
                            //没有模板图片
                            ScaledRoiRect roi = visionRecognizeRequest.getRoi();
                            if (roi != null) {
                                if (streamService.isStreamDevice(device)) {
                                    absoluteRoiRect = roi.toRect(streamService.getImageSize(device));
                                } else {
                                    absoluteRoiRect = roi.toRect(visionRecognizeRequest.getImageSize());
                                }
                            }
                        }
                        log.info("图像模板{}绝对ROI坐标:{}", templateName == null ? "" : templateName, absoluteRoiRect);
                        return absoluteRoiRect;
                    }
                });
        if (device.getDeviceModel().equals(DeviceModel.Camera.HIK_CAMERA)) {
            visionResult.setLargeImage(true);
        }
        return visionResult;
    }

    public void savePassImage(Device device, Frame frame, String projectName) {
        if (frame == null || !(device instanceof CameraDevice)) {
            return;
        }

        try (Frame tempFrame = frame.clone()) {
            CameraDevice cameraDevice = (CameraDevice) device;
            CameraConfig cameraConfig = cameraDevice.loadConfig(projectName);
            TakePhotoConfig takePhotoConfig = cameraConfig != null ? cameraConfig.getTakePhotoConfig() : null;

            if (takePhotoConfig != null && takePhotoConfig.isTakePhoto()) {
                String savePath = takePhotoConfig.getSavePath();
                String tableName = actionSequenceExecutionContextInfo.getTableName();
                String tcID = actionSequenceExecutionContextInfo.getTcId();

                if (savePath != null && tcID != null && tableName != null) {
                    ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
                    File baseDir = new File(savePath + "\\" + tableName, tcID);
                    if (!baseDir.exists()) {
                        baseDir.mkdirs();
                    }
                    String imageName = DateUtils.getNowForFile();
                    imageFileManager.saveImageFile(baseDir, imageName, tempFrame);
                }
            }
        } catch (IOException e) {
            log.error("保存图片失败: {}", e.getMessage());
        }
    }

    public VisionResult getModelVisionResult(Device device, VisionRecognizeRequest visionRecognizeRequest, String model) throws OperationFailNotification {
        log.info("开始使用模型在{}主题下，识别模板{}，相似度要求为：{}", model, visionRecognizeRequest.getTemplateName(), visionRecognizeRequest.getThreshold());
        final File outputDir = new File("screenshots");
        // 新增目录创建逻辑
        if (!outputDir.exists() && !outputDir.mkdirs()) {
            log.error("无法创建截图目录: {}", outputDir.getAbsolutePath());
            throw new OperationFailNotification("无法创建截图目录");
        }
        VisionResult visionResult = new VisionResult();
        visionResult.setTemplateName(visionRecognizeRequest.getTemplateName());
        Frame frame = null;
        FileOutputStream fos = null;
        BufferedImage bufferedImage = null;
        boolean isRecognize = false;
        boolean isSuccess = false;
        try {
            String cameraUUID = device.getDeviceUniqueCode();
            frame = streamService.grab(cameraUUID);
            visionResult.setOriginalFrame(frame);
            try (Java2DFrameConverter converter = new Java2DFrameConverter()) {
                bufferedImage = converter.convert(frame);
            }
            File outputFile = new File(outputDir, "screenshot.png");
            fos = new FileOutputStream(outputFile);
            // 获取图片的尺寸
            int width = bufferedImage.getWidth();  // 获取宽度
            int height = bufferedImage.getHeight(); // 获取高度
            // 确保父目录存在（再次检查）
            if (!outputFile.getParentFile().exists()) {
                outputFile.getParentFile().mkdirs();
            }
            ImageIO.write(bufferedImage, "PNG", outputFile);
            log.info("已保存图片到：{}", outputFile.getAbsolutePath());
            // 构建多部分请求体（同时包含文件和文本参数）
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    // 添加文件参数
                    .addFormDataPart(
                            "file",
                            outputFile.getName(),
                            RequestBody.create(outputFile, MediaType.get("image/png"))
                    )
                    // 添加模型参数（关键修改点）
                    .addFormDataPart("model_type", model)
                    .build();

            Request request = new Request.Builder()
                    .url(DETECT_URL) // 注意：如果接口需要URL参数，这里要相应修改
                    .header("User-Agent", "DetectionClient/2.0")
                    .post(requestBody)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                handleResponseStatus(response);
                DetectionResponse detectionResponse = parseResponse(response);
                if (detectionResponse != null) {
                    if (detectionResponse.getDetectionCount() > 0) {
                        List<Detection> matchedDetections = new ArrayList<>();
                        for (Detection detection : detectionResponse.getDetections()) {
                            if ((visionRecognizeRequest.getTemplateName()).equals(detection.getClassName())) {
                                matchedDetections.add(detection);
                            }
                        }
                        if (!matchedDetections.isEmpty()) {
                            Detection bestDetection = Collections.max(matchedDetections, Comparator.comparingDouble(Detection::getConfidence));
                            if (bestDetection.getConfidence() >= visionRecognizeRequest.getThreshold()) {
                                isSuccess = true;
                                isRecognize = true;
                                log.info("模板{}识别成功,相似度为{}", visionRecognizeRequest.getTemplateName(), bestDetection.getConfidence());
                                log.info("坐标为：({},{}),({},{})", bestDetection.getBbox()[0], bestDetection.getBbox()[1], bestDetection.getBbox()[2], bestDetection.getBbox()[3]);
                                // 提取坐标点
                                double startX = bestDetection.getBbox()[0] / width;
                                double startY = bestDetection.getBbox()[1] / height;
                                double endX = bestDetection.getBbox()[2] / width;
                                double endY = bestDetection.getBbox()[3] / height;
                                // 创建 ScaledPoint 对象
                                ScaledPoint pointStart = new ScaledPoint(startX, startY);
                                ScaledPoint pointEnd = new ScaledPoint(endX, endY);
                                visionResult.setBestMatch(new Rectangle((int) pointStart.getX(), (int) pointStart.getY(), (int) (pointEnd.getX() - pointStart.getX()), (int) (pointEnd.getY() - pointStart.getY())));
                                visionResult.setPassed(true);
                                visionResult.setMessage("模型识别模板成功");
                            } else {
                                isRecognize = true;
                                log.info("模板{}识别失败,相似度为{}", visionRecognizeRequest.getTemplateName(), bestDetection.getConfidence());
                            }
                            visionResult.setScore(bestDetection.getConfidence());
                            visionResult.setRoiRect(new AbsoluteRoiRect((int) bestDetection.getBbox()[0], (int) bestDetection.getBbox()[1], (int) bestDetection.getBbox()[2], (int) bestDetection.getBbox()[3]));
                        }
                    }
                }
                if (!isRecognize) {
                    log.info("未检测到模板{}", visionRecognizeRequest.getTemplateName());
                    visionResult.setScore(0.00);
                }
                if (!isSuccess) {
                    visionResult.setPassed(false);
                    visionResult.setMessage("模板检测失败");
                    visionResult.setEventFrame(frame);
                }
            }
        } catch (Exception e) {
            log.info("获取图片失败", e);
        } finally {
            try {
                if (frame != null) frame.close();
            } catch (Exception e) {
                log.warn("帧资源释放异常", e);
            }
            if (bufferedImage != null) {
                try {
                    bufferedImage.flush();
                } catch (Exception e) {
                    log.warn("图片资源释放异常", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.warn("文件流关闭异常", e);
                }
            }
        }
        return visionResult;
    }


    /**
     * 图像识别
     *
     * @param imageOperationContext 图像操作准备信息
     * @return 操作结果
     */
    public OperationResult imageRecognize(ImageOperationContext imageOperationContext) {
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        //获取识别结果
        VisionResult visionResult;
        try {
            //获取识别结果
            visionResult = getVisionResult(imageOperationContext);
            //处理最终结果
            visionResult = handleVisionResult(visionResult, imageOperationContext.getProjectName(), imageOperationContext.getDevice(), imageOperationContext.getTemplateImageConfig().isDistanceEnabled());
            //记录结果
            operationResult.setOk(visionResult.isPassed());
            operationResult.setMessage(visionResult.getMessage());
            //返回List<visionResult>
            operationResult.setData(visionResult.simpleResult());
            operationResult.setResult(visionResult.getPercentScore());
            //释放Frame内存
//            templateFrame.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            operationResult.setOk(false);
            operationResult.setMessage(ExceptionUtils.getExceptionString(e));
        }
        return operationResult;
    }

    private OperationResult robotClickChange(ImageOperationContext imageOperationContext) throws OperationFailNotification {
        OperationResult operationResult = robotClick(imageOperationContext);
        if (operationResult.isOk()) {
            return operationResult;
        } else {
            log.info("两次点击前后图片不一致");
            operationResult.setMessage("两次点击前后图片不一致");
            operationResult.setOk(false);
        }
        return operationResult;
    }

    private OperationResult robotClick(ImageOperationContext imageOperationContext) throws OperationFailNotification {
        OperationResult operationResult = new OperationResult();
        List<Device> deviceList = cameraDeviceFinder.findAllPhysicalCameras(false);
        if (!deviceList.isEmpty()) {
            Device cameraDevice = deviceList.get(0);
            log.info("视觉自动判断开始 使用相机:{}", cameraDevice.getDeviceName());
            operationResult = cameraDevice.openForOperationResult();
            if (operationResult.isFailed()) {
                return operationResult;
            }
            RobotImagePoint robotImagePoint = JSON.to(RobotImagePoint.class, imageOperationContext.getOperation().getOperationObject());
            try {
                //拍摄点击前画面
                Frame[] liveFrames = new Frame[2];
                Frame afterFrame = streamService.grab(cameraDevice).clone();
                final Frame copyFrame = afterFrame.clone();
                liveFrames[0] = copyFrame;
                Frame staticLiveFrame = streamService.grab(cameraDevice).clone();
                ImageUtils.printFrame(staticLiveFrame);
                log.info("准备执行机械臂点击操作{}", imageOperationContext.getOperation().getOperationTarget());
                Device device = deviceFinderManager.findDeviceByAliasName(robotName);
                if (device != null) {
                    operationResult.setOk(true);
                    DefaultDobotDevice defaultDobotDevice = (DefaultDobotDevice) operationResult.getData();
                    defaultDobotDevice.touch(robotImagePoint.getCoordinateName(), robotImagePoint.getProjectName());
                    log.info("触摸后拍照");
                    Frame beforeFrame = streamService.grab(cameraDevice).clone();
                    liveFrames[1] = beforeFrame;
                    VisionResult visionResult = compareFrames(liveFrames, robotImagePoint.getSimilarity(), robotImagePoint.getVisionAlgorithm());
                    if (visionResult.isPassed()) {
                        return operationResult;
                    } else {
                        String message = "第一次点击前后图片不一致";
                        log.info(message);
                        operationResult.fail(message);
                    }
                } else {
                    operationResult.fail("设备不存在");
                }
            } catch (FrameGrabberException e) {
                throw new OperationFailNotification(e);
            }
            return operationResult;
        }
        return operationResult;
    }

    /**
     * 测试相似度
     *
     * @param imageOperationContext 图像操作上下文
     * @return 操作结果
     */
    private OperationResult testSimilarity(ImageOperationContext imageOperationContext) throws OperationFailNotification {
        final Device device = imageOperationContext.getDevice();
        if (device.isSimulated()) {
            log.info("设备模拟中，相似度测试不启用");
            return OperationResult.staticOk();
        }
        imageOperationContext.setOnlyTestSimilarity(true);
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        final TemplateImageConfig templateImageConfig = imageOperationContext.getTemplateImageConfig();
        String visionAlgorithm = templateImageConfig.getAlgorithm();
        List<String> visionAlgorithmList = new ArrayList<>();
        if (visionAlgorithm.equals(AlgorithmSets.allAlgorithmMatching)) {
            //所有图像匹配方法均测试一遍
            visionAlgorithmList.addAll(AlgorithmSets.allAlgorithmSets);
        } else {
            //只测试单一图像匹配方法
            visionAlgorithmList.add(visionAlgorithm);
        }
        //运行图像识别
        Map<String, VisionResult> visionResultMap = new HashMap<>(); //TODO： 后续考虑转换成一个类来管理
        for (String algorithmName : visionAlgorithmList) {
            templateImageConfig.setAlgorithm(algorithmName);
            VisionResult visionResult = getVisionResult(imageOperationContext);
            visionResultMap.put(algorithmName, visionResult);
        }
        //处理识别结果
        boolean isOk = visionResultMap.values().stream().allMatch(VisionResult::isPassed);
        operationResult.setOk(isOk);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, VisionResult> entry : visionResultMap.entrySet()) {
            sb.append(String.format("%s:%s", entry.getKey(), entry.getValue().getMessage())).append("\n");
        }
        operationResult.setMessage(sb.toString());
        operationResult.setData(visionResultMap);
        return operationResult;
    }

    /**
     * 保存图片
     *
     * @param imageOperationContext 图像操作准备信息
     * @return 操作结果
     */
    private OperationResult saveImage(ImageOperationContext imageOperationContext) {
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        final Operation operation = imageOperationContext.getOperation();
        final Device device = imageOperationContext.getDevice();
        ImageSaveOptions imageSaveOptions = JSON.to(ImageSaveOptions.class, operation.getOperationObject());
        TemplateImageConfig templateImageConfig = imageOperationContext.getTemplateImageConfig();
        Frame frame;
        try {
            if (imageSaveOptions.isWholeSaved()) {
                frame = streamService.grab(device);
            } else {
                frame = streamService.grab(device, templateImageConfig.getRoi());
            }
            ImageUtils.writeFrame(frame, imageSaveOptions.getFilePath());
            operationResult.setOk(true);
        } catch (Exception e) {
            operationResult.setOk(false);
            operationResult.setMessage(ExceptionUtils.getExceptionString(e));
        }
        return operationResult;
    }

    private OperationResult setExposureAutoMode(ImageOperationContext imageOperationContext) {
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        final Device device = imageOperationContext.getDevice();
        if (device != null) {
            CameraSettings cameraSettings = JSON.to(CameraSettings.class, imageOperationContext.getOperation().getOperationObject());
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.setExposureAuto(cameraUUID, cameraSettings);
            log.info("设置{}相机自动曝光模式{}", device.getDeviceName(), cameraSettings.getAutoExposureMode());
        }
        operationResult.setOk(true);
        return operationResult;
    }

    private OperationResult getCameraParameters(ImageOperationContext imageOperationContext) {
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        final Device device = imageOperationContext.getDevice();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            CameraSettings cameraSettings = new CameraSettings();
            if (device.getDeviceModel().equals(DeviceModel.Camera.HIK_CAMERA)) {
                cameraSettings = streamService.getCameraSettings(cameraUUID);

            } else {
                cameraSettings.setFrameRate(Float.parseFloat(String.valueOf(streamService.getFrameRate(cameraUUID))));
            }
            operationResult.setData(cameraSettings);
            log.info("获取{}相机参数{}", device.getDeviceName(), cameraSettings);
        }
        operationResult.setOk(true);
        return operationResult;
    }


    private OperationResult setCameraParameters(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            CameraSettings cameraSettings = JSON.to(CameraSettings.class, operationPreparation.getOperation().getOperationObject());
            String cameraUUID = device.getDeviceUniqueCode();
            System.out.println(cameraSettings);
//            streamService.setCameraParameters(cameraUUID, cameraSettings);
//            log.info(String.format("设置%s相机参数%s", device.getDeviceName(), cameraSettings.getCameraParameters()));
        }
        operationResult.setOk(true);
        return operationResult;
    }

    public OperationResult setCameraFrameRate(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            int frameRate = (int) operationPreparation.getOperation().getOperationObject();
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.setFrameRate(cameraUUID, frameRate);
            log.info("设置{}相机帧率{}", device.getDeviceName(), frameRate);
        }
        return operationResult;
    }


    private OperationResult setReverseX(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            boolean reverseX = (boolean) operationPreparation.getOperation().getOperationObject();
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.setReverseX(cameraUUID, reverseX);
            log.info("设置{}相机水平翻转", device.getDeviceName());
        }
        return operationResult;
    }

    private OperationResult setReverseY(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            boolean reverseY = (boolean) operationPreparation.getOperation().getOperationObject();
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.setReverseY(cameraUUID, reverseY);
            log.info("设置{}相机垂直翻转", device.getDeviceName());
        }
        return operationResult;
    }

    private OperationResult startGrabbing(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.grabberSwitch(cameraUUID, true);
            log.info("{}相机开启采集", device.getDeviceName());
        }
        return operationResult;
    }

    public OperationResult cameraScreenShoot(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        // 1. 设备有效性校验
        if (device == null) {
            return createFailureResult(operationResult, "操作失败：未关联有效设备");
        }
        String projectName = operationPreparation.getProjectName();
        ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
        try (Frame frame = captureFrame(device)) {
            File screenShotFile;
            String tableName = actionSequenceExecutionContextInfo.getTableName();
            String tcID = actionSequenceExecutionContextInfo.getTcId();
            // 2. 加载配置并构建路径
            CameraConfig cameraConfig = device.loadConfig(projectName);
            TakePhotoConfig takePhotoConfig = cameraConfig != null ? cameraConfig.getTakePhotoConfig() : null;
            String savePath = takePhotoConfig != null ? takePhotoConfig.getSavePath() : null;
            if (savePath != null && tcID != null && tableName != null) {
                // 自定义路径+文件夹名保存
                File baseDir = new File(savePath + "\\" + tableName, tcID);
                if (!baseDir.exists() && !baseDir.mkdirs()) {
                    return createFailureResult(operationResult, "无法创建指定目录: " + baseDir.getAbsolutePath());
                }
                String imageName = DateUtils.getNowForFile();
                screenShotFile = imageFileManager.saveImageFile(baseDir, imageName, frame);
            } else {
                // 默认路径保存
                String imageName = DateUtils.getNowForFile();
                screenShotFile = imageFileManager.screenShotFile(imageName, frame, false);
            }

            // 3. 设置结果返回
            operationResult.setOk(true);
            operationResult.setData(screenShotFile.getAbsolutePath());
            operationResult.setMessage("截图保存至: " + screenShotFile.getPath());

        } catch (FrameGrabberException e) {
            log.error("摄像头画面获取失败", e);
            return createFailureResult(operationResult, "摄像头画面获取失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("截图过程中发生异常", e);
            return createFailureResult(operationResult, "截图异常: " + e.getMessage());
        }

        return operationResult;
    }

    private Frame captureFrame(CameraDevice device) throws FrameGrabberException {
        String cameraUUID = device.getDeviceUniqueCode();
        return streamService.grab(cameraUUID);
    }

    private OperationResult createFailureResult(OperationResult result, String message) {
        result.setOk(false);
        result.setMessage(message);
        return result;
    }

    private OperationResult stopGrabbing(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            streamService.grabberSwitch(cameraUUID, false);
            log.info("{}相机关闭采集", device.getDeviceName());
        }
        return operationResult;
    }

    private OperationResult setCameraCalibration(ImageOperationContext operationPreparation) throws OperationFailNotification {
        pixelCoefficient = 0;
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final Device device = operationPreparation.getDevice();
        if (device != null) {
            CameraSettings cameraSettings = JSON.to(CameraSettings.class, operationPreparation.getOperation().getOperationObject());
            Frame frame;
            try {
                frame = streamService.grab(device);
            } catch (FrameGrabberException e) {
                throw new OperationFailNotification(e);
            }
            RectangleDisplacement rd = new RectangleDisplacement();
//            System.out.println(cameraCalibration.getDimensions());
            double ratio = rd.calibrateFromFrame(frame, 5);
            operationResult.setMessage(String.valueOf(ratio));
            pixelCoefficient = ratio;
        }
        operationResult.setOk(true);
        return operationResult;
    }

    private void updateCameraConfig(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        CameraConfig cameraConfig = JSON.to(CameraConfig.class, operationPreparation.getOperation().getOperationObject());
        device.updateConfig(cameraConfig);
        operationResult.setOk(true);
    }

    private void loadConfig(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        CameraConfig cameraConfig = device.loadConfig(operationPreparation.getProjectName());
        if (cameraConfig == null) {
            cameraConfig = new CameraConfig();
            cameraConfig.setProject(operationPreparation.getProjectName());
            device.updateConfig(cameraConfig);
        }
        operationResult.setData(cameraConfig);
        operationResult.setOk(true);
    }

    private void failVideo(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        String projectName = operationPreparation.getProjectName();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            CameraConfig cameraConfig = device.loadConfig(projectName);
            if (cameraConfig != null) {
                FailVideoConfig failVideoConfig = cameraConfig.getFailVideoConfig();
                if (failVideoConfig.isFailVideo()) {
                    Queue<Frame> frameQueue;
                    try {
                        frameQueue = streamService.recordFrames(cameraUUID, failVideoConfig.getFailVideoTime() * 1000L);
                    } catch (OperationFailNotification e) {
                        operationResult.fail(e.getMessage());
                        return;
                    }
                    try {
                        streamService.saveFramesToVideo(frameQueue, failVideoConfig.getFailVideoResolution(), projectName, failVideoConfig.getFailVideoSavePath());
                        operationResult.setContext(failVideoConfig.getFailVideoSavePath());
                    } catch (Exception e) {
                        operationResult.fail(e.getMessage());
                        return;

                    }
                }
            }
        }
        operationResult.setOk(true);
    }

    private void startBacktrackVideo(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        String projectName = operationPreparation.getProjectName();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            CameraConfig cameraConfig = device.loadConfig(projectName);
            if (cameraConfig != null) {
                BackTrackConfig backtrackVideoConfig = cameraConfig.getBackTrackConfig();
                if (backtrackVideoConfig.isBacktrack()) {
                    try {
                        streamService.startRecording(cameraUUID, backtrackVideoConfig.getBacktrackTime(), backtrackVideoConfig.getBackResolution());
                    } catch (OperationFailNotification e) {
                        operationResult.fail(e.getMessage());
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        operationResult.setOk(true);
    }

    private void stopBacktrackVideo(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();
        String projectName = operationPreparation.getProjectName();
        if (device != null) {
            String cameraUUID = device.getDeviceUniqueCode();
            CameraConfig cameraConfig = device.loadConfig(projectName);
            if (cameraConfig != null) {
                BackTrackConfig backtrackVideoConfig = cameraConfig.getBackTrackConfig();
                if (backtrackVideoConfig.isBacktrack()) {
                    Queue<Frame> frameQueue = null;
                    try {
                        frameQueue = streamService.stopRecording(cameraUUID);
                    } catch (OperationFailNotification e) {
                        throw new RuntimeException(e);
                    }
                    try {
                        streamService.saveFramesToVideo(frameQueue, backtrackVideoConfig.getBackResolution(), projectName, backtrackVideoConfig.getBackSavePath());
                        operationResult.setContext(backtrackVideoConfig.getBackSavePath());
                    } catch (Exception e) {
                        operationResult.fail(e.getMessage());
                        return;
                    }

                }
            }
        }
        operationResult.setOk(true);
    }

    public OperationResult startRecording(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();

        // 1. 设备有效性校验
        if (device == null) {
            return createFailureResult(operationResult, "操作失败：未关联有效设备");
        }
        String tableName = actionSequenceExecutionContextInfo.getTableName();
        String tcID = actionSequenceExecutionContextInfo.getTcId();
        String projectName = operationPreparation.getProjectName();
        CameraConfig cameraConfig = device.loadConfig(projectName);
        TakePhotoConfig takePhotoConfig = cameraConfig != null ? cameraConfig.getTakePhotoConfig() : null;
        String savePath = takePhotoConfig != null ? takePhotoConfig.getSavePath() : null;
        if (savePath != null && tcID != null && tableName != null) {
            // 自定义路径+文件夹名保存
            File baseDir = new File(savePath + "\\" + tableName, tcID);
            if (!baseDir.exists() && !baseDir.mkdirs()) {
                return createFailureResult(operationResult, "无法创建指定目录: " + baseDir.getAbsolutePath());
            }
            try {
                streamService.startVideoRecording(device.getDeviceUniqueCode());
            } catch (OperationFailNotification e) {
                log.error("开始视频录制失败", e);
                return createFailureResult(operationResult, "视频录制失败: " + e.getMessage());
            }
        }
        operationResult.setOk(true);
        operationResult.setMessage("开始录制视频");
        return operationResult;
    }

    public OperationResult stopRecording(ImageOperationContext operationPreparation) {
        final OperationResult operationResult = operationPreparation.getOperationResult();
        final CameraDevice device = (CameraDevice) operationPreparation.getDevice();

        // 1. 设备有效性校验
        if (device == null) {
            return createFailureResult(operationResult, "操作失败：未关联有效设备");
        }
        String tableName = actionSequenceExecutionContextInfo.getTableName();
        String tcID = actionSequenceExecutionContextInfo.getTcId();
        String projectName = operationPreparation.getProjectName();
        CameraConfig cameraConfig = device.loadConfig(projectName);
        TakePhotoConfig takePhotoConfig = cameraConfig != null ? cameraConfig.getTakePhotoConfig() : null;
        String savePath = takePhotoConfig != null ? takePhotoConfig.getSavePath() : null;
        if (savePath != null && tcID != null && tableName != null) {
            File baseDir = new File(savePath + "\\" + tableName, tcID);
            if (!baseDir.exists() && !baseDir.mkdirs()) {
                return createFailureResult(operationResult, "无法创建指定目录: " + baseDir.getAbsolutePath());
            }
            try {
                streamService.stopVideoRecordingAndSave(device.getDeviceUniqueCode(), baseDir.getAbsolutePath());
            } catch (Exception e) {
                log.error("视频录制失败", e);
                return createFailureResult(operationResult, "视频录制失败: " + e.getMessage());
            }
        }
        operationResult.setOk(true);
        operationResult.setMessage("录制视频成功保存");
        return operationResult;
    }


    /**
     * 执行图像指令
     */
    private void initializeOperationMethodMap() {
        operationMethodMap.put(ImageOperationMethod.TEST_SIMILARITY, this::testSimilarity);
        operationMethodMap.put(ImageOperationMethod.SAVE_IMAGE, this::saveImage);
        operationMethodMap.put(ImageOperationMethod.CHECK_CHANGE, this::robotClickChange);
        operationMethodMap.put(ImageOperationMethod.ENABLE_BINARIZATION, op -> imageAlgorithmConfig.setEnableBinarization(true));
        operationMethodMap.put(ImageOperationMethod.SET_EXPOSURE_AUTO_MODE, this::setExposureAutoMode);
        operationMethodMap.put(ImageOperationMethod.SET_CAMERA_FRAME_RATE, this::setCameraFrameRate);
        operationMethodMap.put(ImageOperationMethod.SET_REVERSE_X, this::setReverseX);
        operationMethodMap.put(ImageOperationMethod.SET_REVERSE_Y, this::setReverseY);
        operationMethodMap.put(ImageOperationMethod.START_GRABBING, this::startGrabbing);
        operationMethodMap.put(ImageOperationMethod.STOP_GRABBING, this::stopGrabbing);
        operationMethodMap.put(ImageOperationMethod.SET_CAMERA_PARAMETERS, this::setCameraParameters);
        operationMethodMap.put(ImageOperationMethod.SET_CAMERA_CALIBRATION, this::setCameraCalibration);
        operationMethodMap.put(ImageOperationMethod.GET_CAMERA_PARAMETERS, this::getCameraParameters);
        operationMethodMap.put(ImageOperationMethod.UPDATE_CONFIG, this::updateCameraConfig);
        operationMethodMap.put(ImageOperationMethod.LOAD_CONFIG, this::loadConfig);
        operationMethodMap.put(ImageOperationMethod.FAIL_VIDEO, this::failVideo);
        operationMethodMap.put(ImageOperationMethod.CAMERA_SCREEN_SHOOT, this::cameraScreenShoot);
        operationMethodMap.put(ImageOperationMethod.START_BACKTRACK_VIDEO, this::startBacktrackVideo);
        operationMethodMap.put(ImageOperationMethod.STOP_BACKTRACK_VIDEO, this::stopBacktrackVideo);
    }


    private void executeCommand(ImageOperationContext imageOperationContext) throws UnSupportedOperationMethod, OperationFailNotification, IOException {
        final Device device = imageOperationContext.getDevice();
        final Operation operation = imageOperationContext.getOperation();
        ImageOperationMethod operationMethod = (ImageOperationMethod) operation.getOperationMethod();
        if (device != null) {
            OperationMethodConsumer<ImageOperationContext> method = operationMethodMap.get(operationMethod);
            if (method != null) {
                method.accept(imageOperationContext);
            } else {
                OperationResult operationResult = imageRecognize(imageOperationContext);
                if (operationResult.isOk()) {
                    SimpleVisionResult simpleVisionResult = (SimpleVisionResult) operationResult.getData();
                    if (operationMethod.isEquals(ImageOperationMethod.WAIT_CLICK)) {
                        //处理点击事件，点击并识别
                        PointInt center = simpleVisionResult.getCenterPoint();
                        ((AndroidDevice) imageOperationContext.getDevice()).click(center);
                    } else if (operationMethod.isEquals(ImageOperationMethod.VISION_GUIDE_CLICK)) {
                        //视觉引导点击
                        OperationResult opResult = robotVisionGuideClick(simpleVisionResult.getCenterPoint(), imageOperationContext);
                        operationResult.setOk(opResult.isOk());
                        operationResult.setMessage(opResult.getMessage());
                        operationResult.setData(opResult.getData());
                    }
                }
            }
        }
    }

    @Override
    public OperationResult execute(ExecutionContext executionContext, Operation operation) {
        log.info("执行图像操作:{}", operation);
        ImageOperationContext imageOperationContext = prepareContext(executionContext, operation);
        final OperationResult operationResult = imageOperationContext.getOperationResult();
        if (operationResult.isOk()) {
            try {
                executeCommand(imageOperationContext);
            } catch (UnSupportedOperationMethod e) {
                return operationResult;
            } catch (OperationFailNotification e) {
                return e.getOperationResult();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return operationResult;
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        imageAlgorithmConfig.setEnableBinarization(false);
        // 为每次测试生成一个当前日期的文件夹名
        allImageStorageFolderName = DateUtils.getNowForFile();
    }

    private void handleResponseStatus(Response response) throws IOException {
        if (!response.isSuccessful()) {
            String errorBody = response.body() != null ?
                    response.body().string() : "无错误详情";
            throw new IOException(String.format("检测请求失败 [%d %s]: %s",
                    response.code(),
                    response.message(),
                    errorBody));
        }
    }

    private DetectionResponse parseResponse(Response response) throws IOException {
        try {
            if (response.body() != null) {
                return objectMapper.readValue(response.body().byteStream(), DetectionResponse.class);
            }
        } catch (Exception e) {
            throw new IOException("响应解析失败: " + e.getMessage(), e);
        }
        return null;
    }

}