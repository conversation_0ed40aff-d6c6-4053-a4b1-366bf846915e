package com.desaysv.workserver.devices;

import com.desaysv.workserver.config.DeviceConfig;

/**
 * 设备配置接口
 *
 * @param <T> 泛型接口（解决子类形参多态问题）
 */
public interface DeviceConfigurable<T extends DeviceConfig> {


    /**
     * 更新配置
     *
     * @param deviceConfig 设备配置
     */
    void updateConfig(T deviceConfig);

    /**
     * 导入配置
     *
     * @param projectName 项目名
     * @return 设备配置
     */
    T loadConfig(String projectName);

    void updateConfigByKey(DeviceConfig deviceConfig);

    String loadConfigByKey(DeviceConfig deviceConfig);

}
