package com.desaysv.workserver.devices.testbox;

import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.interfaces.ITestBoardBox;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.serial.SerialPortWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 10:50
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Slf4j
public class TestBox extends Device implements ITestBoardBox {
    private static final int dataBits = 8;
    private static final int stopBits = 2;
    private static final int parity = 0;
    private static final int flowControlIn = 0;
    private static final int flowControlOut = 0;
    private ModbusMaster master;
    private final OperateRegister operateRegister;

    public TestBox() {
        this(new DeviceOperationParameter());
    }

    public TestBox(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        operateRegister = new OperateRegister();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TEST_BOX;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.TestBox.TEST_BOX;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String commPortId = "COM" + getDevicePort();
        int baudRate = getBaudRate();
        log.info("open testBox Port: {}, baudRate:{}", commPortId, getBaudRate());
        SerialPortWrapper serialPortWrapper = new SerialPortWrapperImpl(
                commPortId, baudRate, dataBits, stopBits, parity, flowControlIn, flowControlOut);
        ModbusFactory modbusFactory = new ModbusFactory();
        master = modbusFactory.createRtuMaster(serialPortWrapper);
        try {
            master.init();
            master.setConnected(true);
            log.info("测试箱连接成功，master:{}", master.hashCode());
        } catch (ModbusInitException e) {
            throw new DeviceOpenException(e);
        }
//        return master.isConnected();
        return true;

    }

    @Override
    public boolean close() throws DeviceCloseException {
        if (master != null) {
            master.destroy();
            log.info("close ModbusMaster");
        }
        return true;
    }

    @Override
    public List<Integer> fetchResistanceBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            return new ArrayList<>();
        }
        log.info("fetchResistanceBoardCard all channel");
        return operateRegister.readResistanceBoardCardData(master);
    }

    @Override
    public Map<String, Boolean> fetchRelayBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            return new HashMap<>();
        }
        log.info("fetchRelayBoardCard all channel");
        return operateRegister.readRelayBoardCardData(master);
    }

    @Override
    public AcquisitionEntity fetchVoltageBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            return new AcquisitionEntity();
        }
        log.info("fetchVoltageBoardCard all channel");
        return operateRegister.readAllAcquisitionBoardCard(master);
    }

    @Override
    public float fetchVoltageByChannel(int boardCardNumber, int deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return (float) RandomUtil.randomDouble(1, 10);
        }
        log.info("fetchVoltageByChannel boardCardNumber:{}, channel:{}", boardCardNumber, deviceChannel);
        return operateRegister.readAcquisitionBoardCardByChannel(master, boardCardNumber, deviceChannel);
    }

    @Override
    public Map<String, PWMEntity> fetchPWMOutputBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            Map<String, PWMEntity> simulatedmap = new HashMap<>();
            for (int ch = 1; ch < 10; ch++) {
                simulatedmap.put(String.valueOf(ch), new PWMEntity((float) RandomUtil.randomDouble(1, 10), (float) RandomUtil.randomDouble(1, 10)));
            }
            return simulatedmap;
        }
        log.info("fetchPWMOutputBoardCard all channel");
        return operateRegister.readPWMOutputBoardCardData(master);
    }

    @Override
    public Map<String, PWMEntity> fetchPWMInputBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            return new HashMap<>();
        }
        log.info("fetchPWMInputBoardCard all channel");
        return operateRegister.readPWMInputBoardCardData(master);
    }

    @Override
    public PWMEntity fetchPWMInputBoardCardByChannel(int deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return new PWMEntity((float) RandomUtil.randomDouble(1, 10), (float) RandomUtil.randomDouble(1, 10));
        }
        log.info("fetchPWMInputBoardCardByChannel channel:{}", deviceChannel);
        return operateRegister.readPWMInputBoardCardDataByChannel(master, deviceChannel);
    }


    @Override
    public List<Integer> fetchTriStateOutputBoardCard() throws BoardCardTransportException {
        if (isSimulated()) {
            return new ArrayList<>();
        }
        log.info("fetchPWMInputBoardCardByChannel all channel");
        return operateRegister.readTriStateOutputBoardCardData(master);
    }

    @Override
    public boolean writeResistanceBoardCard(Integer deviceChannel, int resistance) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        log.info("写入电阻板卡 通道:{}, 电阻值:{}", deviceChannel, resistance);
        return operateRegister.writeResistanceBoardCardData(master, deviceChannel, resistance);
    }

//    @Override
//    public boolean writeResistanceBoardCard(Integer slotNumber, Integer deviceChannel, int resistance) throws BoardCardTransportException {
//        return false;
//    }


    @Override
    public boolean writeRelaySwitchBoardCard(Integer deviceChannel, int value) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        log.info("写入继电器板卡 通道:{}, 值:{}", deviceChannel, value);
        return operateRegister.writeRelayBoardCardData(master, deviceChannel, value);
    }

    @Override
    public boolean writeRelaySwitchAll(int value) throws BoardCardTransportException {
        return false;
    }

    @Override
    public boolean writeTriStateOutputBoardCard(Integer deviceChannel, int state) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        log.info("写入三态输出板卡 通道:{}, 值:{}", deviceChannel, state);
        return operateRegister.writeTriStateOutputBoardCardData(master, deviceChannel, state);
    }

    @Override
    public boolean writePWMOutputBoardCard(Integer deviceChannel, PWMEntity pwmEntity) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        log.info("写入PWM输出板卡 通道:{}, 值:{}", deviceChannel, pwmEntity);
        return operateRegister.writePWMOutputBoardCardData(master, deviceChannel, pwmEntity);
    }

    @Override
    public int fetchTriStateOutputByChannel(int deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return -1;
        }
        log.info("fetchTriStateOutputByChannel channel:{}", deviceChannel);
        return operateRegister.readTriStateOutputBoardCardByChannel(master, deviceChannel);
    }


    @Override
    public PWMEntity fetchPWMOutputBoardCardByChannel(int deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return new PWMEntity((float) RandomUtil.randomDouble(1, 10), (float) RandomUtil.randomDouble(1, 10));
        }
        log.info("fetchPWMOutputBoardCardByChannel channel:{}", deviceChannel);
        return operateRegister.readPWMOutputBoardCardByChannel(master, deviceChannel);
    }

    public Integer fetchResistorByChannel(Integer deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return -1;
        }
        log.info("fetchResistorByChannel channel:{}", deviceChannel);
        return operateRegister.readResistanceBoardCardByChannel(master, deviceChannel);
    }

    @Override
    public Integer fetchRelayBoardCardByChannel(Integer deviceChannel) throws BoardCardTransportException {
        if (isSimulated()) {
            return -1;
        }
        log.info("fetchRelayBoardCardByChannel channel:{}", deviceChannel);
        return operateRegister.readRelayBoardCardByChannel(master, deviceChannel);
    }

}
