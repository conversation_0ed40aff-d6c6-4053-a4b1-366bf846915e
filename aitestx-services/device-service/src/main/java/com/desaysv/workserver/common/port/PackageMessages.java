package com.desaysv.workserver.common.port;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/1/11 16:05
 */

@Data
public class PackageMessages {
    private int interval;
    private List<MessageText> messageTexts;

    public PackageMessages(int interval, List<MessageText> messageTexts) {
        this.interval = interval;
        this.messageTexts = messageTexts;
    }

}
