package com.desaysv.workserver.devices.bus.base;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TaskIdGenerator {

    private final static Map<String, ChannelMessageId> taskIdMap = new ConcurrentHashMap<>();

    public static ChannelMessageId generateTaskId(int channel, int arbitrationId) {
        String key = generateKey(channel, arbitrationId);
        if (taskIdMap.containsKey(key)) {
            // 如果已经存在相同的任务ID，则返回已存在的ID
            return taskIdMap.get(key);
        }
        // 如果不存在相同的任务ID，则生成一个新的ID并保存到缓存中
        ChannelMessageId channelMessageId = new ChannelMessageId(channel, arbitrationId);
        taskIdMap.put(key, channelMessageId);
        return channelMessageId;
    }

    private static String generateKey(int channel, int arbitrationId) {
        // 根据参数生成唯一的缓存键
        return channel + "-" + arbitrationId; // 这里简单地将参数拼接成一个字符串作为键，实际应根据业务逻辑生成唯一键
    }

    /**
     * flexray用
     */
    public static ChannelMessageId generateTaskId(int channel, int soltId,int base,int rep) {
        String key = generateKey(channel,soltId,base,rep);
        if (taskIdMap.containsKey(key)) {
            // 如果已经存在相同的任务ID，则返回已存在的ID
            return taskIdMap.get(key);
        }
        // 如果不存在相同的任务ID，则生成一个新的ID并保存到缓存中
        ChannelMessageId channelMessageId = new ChannelMessageId(channel, soltId, base, rep);
        taskIdMap.put(key, channelMessageId);
        return channelMessageId;
    }

    private static String generateKey(int channel, int soltId,int base,int rep) {
        // 根据参数生成唯一的缓存键
        return channel + "-" + soltId+ "-" +base+ "-" +rep; // 这里简单地将参数拼接成一个字符串作为键，实际应根据业务逻辑生成唯一键
    }



    /**
     * Lin用
     */
    public static ChannelMessageId generateTaskId(int channel, String idHex) {
        String key = generateKey(channel,idHex);
        if (taskIdMap.containsKey(key)) {
            // 如果已经存在相同的任务ID，则返回已存在的ID
            return taskIdMap.get(key);
        }
        // 如果不存在相同的任务ID，则生成一个新的ID并保存到缓存中
        ChannelMessageId channelMessageId = new ChannelMessageId(channel, idHex);
        taskIdMap.put(key, channelMessageId);
        return channelMessageId;
    }

    private static String generateKey(int channel, String idHex) {
        // 根据参数生成唯一的缓存键
        return channel + "-" + idHex; // 这里简单地将参数拼接成一个字符串作为键，实际应根据业务逻辑生成唯一键
    }

}
