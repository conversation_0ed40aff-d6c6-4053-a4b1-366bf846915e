package com.desaysv.workserver.manager;

import com.desaysv.workserver.common.annotation.DeviceMock;
import com.desaysv.workserver.common.annotation.OperationTargetAliasMap;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.I2C.I2CDevice;
import com.desaysv.workserver.devices.android.AdbHudDevice;
import com.desaysv.workserver.devices.android.UsbAndroid;
import com.desaysv.workserver.devices.autoclicker.AutoClickerDevice;
import com.desaysv.workserver.devices.camera.hik.HikCamera;
import com.desaysv.workserver.devices.camera.usb.USBCamera;
import com.desaysv.workserver.devices.daq.art_daq.USB3200N;
import com.desaysv.workserver.devices.daq.keysight.KeySight34461A;
import com.desaysv.workserver.devices.daq.ut8806.Ut8806;
import com.desaysv.workserver.devices.dc_collector.ZH4424DcCollector;
import com.desaysv.workserver.devices.electric_relay.*;
import com.desaysv.workserver.devices.electronic_load.N68000ElectronicLoad;
import com.desaysv.workserver.devices.oscilloscope.RigolDhoOscilloscope;
import com.desaysv.workserver.devices.oscilloscope.SDS5000XOscilloscope;
import com.desaysv.workserver.devices.oscilloscope.SDS6000XOscilloscope;
import com.desaysv.workserver.devices.power.*;
import com.desaysv.workserver.devices.power.kikusui.Kikusui;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.resistance.RemoteResistanceDevice;
import com.desaysv.workserver.devices.resistance.ResistanceDevice;
import com.desaysv.workserver.devices.resistance.ResistanceQRDevice;
import com.desaysv.workserver.devices.robot.DobotMG400;
import com.desaysv.workserver.devices.robot.DobotMagician;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.signal_generator.TKSignalGenerator;
import com.desaysv.workserver.devices.soundcard.AuxInSoundDevice;
import com.desaysv.workserver.devices.soundcard.USB4704;
import com.desaysv.workserver.devices.speaker.SpeakerDevice;
import com.desaysv.workserver.devices.tcpserver.TcpServerDevice;
import com.desaysv.workserver.devices.testbox.RZCUTestBox;
import com.desaysv.workserver.devices.testbox.TestBox;
import com.desaysv.workserver.devices.usbplug.UsbPlugDevice;
import com.desaysv.workserver.devices.usbswtich.UsbSwitchDevice;
import com.desaysv.workserver.devices.videocapture.VideoCaptureDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.finder.SerialPortDeviceFinder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 设备管理
 */
@Component
public class DeviceManager {
    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    @Autowired
    private DeviceFinderManager deviceFinderManager;

    /**
     * 获取所有设备
     *
     * @return 所有设备
     */
    public List<Device> getAllDevices() {
        return deviceRegisterManager.getAllDevices();
    }

    /**
     * 根据设备名获取设备
     *
     * @param deviceName 设备名
     * @return 设备
     */
    public Device getDevice(String deviceName) {
        return deviceRegisterManager.getDevice(deviceName);
    }

    /**
     * 获取所有相机列表
     *
     * @return 所有相机列表
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Camera.USB_CAMERA, targetClazz = USBCamera.class),
//                    @OperationTargetAliasMap(deviceModel = DeviceModel.Camera.BASLER_CAMERA, targetClazz = BaslerCamera.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Camera.HIK_CAMERA, targetClazz = HikCamera.class)
            },
            enable = true)
    public List<Device> getAllPhysicalCameras(String deviceModel) {
        return deviceFinderManager.getCameraDeviceFinder().findAllPhysicalCameras(deviceModel, true);
    }

    /**
     * 获取所有的有线连接Android设备列表
     *
     * @return 所有的有线连接Android设备列表
     */
    public List<Device> getAllPhysicalAndroids(String deviceModel) {
        return deviceFinderManager.getAndroidDeviceFinder().findPhysicalAndroids(deviceModel, true);
    }

    /**
     * 获取所有的Android设备列表
     *
     * @return 所有的Android设备列表
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Android.USB_ANDROID, targetClazz = UsbAndroid.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Android.ADB_HUD, targetClazz = AdbHudDevice.class)
            })
    public List<Device> getAllAndroids(String deviceModel) {
        return getAllPhysicalAndroids(deviceModel);
    }

    public List<String> getAllQnxDevices() {
        return Arrays.asList("rtmp://127.0.0.1/live?live=1");
    }

    /**
     * 获取所有网络通讯机械臂
     *
     * @return 所有网络通讯机械臂
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Robot.DOBOT_MG400, targetClazz = DobotMG400.class)
            })
    public List<Device> getAllIpRobots(String deviceModel) {
        return deviceFinderManager.getRobotDeviceFinder().findAllIpRobots(deviceModel, true);
    }

    /**
     * 获取所有串口设备
     *
     * @param deviceModel 设备型号
     * @return 所有串口设备
     * @throws IllegalAccessException
     */
//    @Cacheable(value = {"physicalSerialPort"}, key = "#deviceModel")
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.TestBox.TEST_BOX, targetClazz = TestBox.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.TestBox.RZCU_TEST_BOX, targetClazz = RZCUTestBox.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Robot.DOBOT_MAGICIAN, targetClazz = DobotMagician.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.UsbPlug.USB_PLUG, targetClazz = UsbPlugDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.UsbSwitch.USB_SWITCH, targetClazz = UsbSwitchDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectricRelay.JYDAM1600C, targetClazz = JYDAM1600CDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectricRelay.ZQWL_IO_1BXRC32, targetClazz = ZQWLIO1BXRC32Device.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectricRelay.ZQWL_IO_1CX5R4, targetClazz = ZQWLIO1CX5R4Device.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectricRelay.ZQWL_IO_1BX3C8, targetClazz = ZQWLIO1BX3C8Device.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectricRelay.ZQWL_IO_1CNRR4, targetClazz = ZQWLIO1CNRR4Device.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Serial.PORT_SERIAL, targetClazz = SerialPortDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT68xx, targetClazz = IT68xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT69xx, targetClazz = IT69xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT6322, targetClazz = IT6322.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT63xx, targetClazz = IT63xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT65xx, targetClazz = IT65xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT67xx, targetClazz = IT67xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.POWER_BOX, targetClazz = PowerControlBox.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.MPS3610H, targetClazz = MPS3610H.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Resistance.QR10X, targetClazz = ResistanceQRDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Resistance.INSTRUMENT_RESISTANCE, targetClazz = ResistanceDevice.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.ElectronicLoad.N68000, targetClazz = N68000ElectronicLoad.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.TestBox.RZCU_TEST_BOX, targetClazz = RZCUTestBox.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.DcCollector.DC_COLLECTOR_24_DEVICE, targetClazz = ZH4424DcCollector.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.PVD8V50E, targetClazz = PVD8V50E.class)
            },
            enable = true)
    public List<Device> getAllPhysicalSerialPorts(String deviceModel) throws IllegalAccessException {
        return SerialPortDeviceFinder.findPhysicalSerialPorts(deviceModel, true);
    }


//    /**

    /**
     * 获取所有VISA设备
     *
     * @param deviceModel 设备型号
     * @return 所有VISA设备
     */
    @DeviceMock(numbers = 1,
            commProtocol = CommProtocol.USB,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Daq.KEYSIGHT_34461A, targetClazz = KeySight34461A.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Daq.UT8806, targetClazz = Ut8806.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.KIKUSUI, targetClazz = Kikusui.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.N6700, targetClazz = N6700.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT69xx, targetClazz = IT69xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT65xx, targetClazz = IT65xx.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.IT67xx, targetClazz = IT67xx.class),
//                    @OperationTargetAliasMap(deviceModel = DeviceModel.Power.PVD8V50E, targetClazz = PVD8V50E.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Oscilloscope.SDS_5000X, targetClazz = SDS5000XOscilloscope.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Oscilloscope.SDS_6000X, targetClazz = SDS6000XOscilloscope.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Oscilloscope.RIGOL_800, targetClazz = RigolDhoOscilloscope.class),
                    @OperationTargetAliasMap(deviceModel = DeviceModel.SignalGenerator.TK_AFG_1022, targetClazz = TKSignalGenerator.class)
            },
            enable = true)
    //FIXME: 无效
    public List<Device> getAllVisaInstrumentPorts(String deviceModel) {
        return deviceFinderManager.getVisaInstrumentDeviceFinder().findPhysicalVisaInstruments(deviceModel, true);
    }
//     * 获取所有Can设备
//     *
//     * @param deviceModel   设备型号
//     * @param deviceChannel 设备通道
//     * @return 所有Can设备
//     */
//    public List<Device> getAllCanPorts(String deviceModel, Integer deviceChannel) {
//        return deviceFinderManager.getCanDeviceFinder().findAllCanDevice(deviceModel, deviceChannel, true);
//    }

    /**
     * 获取所有AutoClicker设备
     *
     * @param deviceModel 设备型号
     * @return 所有AutoClicker设备
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.AutoClicker.AUTO_CLICKER, targetClazz = AutoClickerDevice.class),
            },
            enable = true)
    public List<Device> getAllAutoClicker(String deviceModel) {
        return deviceFinderManager.getAutoClickerDeviceFinder().findAllAutoClickerDevice(deviceModel);
    }

    /**
     * 获取所有Daq设备（USB3200N）
     *
     * @param deviceModel 设备型号
     * @return 所有Daq设备
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Daq.USB3200N, targetClazz = USB3200N.class),
            },
            enable = true)
    public List<Device> getAllDaq(String deviceModel) {
        return deviceFinderManager.getDaqDeviceFinder().findAllDaqDevices(deviceModel);
    }

    /**
     * 获取所有I2C-USB设备
     *
     * @param deviceModel 设备型号
     * @return 所有I2C-USB设备
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.UsbI2C.USB_I2C, targetClazz = I2CDevice.class),
            },
            enable = true)
    public List<Device> getAllUsbI2C(String deviceModel) {
        return deviceFinderManager.getI2CDeviceFinder().findI2CDevice(deviceModel);
    }

    /**
     * 获取remoteResistance设备
     *
     * @param deviceModel 设备型号
     * @return 所有remoteResistance设备
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Resistance.REMOTE_RESISTANCE, targetClazz = RemoteResistanceDevice.class),
            },
            enable = true)
    public List<Device> getAllRemoteResistance(String deviceModel) {
        return deviceFinderManager.getRemoteResistanceDeviceFinder().findAllIpResistance(deviceModel, true);
    }

    /**
     * 获取TcpServer
     *
     * @param deviceModel 设备型号
     * @return 所有TcpServer
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.TcpServer.TCP_SERVER, targetClazz = TcpServerDevice.class),
            },
            enable = true)
    public List<Device> getAllTcpServer(String deviceModel) {
        return deviceFinderManager.getTcpServerFinder().findAllTcpServer(deviceModel, true);
    }

    /**
     * 获取TcpClient
     *
     * @param deviceModel 设备型号
     * @return 所有TcpClient
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.TcpServer.TCP_SERVER, targetClazz = TcpServerDevice.class),
            },
            enable = true)
    public List<Device> getAllTcpClient(String deviceModel) {
        return deviceFinderManager.getTcpServerFinder().findAllTcpServer(deviceModel, true);
    }


    /**
     * 获取所有音频输入设备
     *
     * @param deviceModel
     * @return
     */
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.SoundCard.AUXIN_SOUND_DEVICE, targetClazz = AuxInSoundDevice.class),
            },
            enable = true)
    public List<Device> getAllSoundDevices(String deviceModel) {
        return deviceFinderManager.getSoundDeviceFinder().findAllSoundInputDevices();
    }

    /**
     * 获取所有USB4704设备
     **/
    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.SoundCard.USB4704_DEVICE, targetClazz = USB4704.class),
            },
            enable = true)
    public List<Device> getAllUSB4704Devices(String deviceModel) {
        return deviceFinderManager.getSoundDeviceFinder().findUSB4704Devices();
    }

    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.Speaker.SPEAKER, targetClazz = SpeakerDevice.class),
            },
            enable = true)
    public List<Device> getAllSpeakerDevices(String deviceModel) {
        return deviceFinderManager.getSoundDeviceFinder().findAllSoundOutputDevices();
    }

    @DeviceMock(numbers = 1,
            alias = {
                    @OperationTargetAliasMap(deviceModel = DeviceModel.VideoCapture.VIDEO_CAPTURE, targetClazz = VideoCaptureDevice.class),
            },
            enable = true)
    public List<Device> getAllVideoCaptureDevices(String deviceModel) {
        return deviceFinderManager.getVideoCaptureDeviceFinder().findAllVideoCaptureDevices(deviceModel, true);
    }

}
