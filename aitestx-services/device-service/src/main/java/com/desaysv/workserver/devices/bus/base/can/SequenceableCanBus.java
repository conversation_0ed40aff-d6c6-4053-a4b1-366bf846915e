package com.desaysv.workserver.devices.bus.base.can;

import cantools.dbc.DbcReader;
import cantools.dbc.DecodedSignal;
import cantools.dbc.Message;
import cantools.dbc.Node;
import cantools.exceptions.DecodingFrameLengthException;
import cantools.exceptions.MessageNotFoundException;
import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.config.can.*;
import com.desaysv.workserver.devices.bus.DbcUtils;
import com.desaysv.workserver.devices.bus.base.*;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequenceMore;
import com.desaysv.workserver.entity.DeviceContextInfo;
import com.desaysv.workserver.exceptions.SimulatedDeviceNotification;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.DataUtils;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.HEX_NUMBER;

/**
 * 支持动作序列的CAN总线
 */

public abstract class SequenceableCanBus extends CanBus implements ICanSequenceMore {
    Logger log = LogManager.getLogger(SequenceableCanBus.class.getSimpleName());

    private static final int READ_TIMEOUT = 5000;
    protected final static int TIMEOUT_MILLISECONDS = 5000;
    private final CanRecvQueueManager canRecvQueueManager;
    protected String projectName;

    public SequenceableCanBus() {
        this(new DeviceOperationParameter());
    }

    public SequenceableCanBus(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        canRecvQueueManager = new CanRecvQueueManager();
        this.projectName = deviceOperationParameter.getProject();
    }

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return false;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public void send(FlexrayMessage message, Float timeout) throws BusError {

    }

    /**
     * 写入配置文件
     *
     * @param canConfigParameter
     * @return
     */
    protected CanConfig writeConfig(CanConfigParameter canConfigParameter) {
        // 写入配置文件
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        Map<String, CanConfigParameter> configParameters = deviceConfig.getConfigParameters();

        int channel = canConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= getMaxChannelCount(); i++) {
                CanConfigParameter clonedParam = SerializationUtils.clone(canConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(canConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    /**
     * 写入配置文件
     *
     * @param netCanConfigParameter
     * @return
     */
    protected CanConfig writeConfig(NetCanConfigParameter netCanConfigParameter) {
        // 写入配置文件
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        Map<String, NetCanConfigParameter> configParameters = deviceConfig.getConfigNetParameters();

        int channel = netCanConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= 4; i++) {
                NetCanConfigParameter clonedParam = SerializationUtils.clone(netCanConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(netCanConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    @Override
    public int getCanMessageCycle(Integer deviceChannel, String messageName) {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageName);
                if (message != null) {
                    break;
                }
            }
            if (message != null) {
                return message.getInterval();
            }
        }
        throw new IllegalArgumentException("未找到报文" + messageName);
    }

    @Override
    public boolean sendCanData(Integer deviceChannel, String messageId, byte[] byteData, Integer period, Integer cycle, boolean isCanFd) throws BusError {
        CanMessage canMessage = new CanMessage();
        if (period == 0) {
            log.info("检测到CAN报文{}周期为0，改为发送事件帧报文", messageId);
            canMessage.setSendTimes(1);
        } else {
            canMessage.setSendTimes(cycle == null ? -1 : cycle);
        }
        canMessage.setCanFd(isCanFd);
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        canMessage.setData(byteData);
        canMessage.setPeriod(period / 1000.0f); //s
        canMessage.setDlc(canMessage.getData().length);
        canMessage.setDuration(canMessage.getSendTimes() * canMessage.getPeriod());
        sendCanMessage(deviceChannel, canMessage);
        return true;
    }

    @Override
    public boolean stopSendPeriodicCanData(Integer deviceChannel, String messageId) {
        log.info("停止发送CAN通道{}报文{}", deviceChannel, messageId);
        // 判断messageId是否符合十六进制格式，符合的话就是报文id
        if (messageId != null && messageId.matches(HEX_NUMBER)) {
            stopCanMessage(deviceChannel, DataUtils.parseHexString(messageId));
        } else {
            //否则messageId为报文名称
            CanConfig deviceConfig = getDeviceConfig();
            DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
            try {
                if (dbcConfig != null) {
                    Message message = null;
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                    for (DbcReader dbcReader : dbcReaders) {
                        message = dbcReader.getBus().getMessageMap().get(messageId);
                        if (message != null) {
                            break;
                        }
                    }

                    if (message == null) {
                        throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageId));
                    }
                    stopCanMessage(deviceChannel, DataUtils.parseHexString(message.getId()));
                } else {
                    throw new BusError("dbc文件未找到");
                }
            } catch (Exception e) {
                log.error("停止发送CAN通道{}报文{}失败", deviceChannel, messageId);
            }
        }
        return true;
    }

    @Override
    public boolean sendAllPeriodicCanMessage(Integer deviceChannel) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                for (DbcReader dbcReader : dbcReaders) {
                    List<Message> messages = dbcReader.getBus().getMessages();
                    for (Message message : messages) {
                        byte[] messageData = dbcReader.getMessageData(message.getName());
                        sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                    }
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, int messageStatus) throws BusError {
        if (messageStatus == 0) {
            log.info("暂停CAN通道{}所有报文", deviceChannel);
            pauseAllCanMessage(deviceChannel);
        } else {
            CanConfig deviceConfig = getDeviceConfig();
            DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
            try {
                if (dbcConfig != null) {
                    log.info("发送CAN通道{}所有报文", deviceChannel);
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                    for (DbcReader dbcReader : dbcReaders) {
                        List<Message> messages = dbcReader.getBus().getMessages();
                        for (Message message : messages) {
                            setCanSingleMsgStatus(deviceChannel, null, message.getName(), messageStatus);
                        }
                    }
                } else {
                    throw new BusError("dbc文件未找到");
                }
            } catch (BusError e) {
                throw e;
            } catch (Exception e) {
                throw new BusError(e);
            }
        }
        return true;
    }

    @Override
    public String verifyCanMessage(Integer deviceChannel, String messageId, String byteData,Integer count) throws BusError {
        return "";
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, List<String> excludeEcus, int messageStatus) throws BusError {
        boolean ok = true;
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            Set<String> excludeEcuSet = excludeEcus.stream().map(String::toUpperCase).collect(Collectors.toSet());
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            for (DbcReader dbcReader : dbcReaders) {
                List<Node> nodes = dbcReader.getNetwork().getNode();
                for (Node node : nodes) {
                    String ecu = node.getName().toUpperCase();
                    if (excludeEcuSet.contains(ecu)) {
                        continue;
                    }
                    ok &= setCanEcuAllMsgStatus(deviceChannel, ecu, messageStatus);
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return ok;
    }


    @Override
    public boolean setCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long signalRawValue) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                Message message = null;
                DbcReader dbcReader = null;
                for (int i = 0; i < dbcReaders.size(); i++) {
                    message = dbcReaders.get(i).getBus().getMessageMap().get(messageName);
                    if (message != null) {
                        dbcReader = dbcReaders.get(i);
                        break;
                    }
                }
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}报文{}信号{}原始值改为:0x{}", deviceChannel, messageName, signalName, String.format("%X", signalRawValue));
                CyclicTask cyclicTask = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel,
                        DataUtils.parseHexString(message.getId())));
                if (cyclicTask == null) {
                    //信号没有运行，启动报文
                    byte[] bytesBefore = dbcReader.getMessageData(messageName);
                    byte[] bytesAfter = decodeSignalRawValue(message, signalName, BigInteger.valueOf(signalRawValue), bytesBefore);
                    DeviceContextInfo.getInstance().getMessageDataMap().put(messageName,ByteUtils.byteArrayToHexString(bytesAfter));
                    sendCanData(deviceChannel, message.getId(), bytesAfter, message.getInterval(), null, message.isFd());
                } else if (cyclicTask instanceof ThreadBasedCyclicSendTask) {
                    //信号已经在运行
                    ThreadBasedCyclicSendTask task = (ThreadBasedCyclicSendTask) cyclicTask;
                    byte[] bytesBefore = task.getCanMessage().getData();
                    byte[] bytesAfter = decodeSignalRawValue(message, signalName, BigInteger.valueOf(signalRawValue), bytesBefore);
                    DeviceContextInfo.getInstance().getMessageDataMap().put(messageName,ByteUtils.byteArrayToHexString(bytesAfter));
                    task.modifyData(bytesAfter);
                    task.resume();
                } else {
                    throw new BusError("CAN报文任务类型错误");
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    /**
     * 设置指定CAN信号的物理值
     *
     * @param deviceChannel  CAN通道号(1/2)
     * @param ecuNodeName    ECU节点名称（当前参数未实际使用，保留供后续扩展）
     * @param messageName    报文名称（对应DBC文件中定义的报文名称）
     * @param signalName     信号名称（对应DBC文件中定义的信号名称）
     * @param signalPhyValue 要设置的物理值（经过缩放和偏移处理后的实际物理量值）
     * @return 总是返回true表示操作成功
     * @throws BusError 可能抛出以下异常：
     *                  1. DBC文件未找到
     *                  2. 指定报文/信号不存在
     *                  3. 报文任务类型错误
     * @implNote 实现逻辑：
     * 1. 根据通道号获取DBC配置，加载对应DBC文件
     * 2. 查找目标报文和信号定义
     * 3. 判断信号对应的周期发送任务是否存在：
     * - 若任务不存在：创建新的周期发送任务，使用修改后的信号值启动发送
     * - 若任务存在：修改运行中的报文数据，并恢复任务执行
     * 4. 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
     */
    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) throws BusError {
        return setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, signalPhyValue, null);
    }

    /**
     * 设置指定CAN信号的物理值
     *
     * @param deviceChannel  CAN通道号(1/2)
     * @param ecuNodeName    ECU节点名称（当前参数未实际使用，保留供后续扩展）
     * @param messageName    报文名称（对应DBC文件中定义的报文名称）
     * @param signalName     信号名称（对应DBC文件中定义的信号名称）
     * @param signalPhyValue 要设置的物理值（经过缩放和偏移处理后的实际物理量值）
     * @param cycle          发送次数
     * @return 总是返回true表示操作成功
     * @throws BusError 可能抛出以下异常：
     *                  1. DBC文件未找到
     *                  2. 指定报文/信号不存在
     *                  3. 报文任务类型错误
     * @implNote 实现逻辑：
     * 1. 根据通道号获取DBC配置，加载对应DBC文件
     * 2. 查找目标报文和信号定义
     * 3. 判断信号对应的周期发送任务是否存在：
     * - 若任务不存在：创建新的周期发送任务，使用修改后的信号值启动发送
     * - 若任务存在：修改运行中的报文数据，并恢复任务执行
     * 4. 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
     */
    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue, Integer cycle) throws BusError {
        // 获取设备配置和DBC配置
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            // 判断DBC配置是否存在
            if (dbcConfig != null) {
                // 加载DBC文件
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                Message message = null;
                DbcReader dbcReader = null;
                for (int i = 0; i < dbcReaders.size(); i++) {
                    // 查找目标报文和信号定义
                    message = dbcReaders.get(i).getBus().getMessageMap().get(messageName);
                    if (message != null) {
                        dbcReader = dbcReaders.get(i);
                        break;
                    }
                }

                // 报文不存在则抛出异常
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}报文{}信号{}物理值改为:{}", deviceChannel, messageName, signalName, signalPhyValue);
                // 获取信号执行任务
                CyclicTask cyclicTask = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel,
                        DataUtils.parseHexString(message.getId())));
                // 判断信号对应的周期发送任务是否存在
                if (cyclicTask == null) {  //信号没有运行，启动报文
                    byte[] bytesBefore = dbcReader.getMessageData(messageName);
                    log.info("CAN通道{}获取报文{}初始值:{}", deviceChannel, messageName, StrUtils.getHexStringWithBlank((bytesBefore)));
                    // 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
                    byte[] bytesAfter = decodeSignalPhyValue(message, signalName, BigDecimal.valueOf(signalPhyValue), bytesBefore);
                    sendCanData(deviceChannel, message.getId(), bytesAfter, message.getInterval(), cycle, message.isFd());
                } else if (cyclicTask instanceof ThreadBasedCyclicSendTask) { // 信号已经在运行
                    //信号已经在运行
                    ThreadBasedCyclicSendTask task = (ThreadBasedCyclicSendTask) cyclicTask;
                    byte[] bytesBefore = task.getCanMessage().getData();
                    // 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
                    byte[] bytesAfter = decodeSignalPhyValue(message, signalName, BigDecimal.valueOf(signalPhyValue), bytesBefore);
                    // 修改运行中的报文数据，并恢复任务执行
                    task.modifyData(bytesAfter);
                    task.resume();
                } else {
                    throw new BusError("CAN报文任务类型错误");
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    @Override
    public void executeCycleStepLogic(String taskId, Integer deviceChannel, String ecuNodeName,
                                      String messageName, String signalName, Integer start,
                                      Integer end, int step, String interval) {
        try {
            ActualExpectedResult result = stepResults.get(taskId);
            if (result == null) return;

            // 初始化步进参数
            final long milliseconds = (long) (BaseRegexRule.getSecondsOfDefaultMills(interval) * 1000);
            int direction = Integer.signum(end - start);
            if (direction == 0) {
                result.put("setStepCanSignal", true, "步进初始值与终止值一致！");
                return;
            }

            int effectiveStep = step * direction;
            int currentValue = start;
            int stepCount = 0;

            // 步进主循环
            while (!Thread.currentThread().isInterrupted()) {
                while (direction == 1 ? currentValue <= end : currentValue >= end) {
                    // 暂停控制
                    handlePauseState();

                    // 执行信号设置
                    boolean success = setCanSignalPhyValue(deviceChannel, ecuNodeName,
                            messageName, signalName, currentValue, 1);
                    stepCount++;
                    result.put("stepCount", stepCount);
                    
                    if (!success) {
                        log.error("第{}次信号设置失败，终止任务", stepCount);
                        result.put("setStepCanSignal", false, "第" + stepCount + "次信号设置失败");
                        result.markComplete(false);
                        return;
                    }

                    result.put("setStepCanSignal", success, currentValue);

                    // 第二次步进成功后标记完成
                    if (stepCount >= 2) {
                        log.info("循环步进改变信号值成功，任务继续在后台运行");
                        result.put("setStepCanSignal", true, "循环步进改变信号值成功，任务继续在后台运行");
                    }

                    // 间隔控制
                    if (milliseconds > 0) {
                        Thread.sleep(milliseconds);
                    }

                    currentValue += effectiveStep;
                }

                // 循环重置
                currentValue = start;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("任务被中断");
            ActualExpectedResult result = stepResults.get(taskId);
            if (result != null) {
                result.put("setStepCanSignal", false, "任务被中断");
                result.markComplete(false);
            }
        } catch (BusError e) {
            log.error("总线错误: {}", e.getMessage());
            ActualExpectedResult result = stepResults.get(taskId);
            if (result != null) {
                result.put("setStepCanSignal", false, "总线错误: " + e.getMessage());
                result.markComplete(false);
            }
        }
    }

    // 封装的暂停处理方法
    private void handlePauseState() {
        while (isPause()) {
            log.info("CAN信号步进已暂停");
            synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                try {
                    ActionSequenceLock.getInstance().getPauseLock().wait();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }


    private static byte[] decodeSignalPhyValue(Message message, String signalName, BigDecimal signalPhyValue, byte[] decodeBytes) throws DecodingFrameLengthException, BusError {
        Map<String, DecodedSignal> decodedSignalMap = message.decodeByDecodedSignal(decodeBytes);
        DecodedSignal decodedSignal = decodedSignalMap.get(signalName);
        if (decodedSignal == null) {
            throw new BusError(String.format("CAN报文%s信号%s未找到", message.getName(), signalName));
        }
        decodedSignal.changePhyValue(signalPhyValue);
        return message.encodeByDecodedSignal(decodedSignalMap);
    }

    private static byte[] decodeSignalRawValue(Message message, String signalName, BigInteger signalRawValue, byte[] decodeBytes) throws DecodingFrameLengthException, BusError {
        Map<String, DecodedSignal> decodedSignalMap = message.decodeByDecodedSignal(decodeBytes);
        DecodedSignal decodedSignal = decodedSignalMap.get(signalName);
        if (decodedSignal == null) {
            throw new BusError(String.format("CAN报文%s信号%s未找到", message.getName(), signalName));
        }
        decodedSignal.changeRawValue(signalRawValue);
        return message.encodeByDecodedSignal(decodedSignalMap);
    }

    @Override
    public double fetchCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError {
        return fetchCanSignalValue(deviceChannel, ecuNodeName, messageName, signalName, false);
    }

    @Override
    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError {
        return fetchCanSignalValue(deviceChannel, ecuNodeName, messageName, signalName, true);
    }

    private Message searchMessage(DbcConfig dbcConfig, String messageName) {
        List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
        Message message = null;
        for (DbcReader reader : dbcReaders) {
            message = reader.getBus().getMessageMap().get(messageName);
            if (message != null) {
                break;
            }
        }
        return message;
    }

    @Override
    public Map<String, DecodedSignal> fetchAllCanSignalValue(Integer deviceChannel, String messageName) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                Message message = searchMessage(dbcConfig, messageName);
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}读取{}报文:{}，设定读取超时:{}ms", deviceChannel, message.getName(), message.isFd() ? "CANFD" : "CAN", READ_TIMEOUT);
                byte[] data;
                if (isSimulated()) {
                    byte[] byteArray = new byte[64]; // Creating array with 64 bytes (B1 to B64)
                    byteArray[48] = (byte) 0x88; // B49
                    byteArray[49] = (byte) 0xFD; // B50
                    byteArray[50] = (byte) 0xEF; // B51
                    byteArray[51] = (byte) 0x41; // B52
                    byteArray[52] = (byte) 0x88; // B53
                    byteArray[53] = (byte) 0xFD; // B54
                    byteArray[54] = (byte) 0xEF; // B55
                    byteArray[55] = (byte) 0x41; // B56
                    byteArray[56] = (byte) 0x88; // B57
                    byteArray[57] = (byte) 0xFD; // B58
                    byteArray[58] = (byte) 0xEF; // B59
                    byteArray[59] = (byte) 0x41; // B60
                    byteArray[60] = (byte) 0x88; // B61
                    byteArray[61] = (byte) 0xFD; // B62
                    byteArray[62] = (byte) 0xEF; // B63
                    byteArray[63] = (byte) 0x41; // B64
                    data = byteArray;
//                    int length = message.getLength();
//                    data = new byte[length];
//                    // 填充数组
//                    for (int i = 0; i < length; i++) {
//                        data[i] = (byte) (i % 256);  // 使用模256的方式防止溢出
//                    }
                } else {
                    data = readDataByIdHex(deviceChannel, message.getIntegerId(), message.isFd(), READ_TIMEOUT);
                }
                log.info("读取到的数据:{}", ByteUtils.byteArrayToHexString(data));
                if (!ByteUtils.isEmpty(data)) {
                    return message.decodeByDecodedSignal(data);
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (DecodingFrameLengthException | TSCanException | InterruptedException e) {
            throw new BusError(e);
        }
        return new HashMap<>();
    }

    public double fetchCanSignalValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, boolean isPhyValue) throws SimulatedDeviceNotification, BusError {
        if (isSimulated()) {
            throw new SimulatedDeviceNotification();
        }
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            Map<String, DecodedSignal> decodedSignalMap = fetchAllCanSignalValue(deviceChannel, messageName);
            if (!decodedSignalMap.containsKey(signalName)) {
                throw new BusError(String.format("CAN通道%d报文%s信号%s未找到", deviceChannel, messageName, signalName));
            } else {
                return isPhyValue ? decodedSignalMap.get(signalName).getPhyValue().doubleValue() : decodedSignalMap.get(signalName).getRawValue().doubleValue();
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
    }

    @Override
    public boolean setCanMessageCSRolling(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) {
        return false;
    }

    @Override
    public boolean setCanMessageDLC(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageNameOrId);
                if (message != null) {
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                cyclicTask.modifyDlc((int) dlc);
            } else {
                throw new BusError(String.format("CAN通道%d报文%s未运行", deviceChannel, message.getName()));
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    @Override
    public boolean setCanMessageCycleTime(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageNameOrId);
                if (message != null) {
                    dbcReader = reader;
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                cyclicTask.modifyPeriod((float) (cycleTime / 1000.0f));
            } else {
                //报文未运行
                try {
                    byte[] messageData = dbcReader.getMessageData(message.getName());
                    sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), (int) cycleTime, message.isFd());
                } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                    throw new BusError(e);
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    @Override
    public boolean setCanSingleMsgStatus(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            log.info("CAN通道{}报文{}{}", deviceChannel, messageNameOrId, messageStatus == 0 ? "丢失" : "恢复");
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                // 根据id或者名称获取
                message = reader.getBus().getMessageMap().values().stream()
                        .filter(msg -> msg.getId().equals(messageNameOrId) || msg.getName().equals(messageNameOrId))
                        .findFirst()
                        .orElse(null);
                if (message != null) {
                    dbcReader = reader;
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                if (messageStatus == 0) {
                    cyclicTask.pause();
                } else {
                    cyclicTask.resume();
                }
            } else {
                //信号没在运行
                if (messageStatus == 1) {
                    try {
                        byte[] messageData = dbcReader.getMessageData(message.getName());
                        sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                    } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                        throw new BusError(e);
                    }
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    @Override
    public boolean setCanEcuAllMsgStatus(Integer deviceChannel, String ecuNodeName, int messageStatus) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            log.info("CAN通道{}的ECU节点{}所有报文{}", deviceChannel, ecuNodeName, messageStatus == 0 ? "丢失" : "恢复");
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            List<Message> messages = new ArrayList<>();
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                messages = reader.getBus().getMessageByEcu(ecuNodeName);
                if (!messages.isEmpty()) {
                    dbcReader = reader;
                    break;
                }
            }
            for (Message message : messages) {
                ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
                if (cyclicTask != null) {
                    //信号已经在运行
                    if (messageStatus == 0) {
                        cyclicTask.pause();
                    } else {
                        cyclicTask.resume();
                    }
                } else {
                    //信号没在运行
                    if (messageStatus == 1) {
                        try {
                            byte[] messageData = dbcReader.getMessageData(message.getName());
                            sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                        } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                            throw new BusError(e);
                        }
                    }
                }
            }
            if (messages.isEmpty()) {
                log.info("CAN通道{}的ECU节点{}没包含需要发送的报文", deviceChannel, ecuNodeName);
            }
            return true;
        } else {
            throw new BusError("dbc文件未找到");
        }
    }


    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        boolean isChecked = StrUtils.containsIgnoreCase(checkedContext, BaseRegexRule.PTX_RX_CONSTANT);
        String recvMessageId = StrUtils.extractHexId(checkedContext);
        return setCanPTS(deviceChannel, ecuNodeName, messageId, byteInstruction, recvMessageId, isChecked);
    }


    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError {
        try {
            String sendPtsMessageId = sendMessageId == null ? getDeviceConfig().getPtsConfig().getHexSendPtsMessageId() : sendMessageId;
            if (sendPtsMessageId == null) {
                log.warn("CAN PTS发送报文ID未设置");
                return false;
            }
            if (isChecked) {
                log.info("发送前清除CAN缓存");
                clearBuffer(deviceChannel);
            }
            log.info("CAN通道{}写入PTS ID:{}，报文数据:{}", deviceChannel, sendPtsMessageId, StrUtils.addHexSpace(byteInstruction));
            CanMessage canMessage = new CanMessage();
            canMessage.setChannel(deviceChannel);
            canMessage.setArbitrationId(DataUtils.parseHexString(sendPtsMessageId));
            canMessage.setData(ByteUtils.hexStringToByteArray(byteInstruction));
            canMessage.setCanFd(false);
            canMessage.setDlc(canMessage.getData().length);
            canMessage.setRemoteFrame(false);
            if (DataUtils.parseHexString(sendPtsMessageId) > 0x7FF) {
                canMessage.setExtendedId(true);
            } else {
                canMessage.setExtendedId(false);
            }
            canMessage.setSendTimes(1);
            send(canMessage);
            if (isChecked) {
                //先清空缓存队列
                canRecvQueueManager.clear();
                //需要检查PTS RX
                String receivePtsMessageId = recvMessageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : recvMessageId;
                log.info("CAN通道{}发送完实时读取PTS ID:{}，设定读取超时时间:{}ms", deviceChannel, receivePtsMessageId, TIMEOUT_MILLISECONDS);
                int targetCanId = DataUtils.parseHexString(receivePtsMessageId);
                //读取PTS
                byte[] data = readDataByIdHex(deviceChannel, targetCanId, getDeviceConfig().getPtsConfig().isCanFd(), TIMEOUT_MILLISECONDS);
                canRecvQueueManager.put(deviceChannel, targetCanId, data);
                log.info("CAN通道{}完成读取PTS ID:{}", deviceChannel, ByteUtils.byteArrayToHexString(data));
            }
            return true;
        } catch (Throwable e) {
            throw new BusError(e);
        }
    }

    @Override
    public String fetchCanPTS(Integer deviceChannel, String messageId) throws BusError {
        try {
            String receivePtsMessageId = messageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : messageId;
            boolean isCanFd = getDeviceConfig().getPtsConfig().isCanFd();
            if (receivePtsMessageId == null) {
                log.warn("CAN PTS接收报文ID未设置");
                return "";
            }
            log.info("CAN通道{}接收及判断PTS ID:{}", deviceChannel, receivePtsMessageId);
            int targetCanId = DataUtils.parseHexString(receivePtsMessageId);
            byte[] tsCanReturnData;
            if (canRecvQueueManager.contains(deviceChannel, targetCanId)) {
                //包含缓存队列中
                log.info("CAN通道{}已经缓存目标PTS接收ID:{}", deviceChannel, receivePtsMessageId);
                tsCanReturnData = canRecvQueueManager.get(deviceChannel, targetCanId);
            } else {
                log.info("CAN通道{}设定读取超时:{}ms", deviceChannel, TIMEOUT_MILLISECONDS);
                tsCanReturnData = readDataByIdHex(deviceChannel, targetCanId, isCanFd, TIMEOUT_MILLISECONDS);
            }
            if (tsCanReturnData != null) {
                log.info("CAN通道{}读取到PTS报文:{}{}", deviceChannel,
                        Arrays.toString(tsCanReturnData),
                        ByteUtils.isEmpty(tsCanReturnData) ? "" : String.format("(%s)", ByteUtils.byteArrayToHexString(tsCanReturnData)));
                return ByteUtils.byteArrayToHexString(tsCanReturnData);
            } else {
                log.error("CAN通道{}PTS接收报文ID:{}超时", deviceChannel, receivePtsMessageId);
                return "NA";
            }
        } catch (Throwable e) {
            throw new BusError(e);
        } finally {
            //清空缓冲队列
            canRecvQueueManager.clear();
        }

    }

    @Override
    public boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return false;
    }

    @Override
    public boolean lastCheckCanMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        return false;
    }

    @Override
    public String notificationUpgrade(int fileType) throws BusError {
        return null;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        return false;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) throws BusError {
        return 0;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpFunSwitch(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpVar(String varName, int xcpValue) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpSwitchAndVar(int switchCommand, String varName, int varValue) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyPosition(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyButton(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setRDefogSts(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setMirrorFoldSTS(String command) throws BusError {
        return false;
    }

    @Override
    public boolean setLampSwitch(String command) throws BusError {
        return false;
    }

    @Override
    public boolean checkTurnLamp(String turnLampType, int workTime, int checkPeriod) throws BusError {
        return false;
    }

    @Override
    public boolean checkFourDoor(String lockStatusCommand) throws BusError {
        return false;
    }

    @Override
    public boolean sendEventMsg(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) throws BusError {
        return false;
    }

    @Override
    public boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        return false;
    }

    @Override
    public double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }


    @Override
    public int fetchXcpVar(String varName) throws BusError {
        return 0;
    }

    @Override
    public boolean checkFindKeyOrNoKey(boolean findKey, int findKeyTime) throws BusError {
        return false;
    }

    public abstract int getMaxChannelCount();


    /**
     * 获取指定通道的最新CAN报文时间戳
     * @param deviceChannel 设备通道号
     * @return 最新时间戳（单位：毫秒）
     */
    public abstract long getLatestCanTimestamp(Integer deviceChannel);

    @Override
    public boolean wake(Integer deviceChannel, Integer time) {
        try {
            long startTime = System.currentTimeMillis();
            long lastTime = getLatestCanTimestamp(deviceChannel);

            while (System.currentTimeMillis() - startTime < time) {
                long newTime = getLatestCanTimestamp(deviceChannel);
                if (newTime > lastTime) {
                    return true; // 收到新报文
                }
                Thread.sleep(200); // 避免CPU空转
            }

            log.warn("在{}ms等待期内未收到新的CAN报文,设备唤醒失败", time);
            return false;
        } catch (InterruptedException e) {
            log.error("唤醒设备失败", e);
            return false;
        }
    }

    @Override
    public boolean sleep(Integer deviceChannel, Integer time) {
        try {
            long startTime = System.currentTimeMillis();
            long lastTime = getLatestCanTimestamp(deviceChannel);

            while (System.currentTimeMillis() - startTime < time) {
                long newTime = getLatestCanTimestamp(deviceChannel);
                if (newTime > lastTime) {
                    log.warn("在{}ms等待期内收到新的CAN报文,设备睡眠失败", time);
                    return false; // 收到新报文，睡眠失败
                }
                Thread.sleep(200); // 避免CPU空转
            }

            return true; // 成功进入睡眠状态

        } catch (InterruptedException e) {
            log.error("睡眠设备失败", e);
            return false;
        }
    }

    /**
     * 构建CAN报文基础信息
     *
     * @param deviceChannel 设备通道
     * @param messageId     报文ID
     * @param byteData      数据字符串（十六进制）
     * @return 构建好的CanMessage对象
     */
    protected CanMessage buildCanMessage(Integer deviceChannel, String messageId, String byteData) {
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        byte[] data = ByteUtils.hexStringToByteArray(byteData);

        // 填充数据：第一个字节为长度，后续为实际数据，不足部分补0
        byte[] paddedData = new byte[8];
        paddedData[0] = (byte) data.length;
        System.arraycopy(data, 0, paddedData, 1, data.length);
        Arrays.fill(paddedData, 1 + data.length, 8, (byte) 0x00);

        canMessage.setData(paddedData);
        canMessage.setCanFd(false);
        canMessage.setDlc(paddedData.length);
        canMessage.setRemoteFrame(false);

        if (DataUtils.parseHexString(messageId) > 0x7FF) {
            canMessage.setExtendedId(true);
        } else {
            canMessage.setExtendedId(false);
        }

        canMessage.setSendTimes(1);
        return canMessage;
    }

    /**
     * 发送服务帧
     *
     * @param deviceChannel 设备通道
     * @param messageId     报文ID
     * @param byteData      数据字符串（十六进制）
     */
    public void sendService(Integer deviceChannel, String messageId, String byteData) {
        CanMessage canMessage = buildCanMessage(deviceChannel, messageId, byteData);
        try {
            send(canMessage);  // 调用子类实现的send方法
        } catch (BusError e) {
            log.error("服务发送失败: {}", e.getMessage());
        }
    }

    private boolean sendSmallPacket(CanMessage canMessage, byte[] data, Integer deviceChannel) {
        byte[] paddedData = new byte[8];
        System.arraycopy(data, 0, paddedData, 0, data.length);
        Arrays.fill(paddedData, data.length, 8, (byte) 0x00);
        canMessage.setData(paddedData);
        canMessage.setDlc(paddedData.length);
        canMessage.setSendTimes(1);

        try {
            send(canMessage);
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            int receiveId = -1;
            if (!canConfig.getUdsModel().getResponseId().isEmpty()) {
                receiveId = Integer.parseInt(canConfig.getUdsModel().getResponseId().replace("0x", ""), 16);
            }
            Object buffer = getReceiveBuffer();
            Object msgData = readFromTail(buffer, 0);

            long startTime = System.currentTimeMillis();
            boolean sendSuccess = false;

            while (System.currentTimeMillis() - startTime < 3000) {
                if (!isBufferEmpty(buffer)) {
                    for (int i = 0; i < getBufferSize(buffer); i++) {
                        Object returnData = readFromTail(buffer, i);
                        if (getDataFrameId(returnData) == receiveId && getTimestamp(returnData) > getTimestamp(msgData) && isValidFirstFrame(returnData)) {
                            byte[] flowControlFrame = new byte[8];
                            flowControlFrame[0] = 0x30;
                            Arrays.fill(flowControlFrame, 1, 8, (byte) 0x00);
                            sendFlowControlFrame(canMessage, flowControlFrame);
                            sendSuccess = true;
                            break;
                        }
                    }
                }
                if (sendSuccess) break;
                Thread.sleep(100);
            }
        } catch (Exception e) {
            log.error("发送失败: {}", e.getMessage());
            return false;
        }
        return true;
    }


    private boolean sendLargePacket(CanMessage canMessage, byte[] data, Integer deviceChannel) {
        int offset = 0;
        int packetCount = 0;

        byte[] firstFrameData = new byte[8];
        firstFrameData[0] = (byte) 0x10;
        System.arraycopy(data, offset, firstFrameData, 1, 7);
        canMessage.setData(firstFrameData);
        canMessage.setSendTimes(1);

        try {
            Object buffer = getReceiveBuffer();
            Object msgData = readFromTail(buffer, 0);
            send(canMessage);
            offset += 7;

            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            int receiveId = -1;
            if (!canConfig.getUdsModel().getResponseId().isEmpty()) {
                receiveId = Integer.parseInt(canConfig.getUdsModel().getResponseId().replace("0x", ""), 16);
            }

            long startTime = System.currentTimeMillis();

            while (System.currentTimeMillis() - startTime < 3000) {
                if (!isBufferEmpty(buffer)) {
                    for (int i = 0; i < getBufferSize(buffer); i++) {
                        Object returnData = readFromTail(buffer, i);
                        if (isValidFlowControlFrame(returnData, receiveId, msgData)) {
                            int packetsToSend = getDataBytes(returnData)[1];
                            int remainingPackets = (data.length - offset + 6) / 7;
                            int actualPacketsToSend = (packetsToSend == 0) ? remainingPackets : Math.min(packetsToSend, remainingPackets);

                            for (int j = 0; j < actualPacketsToSend && offset < data.length; j++) {
                                int headerValue = 0x21 + ((packetCount++) % 0x10);
                                int dataSize = Math.min(data.length - offset, 7);
                                byte[] packetData = new byte[dataSize + 1];
                                packetData[0] = (byte) headerValue;
                                System.arraycopy(data, offset, packetData, 1, dataSize);

                                if (packetData.length < 8) {
                                    byte[] paddedData = new byte[8];
                                    System.arraycopy(packetData, 0, paddedData, 0, packetData.length);
                                    Arrays.fill(paddedData, packetData.length, 8, (byte) 0x00);
                                    packetData = paddedData;
                                }

                                canMessage.setData(packetData);
                                canMessage.setSendTimes(1);
                                send(canMessage);
                                offset += dataSize;
                            }

                            if (offset >= data.length) {
                                return true;
                            }
                            startTime = System.currentTimeMillis(); // 重置超时计时器
                        } else if (isContinueFrame(returnData)) {
                            startTime = System.currentTimeMillis();
                        } else if (isAbortFrame(returnData)) {
                            return false;
                        }
                    }
                }
                Thread.sleep(100);
            }
            return false;
        } catch (Exception e) {
            log.error("发送失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取接收缓冲区对象
     * @return 子类实现返回对应的数据结构
     */
    public abstract Object getReceiveBuffer();

    /**
     * 从接收缓冲区读取数据
     * @param buffer 缓冲区对象
     * @return 返回具体的数据对象（如 TSCanReturnData 或 CanMessageVo）
     */
    public abstract Object readFromTail(Object buffer, int index);

    /**
     * 获取数据帧ID
     * @param data 数据帧对象
     * @return CAN ID
     */
    public abstract int getDataFrameId(Object data);

    /**
     * 获取时间戳
     * @param data 数据帧对象
     * @return 时间戳
     */
    public abstract long getTimestamp(Object data);

    /**
     * 获取数据字节数组
     * @param data 数据帧对象
     * @return 字节数组
     */
    public abstract byte[] getDataBytes(Object data);

    /**
     * 判断缓冲区是否为空
     * @param buffer 缓冲区对象
     * @return 是否为空
     */
    public abstract boolean isBufferEmpty(Object buffer);

    /**
     * 获取缓冲区大小
     * @param buffer 缓冲区对象
     * @return 缓冲区条目数量
     */
    public abstract int getBufferSize(Object buffer);

    /**
     * 构造并发送流控帧
     * @param canMessage 基础CAN消息
     * @param flowControlFrame 流控帧内容
     */
    public abstract void sendFlowControlFrame(CanMessage canMessage, byte[] flowControlFrame) throws BusError;

    protected abstract boolean isValidFirstFrame(Object data);
    protected abstract boolean isValidFlowControlFrame(Object data, int receiveId, Object msgData);
    protected abstract boolean isContinueFrame(Object data);
    protected abstract boolean isAbortFrame(Object data);


    public boolean sendDatas(Integer deviceChannel, String messageId, String byteData){
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        if(StringUtils.isEmpty(messageId)){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (!canConfig.getUdsModel().getRequestId().isEmpty()) {
                messageId = canConfig.getUdsModel().getRequestId().replace("0x", "");
            }else{
                return false;
            }
        }
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        byte[] newData = ByteUtils.hexStringToByteArray(byteData);
        // 创建新的数据数组，长度为原数据长度+1，用于在第一个字节存储长度
        byte[] data = new byte[newData.length + 1];
        data[0] = (byte) newData.length;
        System.arraycopy(newData, 0, data, 1, newData.length);
        canMessage.setCanFd(false);
        canMessage.setRemoteFrame(false);
        if (DataUtils.parseHexString(messageId) > 0x7FF) {
            canMessage.setExtendedId(true);
        } else {
            canMessage.setExtendedId(false);
        }
        // 判断数据长度
        if (data.length < 8) {
            return sendSmallPacket(canMessage, data, deviceChannel);
        } else {
            return sendLargePacket(canMessage, data, deviceChannel);
        }
    }

    /**
     * 父类中的公共方法：检查回复数据是否符合预期（支持单帧/多帧）
     * @param deviceChannel 设备通道号
     * @param messageId     报文ID（如 "0x7F"）
     * @param expectedData  预期的完整数据字节数组（包含长度信息）
     * @param buffer        接收缓冲区（由子类提供）
     * @return 是否匹配
     */
    protected String checkReplyDataCommonLogic(int deviceChannel, String messageId, byte[] expectedData, Object buffer) {
        int targetId = -1;
        if(!messageId.isEmpty()){
            targetId = Integer.parseInt(messageId.replace("0x", ""), 16);
        }else{
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (!canConfig.getUdsModel().getResponseId().isEmpty()) {
                targetId = Integer.parseInt(canConfig.getUdsModel().getRequestId().replace("0x", ""),16);
            }else{
                return "";
            }
        }
        // 如果数据长度小于8，直接比较整个数据
        if (expectedData.length < 8) {
            for (int i = 0; i < getBufferSize(buffer); i++) {
                Object dataObj = readFromTail(buffer, i);
                if (getDataFrameId(dataObj) == targetId && getChannelFromData(dataObj) == deviceChannel - 1) {
                    byte[] actualData = Arrays.copyOf(getDataBytes(dataObj), expectedData.length);
                    if (Arrays.equals(actualData, expectedData)) {
                        return ByteUtils.byteArrayToHexString(actualData);
                    }
                }
            }
        } else {
            // 数据长度大于等于8，进行分包处理
            List<byte[]> dataFrames = new ArrayList<>();

            // 寻找多帧
            for (int i = 0; i < getBufferSize(buffer); i++) {
                Object currentData = readFromTail(buffer, i);
                if (getChannelFromData(currentData) == deviceChannel - 1 && getDataFrameId(currentData) == targetId) {
                    byte[] data = getDataBytes(currentData);
                    if (data.length >= 1) {
                        int frameHeader = data[0] & 0xFF;

                        // 处理首帧（0x10）
                        if (frameHeader == 0x10) {
                            byte[] packetData = new byte[7];
                            System.arraycopy(data, 1, packetData, 0, 7);
                            dataFrames.add(0, packetData);
                            break; // 找到首帧后结束查找
                        }

                        // 处理后续数据帧（0x21~0x2F）
                        else if (frameHeader >= 0x21 && frameHeader <= 0x2F) {
                            int expectedFrameNumber = (frameHeader - 0x21 + 1) % 0x10;
                            int dataLength = data.length - 1;
                            byte[] packetData = new byte[dataLength];
                            System.arraycopy(data, 1, packetData, 0, dataLength);

                            while (dataFrames.size() < expectedFrameNumber) {
                                dataFrames.add(null); // 填充空位
                            }
                            dataFrames.set(expectedFrameNumber - 1, packetData);

                            // 截断多余部分
                            if (!dataFrames.isEmpty() && dataFrames.get(0) != null) {
                                int totalExpectedLength = expectedData.length;
                                int currentTotalLength = dataFrames.stream()
                                        .filter(Objects::nonNull)
                                        .mapToInt(arr -> arr.length)
                                        .sum();

                                if (currentTotalLength > totalExpectedLength) {
                                    int excess = currentTotalLength - totalExpectedLength;
                                    byte[] lastPacket = dataFrames.get(expectedFrameNumber - 1);
                                    if (lastPacket != null && lastPacket.length > excess) {
                                        byte[] trimmedPacket = new byte[lastPacket.length - excess];
                                        System.arraycopy(lastPacket, 0, trimmedPacket, 0, trimmedPacket.length);
                                        dataFrames.set(expectedFrameNumber - 1, trimmedPacket);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 合并首帧和后续数据帧
            if (!dataFrames.isEmpty()) {
                byte[] firstFrameData = dataFrames.get(0);
                int totalLength = firstFrameData[0] & 0xFF;

                byte[] receivedData = new byte[totalLength + 1];
                System.arraycopy(firstFrameData, 0, receivedData, 0, Math.min(firstFrameData.length, receivedData.length));
                int offset = firstFrameData.length;

                for (int i = 1; i < dataFrames.size() && offset < totalLength + 1; i++) {
                    byte[] packet = dataFrames.get(i);
                    if (packet != null) {
                        int remaining = receivedData.length - offset;
                        int copyLength = Math.min(packet.length, remaining);
                        System.arraycopy(packet, 0, receivedData, offset, copyLength);
                        offset += copyLength;
                    }
                }

                return ByteUtils.byteArrayToHexString(receivedData);
            }
        }
        return "";
    }
    public abstract int getChannelFromData(Object data);

    public abstract String checkReplyData(Integer deviceChannel, String messageId, String byteData);

    public abstract String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError;

    public boolean responsiveServices(UdsModel udsModel){
        log.info("开始响应服务");
        boolean result = false;
        if(udsModel.isEnableFunction() && udsModel.getFunctionId() != null && !udsModel.getFunctionId().isEmpty()){
            if(udsModel.getServiceData() != null && !udsModel.getServiceData().isEmpty()){
                result = sendDatas(udsModel.getChannel(), udsModel.getFunctionId(), udsModel.getServiceData());
                if(udsModel.getResponseData() != null && !udsModel.getResponseData().isEmpty()){
                    String resUdsData = checkReplyData(udsModel.getChannel(), udsModel.getFunctionId(), udsModel.getResponseData());
                    result = resUdsData.equals(udsModel.getResponseData());
                }
                return result;
            }else{
                log.info("服务数据为空发送服务失败");
                return result;
            }
        }else{
            if (udsModel.getServiceData() != null && !udsModel.getServiceData().isEmpty() && udsModel.getRequestId() != null && !udsModel.getRequestId().isEmpty()){
                result = sendDatas(udsModel.getChannel(), udsModel.getRequestId(), udsModel.getServiceData());
                if(udsModel.getResponseData() != null && !udsModel.getResponseData().isEmpty() && udsModel.getResponseId() != null && !udsModel.getResponseId().isEmpty()){
                    String resUdsData = checkReplyData(udsModel.getChannel(), udsModel.getResponseId(),udsModel.getResponseData());
                    result = resUdsData.equals(udsModel.getResponseData());
                }
                return result;
            }else{
                log.info("服务数据或请求ID为空发送服务失败");
                return result;
            }
        }
    }

    public boolean responsiveService(Integer deviceChannel,String serviceData, String responseData){
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        boolean result = false;
        if(canConfig.getUdsModel().isEnableFunction() && canConfig.getUdsModel().getFunctionId() != null && !canConfig.getUdsModel().getFunctionId().isEmpty()){
            if(serviceData != null && !serviceData.isEmpty()){
                result = sendDatas(deviceChannel, canConfig.getUdsModel().getFunctionId(), serviceData);
                if(responseData != null && !responseData.isEmpty()){
                    String resUdsData = checkReplyData(deviceChannel, canConfig.getUdsModel().getFunctionId(), responseData);
                    result = resUdsData.equals(responseData);
                }
                return result;
            }else{
                log.info("服务数据为空发送服务失败");
                return result;
            }
        }else{
            if (serviceData != null && !serviceData.isEmpty() && canConfig.getUdsModel().getRequestId() != null && !canConfig.getUdsModel().getRequestId().isEmpty()){
                result = sendDatas(deviceChannel, canConfig.getUdsModel().getRequestId(), serviceData);
                if(responseData != null && !responseData.isEmpty() && canConfig.getUdsModel().getResponseId() != null && !canConfig.getUdsModel().getResponseId().isEmpty()){
                    String resUdsData = checkReplyData(deviceChannel, canConfig.getUdsModel().getResponseId(),responseData);
                    result = resUdsData.equals(responseData);
                }
                return result;
            }else{
                log.info("服务数据或请求ID为空发送服务失败");
                return result;
            }
        }
    }

    public boolean responsiveService(Integer deviceChannel, String requestId, String responseId, String serviceData, String responseData) {
        log.info("开始响应服务：{}", requestId);

        sendDatas(deviceChannel, requestId, serviceData);

        String resUdsData = checkReplyData(deviceChannel, responseId,responseData);
        boolean isReceived = resUdsData.equals(responseData);

        if (!isReceived) {
            log.error("在3秒超时时间内未接收到符合条件的响应报文: ID={}, 数据以{}开头", responseId, responseData);
        }
        return isReceived;
    }

    public boolean responsiveService(Integer deviceChannel, String functionId, String serviceData, String responseData) {
        log.info("开始响应服务：{}", functionId);

        sendDatas(deviceChannel, functionId, serviceData);

        String resUdsData = checkReplyData(deviceChannel, functionId,responseData);
        boolean isReceived = resUdsData.equals(responseData);;

        if (!isReceived) {
            log.error("在3秒超时时间内未接收到符合条件的响应报文: ID={}, 数据以{}开头", functionId, responseData);
        }
        return isReceived;
    }
    
    /**
     * 发送3E服务
     * @param deviceChannel 设备通道
     * @param isOpen 是否开启功能寻址
     * @return 是否成功
     */
    public boolean send3EService(Integer deviceChannel,boolean isOpen) {
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        UdsModel udsModel = canConfig.getUdsModel();

        String messageId;
        byte[] byteData;

        // 判断是否开启功能寻址
        if (udsModel.isEnableFunction()) {
            // 功能寻址ID
            messageId = udsModel.getFunctionId();
        } else {
            messageId = udsModel.getRequestId();
        }
        if (messageId == null || messageId.isEmpty()) {
            return false;
        }
        // 默认响应数据s
        if (udsModel.isEnableResponse()) {
            byteData = new byte[]{0x02, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
        } else {
            byteData = new byte[]{0x02, 0x3E, (byte) 0x80, 0x00, 0x00, 0x00, 0x00, 0x00};
        }
        if (isOpen) {
            // 使用接口中的方法发送CAN报文
            CanMessage canMessage = new CanMessage();
            canMessage.setChannel(deviceChannel);
            canMessage.setArbitrationId(DataUtils.parseHexString(messageId));

            // 填充数据：第一个字节为长度，后续为实际数据，不足部分补0
            canMessage.setData(byteData);
            canMessage.setCanFd(false);
            canMessage.setDlc(8);
            canMessage.setRemoteFrame(false);

            canMessage.setExtendedId(DataUtils.parseHexString(messageId) > 0x7FF);
            canMessage.setPeriod(4F);
            canMessage.setSendTimes(-1);
            sendPeriodic(canMessage, 4F,-1F);
        }else{
            stopCanMessage(deviceChannel,messageId);
        }
        return true;
    }

}
