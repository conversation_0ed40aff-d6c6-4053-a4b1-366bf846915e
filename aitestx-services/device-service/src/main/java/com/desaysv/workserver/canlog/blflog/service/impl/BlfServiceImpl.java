package com.desaysv.workserver.canlog.blflog.service.impl;

import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.blflog.jna.*;
import com.desaysv.workserver.canlog.blflog.service.BlfService;
import com.desaysv.workserver.canlog.blflog.utils.VBLCANMessageFlagsUtil;
import com.desaysv.workserver.devices.bus.nican.CanMessageVo;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinNT;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class BlfServiceImpl implements BlfService {

    static {
        try {
            File tmpDir = new File(System.getProperty("java.io.tmpdir"));
            File binlog = new File(tmpDir, "binlog.dll");
            if (!binlog.exists()) {
                copyFileFromResources("/library/dlls/blf/binlog.dll", binlog.getAbsolutePath());
            }
            System.load(binlog.getAbsolutePath());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    private static void copyFileFromResources(String resourceName, String destination) throws IOException {
        InputStream stream = BlfServiceImpl.class.getResourceAsStream(resourceName);
        if (stream == null) {
            throw new FileNotFoundException("Resource not found: " + resourceName);
        }
        Files.copy(stream, Paths.get(destination), StandardCopyOption.REPLACE_EXISTING);
    }

    public BlfServiceImpl() {

    }

    /**
     * 写入接口
     *
     * @param filePath
     * @param canMessages
     * @return
     * @throws BlfException
     */
    public boolean write(String filePath, List<CanMessageVo> canMessages) throws BlfException {
        WinNT.HANDLE fileHandle = null;
        try {
            // 打开文件
            fileHandle = binlog_jna.instanceDll.BLCreateFile(filePath, new WinDef.DWORD(BinlogConstant.GENERIC_WRITE));
            if (fileHandle == null || fileHandle.equals(WinNT.INVALID_HANDLE_VALUE)) {
                throw new BlfException("BLF打开文件失败");
            }

            for (CanMessageVo canMessage : canMessages) {
                if (canMessage.getDlc() > 8) {
                    blfCanFDMessageInit(canMessage, fileHandle);
                } else {
                    blfCanMessageInit(canMessage, fileHandle);
                }
            }

            // 确保所有内部流和文件都被刷新
            if (!binlog_jna.instanceDll.BLFlushFileBuffers(fileHandle, new WinDef.DWORD(BinlogConstant.BL_FLUSH_STREAM))) {
                throw new BlfException("BLF最终刷新文件缓冲区失败");
            }

            return true;
        } finally {
            // 确保文件句柄被关闭
            if (fileHandle != null && !fileHandle.equals(WinNT.INVALID_HANDLE_VALUE)) {
                if (!binlog_jna.instanceDll.BLCloseHandle(fileHandle)) {
                    throw new BlfException("BLF关闭文件失败");
                }
            }
        }
    }

    private void blfCanFDMessageInit(CanMessageVo canMessage, WinNT.HANDLE fileHandle) throws BlfException {
        VBLObjectHeaderBase headerBase = new VBLObjectHeaderBase();
        VBLObjectHeader header = new VBLObjectHeader();
        VBLCANFDMessage64 canMesaageFd = new VBLCANFDMessage64();
        headerBase.mSignature = BinlogConstant.BL_OBJ_SIGNATURE;
        headerBase.mHeaderSize = (short) header.size();
        headerBase.mHeaderVersion = 1;
        headerBase.mObjectSize = canMesaageFd.size();
        headerBase.mObjectType = BinlogConstant.BL_OBJ_TYPE_CAN_FD_MESSAGE_64;
        header.mBase = headerBase;
        header.mObjectFlags = BinlogConstant.BL_OBJ_FLAG_TIME_ONE_NANS;
        header.mClientIndex = 0;
        header.mObjectVersion = 0;
        header.mObjectTimeStamp = (long) (canMessage.getTime() * 1000000000L);
        canMesaageFd.mHeader = header;
        canMesaageFd.mChannel = (byte) Short.parseShort(canMessage.getChn());
        canMesaageFd.mDLC = (byte) calculateFdlcFromDataLength(canMessage.getData().length);
        canMesaageFd.mValidDataBytes = (byte) canMessage.getData().length;
        //canMesaageFd.mTxCount = 0;
        canMesaageFd.mID = Integer.valueOf(canMessage.getId().substring(2), 16);
        //canMesaageFd.mFrameLength = 0;
        int mFlags = 0;
        mFlags |= (1 << 12);//CANFD
        canMesaageFd.mFlags = mFlags;
        /*canMesaageFd.mBtrCfgArb = 0;
        canMesaageFd.mBtrCfgData = 0;
        canMesaageFd.mTimeOffsetBrsNs = 0;
        canMesaageFd.mTimeOffsetCRCDelNs = 0;
        canMesaageFd.mBitCount = 0;*/
        canMesaageFd.mDir = (byte) ("Rx".equals(canMessage.getDir()) ? 0 : 1);
        /*canMesaageFd.mExtDataOffset = 0;
        canMesaageFd.mCRC = 0;*/
        canMesaageFd.mData = canMessage.getData();
        VBLCANFDExtFrameData extFrameData = new VBLCANFDExtFrameData();
        canMesaageFd.mExtFrameData = extFrameData;
        // 将结构体写入内存
        canMesaageFd.write();

        // 将结构体数据写入文件
        if (!binlog_jna.instanceDll.BLWriteObject(fileHandle, canMesaageFd.getPointer())) {
            throw new BlfException("BLF写入文件失败");
        }
    }

    private static void blfCanMessageInit(CanMessageVo canMessage, WinNT.HANDLE fileHandle) throws BlfException {
        VBLObjectHeaderBase headerBase = new VBLObjectHeaderBase();
        VBLObjectHeader header = new VBLObjectHeader();
        VBLCANMessage2 canMesaage = new VBLCANMessage2();
        headerBase.mSignature = BinlogConstant.BL_OBJ_SIGNATURE;
        headerBase.mHeaderSize = (short) header.size();
        headerBase.mHeaderVersion = 1;
        headerBase.mObjectSize = canMesaage.size();
        headerBase.mObjectType = BinlogConstant.BL_OBJ_TYPE_CAN_MESSAGE;
        header.mBase = headerBase;
        header.mObjectFlags = BinlogConstant.BL_OBJ_FLAG_TIME_TEN_MICS;
        header.mClientIndex = 0;
        header.mObjectVersion = 0;
        header.mObjectTimeStamp = (long) (canMessage.getTime() * 1000000000L);
        canMesaage.mHeader = header;
        canMesaage.mChannel = Short.parseShort(canMessage.getChn());
        canMesaage.mFlags = VBLCANMessageFlagsUtil.setFlagsExtended(0, false, false, false);
        canMesaage.mDLC = (byte) canMessage.getDlc();
        canMesaage.mID = Integer.valueOf(canMessage.getId().substring(2), 16);
        canMesaage.mData = canMessage.getData();
        canMesaage.mFrameLength = 0;
        canMesaage.mBitCount = 0;
        canMesaage.mReserved1 = 0;
        canMesaage.mReserved2 = 0;
        // 将结构体写入内存
        canMesaage.write();

        // 将结构体数据写入文件
        if (!binlog_jna.instanceDll.BLWriteObject(fileHandle, canMesaage.getPointer())) {
            throw new BlfException("BLF写入文件失败");
        }
    }


    /**
     * 读取接口
     *
     * @param filePath
     * @return
     * @throws BlfException
     */
    @Override
    public List<CanMessageVo> read(String filePath) throws BlfException {
        List<CanMessageVo> canMessages = new ArrayList<>();
        WinNT.HANDLE fileHandle = binlog_jna.instanceDll.BLCreateFile(filePath, new WinDef.DWORD(BinlogConstant.GENERIC_READ)); // GENERIC_READ
        if (fileHandle == null || fileHandle.equals(WinNT.INVALID_HANDLE_VALUE)) {
            throw new BlfException("BLF打开文件失败");
        }

        // 循环读取文件中的所有对象
        while (true) {
            VBLObjectHeaderBase vblObjectHeaderBase = new VBLObjectHeaderBase();
            // 读取头部信息
            if (!binlog_jna.instanceDll.BLPeekObject(fileHandle, vblObjectHeaderBase)) {
                break; // 如果没有更多对象，退出循环
            }
            // 判断对象类型
            switch (vblObjectHeaderBase.mObjectType) {
                case BinlogConstant.BL_OBJ_TYPE_reserved_5:
                case BinlogConstant.BL_OBJ_TYPE_CAN_MESSAGE:
                    VBLCANMessage vb1 = new VBLCANMessage();
//                    // 先初始化结构体所有字段为零值
                    vb1.mHeader = new VBLObjectHeader();
                    vb1.mHeader.mBase = vblObjectHeaderBase;
//                    // 将结构体写入内存（准备接收数据）
                    vb1.write();
                    if (!binlog_jna.instanceDll.BLReadObjectSecure(fileHandle, vb1.getPointer(), vb1.size())) {
                        break;
                    }

                    vb1.read();
                    CanMessageVo canMessage1 = new CanMessageVo();
                    canMessage1.setEventType(String.valueOf(vb1.mFlags));
                    canMessage1.setData(vb1.mData);
                    canMessage1.setId("0x" + Integer.toHexString(vb1.mID & 0x1FFFFFFF).toUpperCase());
                    canMessage1.setChn(String.valueOf(vb1.mChannel));
                    canMessage1.setDlc(vb1.mDLC);
                    canMessage1.setTime(vb1.mHeader.mObjectTimeStamp);
                    canMessages.add(canMessage1);
                    break;
                case BinlogConstant.BL_OBJ_TYPE_CAN_MESSAGE2:
                    VBLCANMessage2 vb2 = new VBLCANMessage2();
                    // 先初始化结构体所有字段为零值
                    vb2.mHeader = new VBLObjectHeader();
                    vb2.mHeader.mBase = vblObjectHeaderBase;
                    // 将结构体写入内存（准备接收数据）
                    vb2.write();
                    binlog_jna.instanceDll.BLReadObjectSecure(fileHandle, vb2.getPointer(), vb2.size());
                    vb2.read();
                    CanMessageVo canMessage2 = new CanMessageVo();
                    canMessage2.setEventType(String.valueOf(vb2.mFlags));
                    canMessage2.setData(vb2.mData);
                    canMessage2.setId("0x" + Integer.toHexString(vb2.mID & 0x1FFFFFFF).toUpperCase());
                    canMessage2.setChn(String.valueOf(vb2.mChannel));
                    canMessage2.setDlc(vb2.mDLC);
                    canMessage2.setTime(vb2.mHeader.mObjectTimeStamp);
                    canMessages.add(canMessage2);
                    break;
                default:
                    log.debug("跳过非CAN消息对象类型: {}", vblObjectHeaderBase.mObjectType);
                    // 读取并丢弃这个对象，或进行相应处理
                    if (!binlog_jna.instanceDll.BLSkipObject(fileHandle, vblObjectHeaderBase.getPointer())) {
                        throw new BlfException("BLF跳过对象失败");
                    }
                    break;
            }
        }

        // 关闭文件
        if (!binlog_jna.instanceDll.BLCloseHandle(fileHandle)) {
            throw new BlfException("BLF关闭文件失败");
        }
        return canMessages;
    }

    private int calculateFdlcFromDataLength(int dataLength) {
        if (dataLength <= 8) {
            return dataLength; // 对于0到8字节的数据长度，FDLC直接等于数据长度
        } else if (dataLength <= 12) {
            return 9;
        } else if (dataLength <= 16) {
            return 10;
        } else if (dataLength <= 20) {
            return 11;
        } else if (dataLength <= 24) {
            return 12;
        } else if (dataLength <= 32) {
            return 13;
        } else if (dataLength <= 48) {
            return 14;
        } else if (dataLength <= 64) {
            return 15;
        } else {
            throw new IllegalArgumentException("Data length exceeds CAN FD maximum payload size.");
        }
    }
}
