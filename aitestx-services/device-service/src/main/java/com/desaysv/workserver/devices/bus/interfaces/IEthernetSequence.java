package com.desaysv.workserver.devices.bus.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.fdx.FdxUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.arraysAreEqual;

public interface IEthernetSequence {
    Logger log = LogManager.getLogger(IEthernetSequence.class.getSimpleName());

    boolean setEthDoIPFun(String address, String ethDoIPCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).SET_DoIP_FUN_ETH"})
    default ActualExpectedResult setEthDoIPFunInfo(String address, String ethDoIPCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setEthDoIPFun(address, ethDoIPCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行SET_DoIP_FUN_ETH Case编号:{},检测结果:{},共耗时:{}毫秒", ethDoIPCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setEthDoIPFunInfo", pass, ethDoIPCaseIdString);
        return actualExpectedResult;
    }

    boolean setEthDoIPUdp(String doIPUdpEthCaseId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).SET_DoIP_UDP_ETH"})
    default ActualExpectedResult setEthDoIPUdpInfo(String doIPUdpEthCaseId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setEthDoIPUdp(doIPUdpEthCaseId);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行SET_DoIP_UDP_ETH Case编号:{},检测结果:{},共耗时:{}毫秒", doIPUdpEthCaseId, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setEthDoIPUdpInfo", pass, doIPUdpEthCaseId);
        return actualExpectedResult;
    }

    boolean setEthOTA(String ethOTACaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).SET_OTA_ETH"})
    default ActualExpectedResult setEthOTAInfo(String ethOTACaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setEthOTA(ethOTACaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行OTA_ETH Case编号:{},检测结果:{},共耗时:{}毫秒", ethOTACaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setEthOTAInfo", pass, ethOTACaseIdString);
        return actualExpectedResult;
    }

    byte[] fetchEthDoIPFun() throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).GET_DoIP_FUN_ETH"})
    default ActualExpectedResult fetchEthDoIPFunInfo(String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] result = new byte[0];
        try {
            result = fetchEthDoIPFun();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = arraysAreEqual(result, FdxUtils.stringBytesPaddedSize(expectResult, 24));
        log.info("获取ETH DoIP_FUN Case编号:{},检测结果:{},共耗时:{}毫秒", expectResult, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchEthDoIPFunInfo", pass, result);
        return actualExpectedResult;
    }

    String fetchEthDoIPExplainFun() throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).GET_DoIP_EXPLAIN_FUN_ETH"})
    default ActualExpectedResult fetchEthDoIPExplainFunInfo(String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String result = null;
        try {
            result = fetchEthDoIPExplainFun();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = expectResult.equals(result);
        log.info("获取获取ETH DoIP_EXPLAIN_FUN Case编号:{},检测结果:{},共耗时:{}毫秒", result, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchEthDoIPExplainFunInfo", pass, result);
        return actualExpectedResult;
    }

    boolean fetchEthDoIPUdp(String doIPUdpCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).GET_DoIP_UDP_ETH"})
    default ActualExpectedResult fetchEthDoIPUdpInfo(String doIPUdpCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchEthDoIPUdp(doIPUdpCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取Eth DoIPUdp Case编号:{},检测结果:{},共耗时:{}毫秒", doIPUdpCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchEthDoIPUdpInfo", expectPass == pass, doIPUdpCaseIdString);
        return actualExpectedResult;
    }


    boolean fetchEthOTA(String otaCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).GET_OTA_ETH"})
    default ActualExpectedResult fetchEthOTAInfo(String otaCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchEthOTA(otaCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN EthOTA Case编号:{},检测结果:{},共耗时:{}毫秒", otaCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchEthOTAInfo", expectPass == pass, otaCaseIdString);
        return actualExpectedResult;
    }

    boolean setEthUdsLog(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.EthernetRegexRule).SET_ETH_UDS_LOG"})
    default ActualExpectedResult setEthUdsLogInfo(int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setEthUdsLog(commandId);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行ETH诊断Log:{},检测结果:{},共耗时:{}毫秒", commandId == 0 ? "开始录制" : "结束录制", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setEthUdsLogInfo", pass, commandId);
        return actualExpectedResult;
    }
}
