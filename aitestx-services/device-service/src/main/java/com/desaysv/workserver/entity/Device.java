package com.desaysv.workserver.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ExceptionUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备基类
 */
//TODO: Device尝试合并到TestDevice @see com.desaysv.workserver.domain.TestDevice
@Slf4j
public abstract class Device extends OperationTarget implements DeviceCallback {
    //TODO:改成端口管理器
    @Getter
    @JSONField(serialize = false)
    private static final Map<String, Integer> deviceModelIndexMap = new ConcurrentHashMap<>();

    @Getter
    @JSONField(serialize = false)
    private static final Map<String, LinkedHashMap<Integer, Device>> deviceInstancesMap = new ConcurrentHashMap<>();

    @JSONField(serialize = false)
    private final static Map<String, String> portMappings = new ConcurrentHashMap<>(); //portName:deviceModel

    @JSONField(serialize = false)
    @Getter
    @Setter
    private DeviceCallback deviceCallback; //设备回调

    //    private Integer deviceChannel; //设备通道，如CAN通道1，CAN通道2
    //    private String portName; //设备端口名
    //    private String deviceAliasName; //设备别名
    private String deviceUniqueCode; //设备唯一码
    private Integer id; //数据库编号
    private Integer baudRate; //波特率
    private Integer sampleRate; //采样率(音频设备)
    private String deviceName; //设备自定义名
    private String userDeviceName; //用户自定义名称
    private Integer devicePort; //相机和CAN专用
    private boolean connected; //是否打开
    private boolean simulated = false;//设备是否模拟
    private int numberChannels; //设备通道数量
    private DeviceOperationParameter deviceOperationParameter; //设备参数

    public int getDefaultChannel() {
        return 1;
    }

    @Override
    public Integer getChannel() {
        Integer channel = super.getChannel(); // 调用父类的 getChannel()
        return channel == null ? getDefaultChannel() : channel;
    }

    public Device() {
        this(new DeviceOperationParameter());
    }

    public Device(DeviceOperationParameter deviceOperationParameter) {
        this.deviceOperationParameter = deviceOperationParameter;
        setDeviceCallback(getDevice());
    }

    @JSONField(serialize = false)
    public static int getDeviceModelIndex(String deviceModel) {
        return getDeviceModelIndexMap().getOrDefault(deviceModel, 0) + 1;
    }

    /**
     * 添加设备映射
     */
    public void addDeviceMapping() {
        String deviceModel = getDeviceModel();
        // 获取当前该型号设备的数量，如果不存在则初始化为0
        Integer count = getDeviceModelIndexMap().getOrDefault(deviceModel, 0);
        // 增加该型号设备的数量
        count += 1;
        // 更新设备型号的数量
        deviceModelIndexMap.put(deviceModel, count);
    }

    /**
     * 删除设备映射
     *
     * @return 当前设备型号的剩余数量
     */
    protected int removeDeviceMapping() {
        removeFromDeviceInstancesMap();
        String deviceModel = getDeviceModel();
        // 获取当前该型号设备的数量
        Integer count = deviceModelIndexMap.getOrDefault(deviceModel, 0);
        // 减少该型号设备的数量，确保不会小于0
        count = Math.max(0, count - 1);
        // 如果数量为0，则移除该型号的记录，否则更新数量
        if (count == 0) {
            deviceModelIndexMap.remove(deviceModel);
        } else {
            deviceModelIndexMap.put(deviceModel, count);
        }
        return count;
    }

    /**
     * 对于已打开的设备调用此方法
     */
    private void addToDeviceInstancesMap() {
        String deviceType = getDeviceType();
        if (!deviceInstancesMap.containsKey(deviceType)) {
            deviceInstancesMap.put(deviceType, new LinkedHashMap<>());
        }
        Map<Integer, Device> map = deviceInstancesMap.get(deviceType);
        map.put(getDeviceIndex(), this);
    }


    /**
     * 对于关闭的设备调用此方法
     */
    private void removeFromDeviceInstancesMap() {
        String deviceType = getDeviceType();
        Map<Integer, Device> instancesMap = deviceInstancesMap.get(deviceType);
        if (instancesMap != null) {
            instancesMap.remove(getDeviceIndex());
        }
    }


    /**
     * 对于已注册的设备调用此方法
     */
    public void addExtraParams(String deviceType) {
        if (!getDeviceInstancesMap().containsKey(deviceType)) {
            getDeviceInstancesMap().put(deviceType, new LinkedHashMap<>());
        }
        Map<Integer, Device> map = getDeviceInstancesMap().get(deviceType);
        map.put(map.size() + 1, this);
    }

    @JSONField(serialize = false)
    public Device getDevice() {
        return this;
    }

    @JSONField(serialize = false)
    public boolean isVISAProtocol() {
        return isVISAProtocol(getDeviceName());
    }

    @JSONField(serialize = false)
    public static boolean isVISAProtocol(String deviceName) {
        return deviceName.endsWith("::INSTR");
    }

    public abstract String getDeviceType();

    public abstract String getDeviceModel();

    //Getter & Setter

    public int getNumberChannels() {
        return getDevice().numberChannels;
    }

    public void setNumberChannels(int numberChannels) {
        getDevice().numberChannels = numberChannels;
    }

    public Integer getId() {
        return getDevice().id;
    }

    public void setId(Integer id) {
        getDevice().id = id;
    }

//    public Integer getDeviceChannel() {
//        return getDevice().deviceChannel;
//    }

//    public void setDeviceChannel(Integer deviceChannel) {
//        getDevice().deviceChannel = deviceChannel;
//    }

    public String getDeviceName() {
        return getDevice().deviceName;
    }

    public String getUserDeviceName() {
        return getDevice().userDeviceName;
    }

    public void setDeviceName(String deviceName) {
        getDevice().deviceName = deviceName.replaceAll("\\s+", "_");
    }

    public Integer getDevicePort() {
        return getDevice().devicePort;
    }

    public String getDevicePortName() {
//        if (getDevice().portName != null) {
//            return getDevice().portName;
//        }
        if (getDevicePort() == null) {
            return null;
        }
        return String.format("COM%d", getDevicePort());
    }

    public void setUserDeviceName(String userDeviceName) {
        getDevice().userDeviceName = userDeviceName;
    }

    public void setDevicePort(Integer devicePort) {
        getDevice().devicePort = devicePort;
    }

    public Integer getBaudRate() {
        return getDevice().baudRate;
    }

    public void setBaudRate(Integer baudRate) {
        getDevice().baudRate = baudRate;
    }

    public Integer getSampleRate() {
        return getDevice().sampleRate;
    }

    public void setSampleRate(Integer sampleRate) {
        getDevice().sampleRate = sampleRate;
    }

    public boolean isConnected() {
        return getDevice().connected;
    }

    public void setConnected(boolean connected) {
        getDevice().connected = connected;
    }

//    public String getDeviceAliasName() {
//        if (getDevice().deviceAliasName == null) {
//            getDevice().setDeviceAliasName(getDevice().getDeviceName());
//        }
//        return getDevice().deviceAliasName;
//    }

//    public void setDeviceAliasName(String deviceAliasName) {
//        getDevice().deviceAliasName = deviceAliasName;
//        getDevice().setAliasName(deviceAliasName);
//    }

    public String getDeviceUniqueCode() {
        return getDevice().deviceUniqueCode;
    }

    public void setDeviceUniqueCode(String deviceUniqueCode) {
        getDevice().deviceUniqueCode = deviceUniqueCode;
    }

    public boolean isSimulated() {
        return getDevice().simulated;
    }

    public void setSimulated(boolean simulated) {
        getDevice().simulated = simulated;
    }

    public DeviceOperationParameter getDeviceOperationParameter() {
        return getDevice().deviceOperationParameter;
    }

    public void setDeviceOperationParameter(DeviceOperationParameter deviceOperationParameter) {
        getDevice().deviceOperationParameter = deviceOperationParameter;
    }

    /**
     * 自动打开设备
     *
     * @return
     * @throws DeviceOpenException
     * @throws DeviceOpenRepeatException
     */
    public boolean autoOpen() throws DeviceOpenException, DeviceOpenRepeatException {
        return open();
    }

    /**
     * 打开设备
     *
     * @return 是否打开设备成功
     */
    public abstract boolean open() throws DeviceOpenException, DeviceOpenRepeatException;

    /**
     * 关闭设备
     *
     * @return 是否关闭设备成功
     */
    public abstract boolean close() throws DeviceCloseException;

    /**
     * 清理设备资源
     *
     * @return
     */
    public boolean finalClean() {
        return true;
    }


    /**
     * 锁定设备端口号
     */
    public void lock() {
        if (isAllowLock()) {
            portMappings.put(getDeviceName(), getDeviceModel());
        }
    }

    protected boolean isAllowLock() {
        return true;
    }

    /**
     * 解锁设备端口号
     */
    public void unlock() {
        portMappings.remove(getDeviceName());
    }

    public static boolean isLocked(String portNameOrId, String deviceModel) {
        String mappingDeviceModel = portMappings.get(portNameOrId);
        if (mappingDeviceModel == null) {
            return false;
        }
        return deviceModel != null && !mappingDeviceModel.equals(deviceModel);
    }

    /**
     * 处理设备打开事件
     *
     * @param isOpenSuccess 是否设备打开成功
     */
    public void handleOpenEvent(boolean isOpenSuccess) {
        getDevice().getDeviceCallback().openCompleted(isOpenSuccess);
    }


    /**
     * 处理设备关闭事件
     *
     * @param isCloseSuccess 是否设备关闭成功
     */
    public void handleCloseEvent(boolean isCloseSuccess) {
        getDevice().getDeviceCallback().closeCompleted(isCloseSuccess);
    }

    /**
     * 默认自动打开设备
     *
     * @return
     */
    public OperationResult openForOperationResult() {
        return openForOperationResult(true);
    }

    public OperationResult openForOperationResult(boolean autoOpen) {
        OperationResult operationResult = new OperationResult();
        try {
//            MyLogger.outputServerLog(String.format("正在打开设备\"%s\"", getDeviceName()));
            log.info("打开设备信息:{}", this);
            if (isSimulated()) {
                log.info("{}设备正在模拟", getDeviceName());
            }
            boolean isOpenOk = autoOpen ? autoOpen() : open();
            if (isOpenOk) {
                log.info("成功连接设备:{}", getDeviceName());
                lock();
                addToDeviceInstancesMap();
                setConnected(true);
            }
            handleOpenEvent(isOpenOk);
            return isOpenOk ? operationResult.ok() : operationResult.fail(String.format("设备%s连接失败", getDeviceName()));
        } catch (DeviceOpenRepeatException e) {
            return operationResult.ok();
        } catch (Exception e) {
            handleOpenEvent(false);
            log.warn(e.getMessage(), e);
            return operationResult.fail(String.format("设备%s连接异常:%s", getDeviceName(), e.getMessage()));
        }
    }


    public OperationResult closeForOperationResult() throws DeviceCloseException {
        OperationResult operationResult = new OperationResult();
        try {
            boolean isCloseOk = close();
            if (isCloseOk) {
                log.info("成功断开设备:{}", getDeviceName());
            }
            handleCloseEvent(isCloseOk);
            return isCloseOk ? operationResult.ok() : operationResult.fail(String.format("设备%s断开失败", getDeviceName()));
        } catch (Exception e) {
            handleCloseEvent(false);
            String message = String.format("设备%s断开异常:%s", getDeviceName(), e.getMessage());
            log.error(message, e);
            throw new DeviceCloseException(ExceptionUtils.getStackTrace(e));
        } finally {
            unlock();
            removeDeviceMapping();
            setConnected(false);
        }
    }

    /**
     * 用于释放资源
     */
    public OperationResult disposeForOperationResult() {
        OperationResult operationResult = new OperationResult();
        // 释放设备资源
        dispose();
        return operationResult.ok();
    }

    @Override
    public String toString() {
        return getDevice().getClass().getSimpleName() + "{" +
                "id=" + getDevice().getId() +
                ", channel=" + getDevice().getChannel() +
                ", aliasName='" + getDevice().getAliasName() + '\'' +
                ", devicePort=" + getDevice().getDevicePort() +
                ", baudRate=" + getDevice().getBaudRate() +
                ", sampleRate=" + getDevice().getSampleRate() +
                ", deviceType='" + getDevice().getDeviceType() + '\'' +
                ", deviceModel='" + getDevice().getDeviceModel() + '\'' +
                ", deviceName='" + getDevice().getDeviceName() + '\'' +
                ", userDeviceName='" + getDevice().getUserDeviceName() + '\'' +
                ", deviceUniqueCode='" + getDevice().getDeviceUniqueCode() + '\'' +
                ", deviceOperationParameter='" + getDevice().getDeviceOperationParameter() + '\'' +
                ", simulated=" + getDevice().isSimulated() +
                '}';
    }

}
