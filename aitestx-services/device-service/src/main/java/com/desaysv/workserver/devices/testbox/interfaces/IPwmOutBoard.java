package com.desaysv.workserver.devices.testbox.interfaces;

import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.PWMEntity;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IPwmOutBoard {
    Logger log = LogManager.getLogger(IPwmOutBoard.class.getSimpleName());

    /**
     * 写入PWM输出（10个通道）
     *
     * @param deviceChannel 板卡通道
     * @param pwmEntity
     */
    boolean writePWMOutputBoardCard(Integer deviceChannel, PWMEntity pwmEntity) throws BoardCardTransportException;

    PWMEntity fetchPWMOutputBoardCardByChannel(int deviceChannel) throws BoardCardTransportException;


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TestBoxRegexRule).CHANGE_PWM_OUT_VALUE"})
    default void writePwmOut(Integer deviceChannel, float frequency, float duty, String physicalMeaning) throws OperationFailNotification {
        log.info("PWM输出板卡通道{}{}设置频率{}HZ和占空比{}%", deviceChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                frequency, duty);
        PWMEntity pwmEntity = new PWMEntity(frequency, duty);
        try {
            boolean isWriteOk = writePWMOutputBoardCard(deviceChannel, pwmEntity);
            log.info("PWM输出板卡通道{}{}设置频率{}HZ和占空比{}%--{}", deviceChannel,
                    StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                    frequency, duty, isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("PWM输出板卡通道%s设置频率%sHz和占空比%s%%--%s", deviceChannel
                    , frequency, duty, isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("PWM输出板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("PWM输出板卡读取失败:" + e.getMessage());
        }
    }
}
