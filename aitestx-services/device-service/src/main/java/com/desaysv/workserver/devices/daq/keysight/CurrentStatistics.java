package com.desaysv.workserver.devices.daq.keysight;

import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 电流波动统计
 * @date: 2025/5/30 14:16
 */
@Data
public class CurrentStatistics {
    private float maxCurrent;
    private float minCurrent;
    private float avgCurrent;
    private boolean stable;
    private int count;

    public CurrentStatistics(float maxCurrent, float minCurrent, float avgCurrent, boolean stable, int count) {
        this.maxCurrent = maxCurrent;
        this.minCurrent = minCurrent;
        this.avgCurrent = avgCurrent;
        this.stable = stable;
        this.count = count;
    }


}
