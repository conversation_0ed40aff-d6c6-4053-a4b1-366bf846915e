package com.desaysv.workserver.devices.serial;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;

public class SaleaeLogicClient {
    private final Socket socket;
    private final BufferedReader in;
    private final PrintWriter out;

    public SaleaeLogicClient(String host, int port) throws IOException {
        socket = new Socket(host, port);
        in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        out = new PrintWriter(socket.getOutputStream(), true);
    }

    public String sendCommand(String command) throws IOException {
        out.println(command);
        return in.readLine();
    }

    public void close() throws IOException {
        in.close();
        out.close();
        socket.close();
    }

//    public static void main(String[] args) {
//        try {
//            SaleaeLogicClient client = new SaleaeLogicClient("localhost", 10429);
//            String response = client.sendCommand("START");
//            if (!response.equals("ACK")) {
//                client.close();
//                return;
//            }
//            Thread.sleep(5000);
//            response = client.sendCommand("STOP");
//            if (!response.equals("ACK")) {
//                client.close();
//                return;
//            }
//            String savePath = "/path/to/save/file.sal";
//            response = client.sendCommand("EXPORT_DATA2, " + savePath + ", logicdata, binary, 1000000000, 0");
//            if (!response.equals("ACK")) {
//                client.close();
//                return;
//            }
//
//            client.close();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//    }
}