package com.desaysv.workserver.devices.bus.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CANSignal {

    /**
     * DBCIndex
     */
    private int dbcIndex;
    /**
     * UUID(父类用于关联)
     */
    private String CANMessageID;
    /**
     * 信号名
     */
    private String signalName;

    /**
     * 原始值(Hex)
     */
    private String originalValue;

    /**
     * 实际值
     */
    private double actualValue;

    /**
     * 初始值
     */
    private double initialValue;

    /**
     * 值描述
     */
    private String valueRepresentation;
    /**
     * 值描述
     */
    private List<CANSignalLabel> canSignalLabels;

    /**
     * 单位
     */
    private String unit;


    /**
     * 变换比例
     */
    private double translateScale;

    /**
     * 变换偏移
     */
    private double offsetTransform;

    /**
     * 起始位
     */
    private int startBit;

    /**
     * 位宽
     */
    private int bitWide;

    /**
     * 注释
     */
    private String remark;

    /**
     * 最大值
     */
    private int min;
    /**
     * 最小值
     */
    private int max;

    /**
     * 标签
     */
    private String label;

    /**
     * functionType(E2E)
     */
    private String sigFuncType;

    /**
     * dataId(E2E)
     */
    private String sigDataID;

    // 是否已经移动到主面板
    private boolean movedToMainPanel;
    // 显示标签
    private boolean showLabel;
    // 锁定信号
    private boolean locked;
    // 信号类型
    private Signal.SignalType signalType;
    // 开关类型值
    private Boolean switchState;    // 开关状态
    private Integer pressedValue;   // 按下时的值
    private Integer releasedValue;  // 释放时的值
    private boolean controlRelayEnabled;// 信号控制继电器开关
    private Map<String,Map<String, Boolean>> relayStates; //通道对应状态
}
