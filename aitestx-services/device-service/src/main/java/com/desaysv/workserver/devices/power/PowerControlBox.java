package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.PowerControlBoxProtocol;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * 电源控制盒
 */
@Slf4j
public class PowerControlBox extends DefaultPower {
    public PowerControlBox() {
        this(new DeviceOperationParameter());
    }

    public PowerControlBox(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        PowerControlBoxProtocol powerProtocol = new PowerControlBoxProtocol(this);
        setPowerProtocol(powerProtocol);
    }

    @Override
    public int getNumberChannels() {
        return 4;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.POWER_BOX;
    }

    public static void main(String[] args) {
        PowerControlBox powerControlBox = new PowerControlBox(null);
        try {
            powerControlBox.setDeviceName("com19");
            powerControlBox.setBaudRate(9600);
            powerControlBox.setDevicePort(19);
            powerControlBox.open();
        } catch (DeviceOpenException e) {
            log.error(e.getMessage(), e);
        }
        int channel = 3;
        powerControlBox.outputOff(channel);
        powerControlBox.outputOn(channel);
    }

}
