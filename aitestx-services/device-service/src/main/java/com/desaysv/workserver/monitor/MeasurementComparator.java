package com.desaysv.workserver.monitor;

import com.desaysv.workserver.GlobalConfigHolder;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.IFetchDevice;
import com.desaysv.workserver.entity.BooleanComparator;
import com.desaysv.workserver.entity.MonitorDataPackage;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.utils.StrUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.*;

/**
 * 测量比较器（电压、电流等）
 */

public interface MeasurementComparator extends IFetchDevice {

    Logger log = LoggerFactory.getLogger(MeasurementComparator.class.getSimpleName());

    default BooleanComparator compare(String condition, float value, String textPattern) {
        BooleanComparator comparator = new BooleanComparator();

        if (condition.contains("&")) {
            String expr1 = condition.split("&")[0];
            String expr2 = condition.split("&")[1];
            boolean r1 = StrUtils.judgeExpressResult(value + expr1);
            boolean r2 = StrUtils.judgeExpressResult(value + expr2);
            comparator.setPass(r1 && r2);
        } else {
            comparator.setPass(StrUtils.judgeExpressResult(value + condition));
        }

        if (!comparator.isPass()) {
            comparator.setMessage(String.format(textPattern + ", 不符合条件%s", value, condition));
        } else {
            comparator.setMessage(String.format(textPattern + ", 符合条件%s", value, condition));
        }
        return comparator;
    }

    default BooleanComparator compareVoltage(String condition, float voltage) {
        BooleanComparator comparator;
        try {
            comparator = compare(condition, voltage, "当前电压%fV");
        } catch (Exception e) {
            comparator = new BooleanComparator();
            comparator.setPass(false);
            comparator.setMessage(e.getMessage());
        }
        return comparator;
    }

    default BooleanComparator compareCurrent(String condition, float current) {
        BooleanComparator comparator;
        try {
            comparator = compare(condition, current, "当前电流%fA");
        } catch (Exception e) {
            comparator = new BooleanComparator();
            comparator.setPass(false);
            comparator.setMessage(e.getMessage());
        }
        return comparator;
    }

    default OperationResult measurementMonitor(Integer deviceChannel, MeasureIndicator measureIndicator, MonitorDataPackage monitorDataPackage) {
        long duration = monitorDataPackage.getDuration() * 1000L;
        int interval = monitorDataPackage.getInterval() <= 0 ? 1 : monitorDataPackage.getInterval();
        long startTime = System.currentTimeMillis();
        BooleanComparator comparator = new BooleanComparator();
        comparator.setPass(false);
        long deltaTime = 0;
        OperationResult operationResult = new OperationResult();
        while (deltaTime <= duration) {
            if (measureIndicator.equals(MeasureIndicator.VOLTAGE)) {
                comparator = compareVoltage(deviceChannel, monitorDataPackage);
            } else if (measureIndicator.equals(MeasureIndicator.CURRENT)) {
                comparator = compareCurrent(deviceChannel, monitorDataPackage);
            } else {
                comparator.setMessage("测量类型错误");
                break;
            }
            if (monitorDataPackage.isStopOnCondition()) {
                if (comparator.isPass()) {
                    //满足条件后，退出
                    log.info("{}，退出监控", comparator.getMessage());
                    break;
                }
            } else {
                if (!comparator.isPass()) {
                    //条件不符合，退出
                    log.info("{}，退出监控", comparator.getMessage());
                    break;
                }
            }
            log.info("测量比较器:{}", comparator);
            try {
                Thread.sleep(interval * 1000L);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                break;
            }
            deltaTime = System.currentTimeMillis() - startTime;
        }
        if (monitorDataPackage.isStopOnCondition() && !comparator.isPass()) {
            log.info("超时时间{}s内还未符合条件:{}", monitorDataPackage.getDuration(), monitorDataPackage.getCondition());
        }
        operationResult.setOk(comparator.isPass());
        operationResult.setMessage(comparator.getMessage());
        operationResult.setData(comparator);
        return operationResult;
    }

    /**
     * 比较电压
     *
     * @param monitorDataPackage 监控数据
     * @return 布尔比较器
     */
    default BooleanComparator compareVoltage(Integer deviceChannel, MonitorDataPackage monitorDataPackage) {
        try {
            float voltage = fetchVoltage(deviceChannel, monitorDataPackage.getCurrentDirection());
            String condition = monitorDataPackage.getCondition();
            BooleanComparator comparator = compareVoltage(condition, voltage);
            log.info("电压比较{}的结果:{}", condition, comparator);
            return comparator;
        } catch (DeviceReadException e) {
            BooleanComparator booleanComparator = new BooleanComparator();
            booleanComparator.setPass(false);
            booleanComparator.setMessage(e.getMessage());
            return booleanComparator;
        }
    }

    /**
     * 比较电流
     *
     * @param monitorDataPackage 监控数据
     * @return 布尔比较器
     */
    default BooleanComparator compareCurrent(Integer deviceChannel, MonitorDataPackage monitorDataPackage) {
        float current;
        try {
            current = fetchCurrent(deviceChannel, monitorDataPackage.getCurrentDirection());
            String condition = monitorDataPackage.getCondition();
            BooleanComparator comparator = compareCurrent(condition, current);
            log.info("电流比较{}的结果:{}", condition, comparator);
            return comparator;
        } catch (DeviceReadException e) {
            BooleanComparator booleanComparator = new BooleanComparator();
            booleanComparator.setPass(false);
            booleanComparator.setMessage(e.getMessage());
            return booleanComparator;
        }
    }

    /**
     * 监控电压
     *
     * @param monitorDataPackage 监控数据
     * @return 操作结果
     */
    default OperationResult monitorVoltage(Integer deviceChannel, MonitorDataPackage monitorDataPackage) throws DeviceReadException {
        OperationResult operationResult = measurementMonitor(deviceChannel, MeasureIndicator.VOLTAGE, monitorDataPackage);
        // 异步执行 recordPower
        CompletableFuture.runAsync(() -> {
            try {
                recordPower(operationResult, deviceChannel, monitorDataPackage.getCurrentDirection(), true);
            } catch (Exception e) {
                log.error("记录电压时出错: {}", e.getMessage(), e);
            }
        });
        return operationResult;
    }

    /**
     * 监控电流
     *
     * @param monitorDataPackage 监控数据
     * @return 操作结果
     */
    default OperationResult monitorCurrent(Integer deviceChannel, MonitorDataPackage monitorDataPackage) throws DeviceReadException {
        OperationResult operationResult = measurementMonitor(deviceChannel, MeasureIndicator.CURRENT, monitorDataPackage);
        // 异步执行 recordPower
        CompletableFuture.runAsync(() -> {
            try {
                recordPower(operationResult, deviceChannel, monitorDataPackage.getCurrentDirection(), false);
            } catch (Exception e) {
                log.error("记录电流时出错: {}", e.getMessage(), e);
            }
        });
        return operationResult;
    }

    /**
     * 记录电压/电流
     *
     * @param
     * @return void
     */
    default void recordPower(OperationResult operationResult, Integer deviceChannel, String currentDirection, boolean isVoltage) {
        if (operationResult.isFailed() && GlobalConfigHolder.getGlobalConfig().isRecordPower()) {
            int recordPowerDuration = GlobalConfigHolder.getGlobalConfig().getRecordPowerDuration();
            if (recordPowerDuration > 0) {
                String isVoltageStr = isVoltage ? "电压" : "电流";
                String isVoltageVAStr = isVoltage ? "V" : "A";
                String project = GlobalConfigHolder.getGlobalConfig().getRecordPowerProject();
                String path = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\devices\\power\\", project);
                // 自定义时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH：mm：ss");
                String fileName = Instant.now().atZone(ZoneId.of("UTC+8")).toLocalDateTime().format(formatter1) + " 后" + recordPowerDuration + "s的" + isVoltageStr + ".txt";
                // 创建目录
                try {
                    Files.createDirectories(Paths.get(path));
                } catch (IOException e) {
                    log.error("创建目录失败: {}", e.getMessage(), e);
                    return;
                }

                // 使用绝对路径来创建文件
                Path filePath = Paths.get(path, fileName);

                // 使用 try-with-resources 外部化 BufferedWriter
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath.toString(), true))) {
                    ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

                    // 安排定时任务，每秒调用一次 fetchVoltage 或 fetchCurrent 方法来获取电压/电流
                    ScheduledFuture<?> scheduledTask = scheduler.scheduleAtFixedRate(() -> {
                        try {
                            double value = isVoltage ? fetchVoltage(deviceChannel, currentDirection) : fetchCurrent(deviceChannel, currentDirection);
                            writer.write(Instant.now().atZone(ZoneId.of("UTC+8")).toLocalDateTime().format(formatter) + "\t" + value + isVoltageVAStr + "\n");
                            writer.flush(); // 确保数据已写入磁盘
                        } catch (DeviceReadException | IOException e) {
                            log.error("获取电源信息时出错: {}", e.getMessage(), e);
                        }
                    }, 0, 1, TimeUnit.SECONDS);
                    log.info("监控电流/电源信息失败！！！电流/电源信息在后台录制中，请等待{}S....", recordPowerDuration);
                    // 使用调度器安排停止记录的任务
                    scheduler.schedule(() -> stopRecording(scheduler, writer, scheduledTask), recordPowerDuration, TimeUnit.SECONDS);
                    // 等待所有任务完成
                    scheduler.awaitTermination(recordPowerDuration + 1, TimeUnit.SECONDS); // 给多一秒时间确保任务完成
                    log.info("监控电流/电源信息失败后，电流/电源信息录制在文件：{}", filePath);
                } catch (IOException e) {
                    log.error("创建文件或写入文件失败: {}", e.getMessage(), e);
                } catch (InterruptedException e) {
                    log.error("等待任务完成时中断: {}", e.getMessage(), e);
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    default void stopRecording(ScheduledExecutorService scheduler, BufferedWriter writer, ScheduledFuture<?> scheduledTask) {
        try {
            if (scheduledTask != null && !scheduledTask.isCancelled()) {
                scheduledTask.cancel(true);
            }
            if (writer != null) {
                writer.close();
            }
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        } catch (IOException e) {
            log.error("停止电源记录时出错: {}", e.getMessage(), e);
        }
    }
}
