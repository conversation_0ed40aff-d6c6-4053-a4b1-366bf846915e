package com.desaysv.workserver.devices.bus.base.can;

import cantools.dbc.DecodedSignal;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.devices.bus.base.BusError;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CAN 信号检测器
 * 负责 CAN 信号的检测和解析功能
 */
@Slf4j
public class CanSignalInspector {

    /**
     * 检测 CAN 信号
     *
     * @param canBus                    CAN 总线对象
     * @param deviceChannel             设备通道
     * @param canSignalInspectorRequest CAN 检测参数
     * @return 操作结果
     * @throws BusError 总线错误
     */
    public static OperationResult inspectSignal(CanBus canBus, Integer deviceChannel, CanSignalInspectorRequest canSignalInspectorRequest) throws BusError {
        OperationResult operationResult = new OperationResult();

        if (canSignalInspectorRequest == null) {
            return operationResult.fail("CAN信号检测参数为空");
        }

        if (canSignalInspectorRequest.getMessageName() == null || canSignalInspectorRequest.getMessageName().trim().isEmpty()) {
            return operationResult.fail("报文名称不能为空");
        }

        if (canSignalInspectorRequest.getSignalMeasurements() == null || canSignalInspectorRequest.getSignalMeasurements().isEmpty()) {
            return operationResult.fail("信号组不能为空");
        }

        try {
            // 获取CAN配置
            CanConfig deviceConfig = canBus.getDeviceConfig();
            if (deviceConfig == null) {
                return operationResult.fail("设备配置未找到");
            }

            // 获取DBC配置
            DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
            if (dbcConfig == null) {
                return operationResult.fail(String.format("通道 %d 的DBC配置未找到", deviceChannel));
            }

            // 解析信号值范围
            Map<String, SignalValueRange> stringSignalValueRangeMap = parseSignalValueRange(canSignalInspectorRequest.getSignalMeasurements());
            if (stringSignalValueRangeMap == null) {
                return operationResult.fail("无法解析信号组: " + canSignalInspectorRequest.getSignalMeasurements());
            }

            log.info("{}开始检测通道{}的CAN信号: 报文名称={}, 信号组={}",
                    canBus.getDeviceName(), deviceChannel, canSignalInspectorRequest.getMessageName(), canSignalInspectorRequest.getSignalMeasurements());

            // 从总线读取信号值
            return detectSignalValue(canBus, deviceChannel, canSignalInspectorRequest, stringSignalValueRangeMap);

        } catch (Exception e) {
            log.error("CAN信号检测出错", e);
            return operationResult.fail("CAN信号检测出错: " + e.getMessage());
        }
    }

    /**
     * 解析信号值范围，支持三种格式：
     * 1. 精确值，如 "30"
     * 2. 范围格式，如 "20~30"
     * 3. 偏差格式，如 "30±2" 或 "30+-2"
     *
     * @param signalMeasurements 信号测试组
     * @return 信号值范围对象，如果解析失败返回null
     */
    private static Map<String, SignalValueRange> parseSignalValueRange(List<SignalMeasurement> signalMeasurements) {
        if (signalMeasurements == null || signalMeasurements.isEmpty()) {
            return null;
        }
        Map<String, SignalValueRange> stringSignalValueRangeMap = new HashMap<>();
        for (SignalMeasurement signalMeasurement : signalMeasurements) {
            stringSignalValueRangeMap.put(signalMeasurement.getSignalName(), extractSignalValueRange(signalMeasurement.getSignalValue()));
        }
        return stringSignalValueRangeMap;
    }

    private static SignalValueRange extractSignalValueRange(String signalValue) {
        // 移除所有空白字符
        String normalizedValue = signalValue.replaceAll("\\s+", "");
        Double minValue = null;
        Double maxValue = null;

        // 处理范围格式 (如 "20~30")
        if (normalizedValue.contains("~")) {
            String[] range = normalizedValue.split("~");
            if (range.length == 2) {
                try {
                    minValue = Double.parseDouble(range[0]);
                    maxValue = Double.parseDouble(range[1]);
                } catch (NumberFormatException e) {
                    log.error("信号值范围格式不正确: {}", normalizedValue, e);
                    return null;
                }
            }
        }
        // 处理偏差格式 (如 "30+-2" 或 "30±2")
        else if (normalizedValue.contains("+-") || normalizedValue.contains("±")) {
            String[] parts = normalizedValue.contains("+-") ? normalizedValue.split("\\+-") : normalizedValue.split("±");
            if (parts.length == 2) {
                try {
                    double baseValue = Double.parseDouble(parts[0]);
                    double deviation = Double.parseDouble(parts[1]);
                    minValue = baseValue - deviation;
                    maxValue = baseValue + deviation;
                } catch (NumberFormatException e) {
                    log.error("信号值偏差格式不正确: {}", normalizedValue, e);
                    return null;
                }
            }
        }
        // 处理精确值
        else {
            try {
                double exactValue = Double.parseDouble(normalizedValue);
                minValue = exactValue;
                maxValue = exactValue;
            } catch (NumberFormatException e) {
                log.error("信号值格式不正确: {}", normalizedValue, e);
                return null;
            }
        }

        if (minValue == null) {
            return null;
        }

        return new SignalValueRange(minValue, maxValue);
    }

    /**
     * 从总线上检测信号值
     *
     * @param canBus                    CAN总线对象
     * @param deviceChannel             设备通道
     * @param canSignalInspectorRequest CAN检测参数
     * @param stringSignalValueRangeMap 信号值组
     * @return 操作结果
     */
    private static OperationResult detectSignalValue(CanBus canBus,
                                                     Integer deviceChannel,
                                                     CanSignalInspectorRequest canSignalInspectorRequest,
                                                     Map<String, SignalValueRange> stringSignalValueRangeMap) {
        OperationResult operationResult = new OperationResult();
        try {
            // 接收CAN报文
            Map<String, DecodedSignal> canSignalsMap = canBus.fetchAllCanSignalValue(deviceChannel, canSignalInspectorRequest.getMessageName());
            StringBuilder sb = new StringBuilder();
            if (canSignalsMap.isEmpty()) {
                operationResult.setOk(false);
                operationResult.setMessage(String.format("无法从dbc解析%s包含的具体信号名", canSignalInspectorRequest.getMessageName()));
            } else {
                boolean ok = true;
                for (Map.Entry<String, SignalValueRange> entry : stringSignalValueRangeMap.entrySet()) {
                    //查找获取的具体信号值
                    String signalName = entry.getKey();
                    DecodedSignal decodedSignal = canSignalsMap.get(signalName);
                    if (decodedSignal != null) {
                        double signalActualValue = decodedSignal.getPhyValue().doubleValue();
                        boolean pass = signalActualValue >= entry.getValue().getMinValue() && signalActualValue <= entry.getValue().getMaxValue();
                        // 判断信号值是否在范围内
                        if (pass) {
                            sb.append(String.format(
                                    "检测到信号 %s.%s 值为 %.2f，在期望范围 [%.2f, %.2f] 内",
                                    canSignalInspectorRequest.getMessageName(), signalName, signalActualValue, entry.getValue().getMinValue(), entry.getValue().getMaxValue())).append("\n");
                        } else {
                            ok = false;
                            sb.append(String.format(
                                    "检测到信号 %s.%s 值为 %.2f，不在期望范围 [%.2f, %.2f] 内",
                                    canSignalInspectorRequest.getMessageName(), signalName, signalActualValue, entry.getValue().getMinValue(), entry.getValue().getMaxValue())).append("\n");
                        }
                    }
                }
                operationResult.setOk(ok);
                operationResult.setMessage(sb.toString());
                log.info(sb.toString());
            }
        } catch (BusError e) {
            operationResult.setOk(false);
            operationResult.setMessage(e.getMessage());
        }
        return operationResult;

    }

    /**
     * 信号值范围内部类
     */
    @Getter
    private static class SignalValueRange {
        private final Double minValue;
        private final Double maxValue;

        public SignalValueRange(Double minValue, Double maxValue) {
            this.minValue = minValue;
            this.maxValue = maxValue;
        }

    }
}