package com.desaysv.workserver.devices.signal_generator;

public class TKSCPICommands {

    //*RST 该命令将仪器设置重置为出厂默认设置。
    public static final String RESET_DEVICE = "*RST";

    /**设置输出的波形类型
     * 示例：SOURce1:FUNCtion:SHAPe SINusoid 设置CHI输出波形类型为正弦波。
     * SOURce1:FUNCtion:SHAPe SQUare 设置CHI输出波形类型为方波。
     * SOURce1:FUNCtion:SHAPe RAMP 设置CHI输出波形类型为斜坡波。
     * SOURce1:FUNCtion:SHAPe PULSe 设置CHI输出波形类型为脉冲波。
     * SOURce1:FUNCtion:SHAPe ARB  设置CHI输出波形类型为任意波形。
     * SOURce1:FUNCtion:SHAPe NOISe 设置CHI输出波形类型为噪声。
     */
    public static final String SOURCE_FUNCTION_SHAPE = "SOURce%d:FUNCtion:SHAPe %s";

    /**
     * 描述：该命令用于设置或查询指定通道的输出波形频率，也可以来设置周期。
     * 语法
     * [SOURce[112]] : FREQuency
     * [:CW|:FIXed]
     * {<频率>|最小值|最大值}
     * [SOURCe[1|2]]:FREQuency[:CWI:FIXed]?{最小值|最大值]
     *  示例：SOURce1:FREQuency:FIXed 500kHz
     *   当运行模式设置为扫频以外时,将CHI输出频率设置为500kHz。
     *   周期与频率的关系为T=-,其中T是周期,f是频率。
     */
    public static final String SOURCE_FREQUENCY_FIXED = "SOURce%d:FREQuency:FIXed %fHz";

    /**
     * 描述：该命令用于设置或查询指定通道的输出波形占空比，只有方波和脉冲波有占空比。
     * 语法
     * SOURCE1:PULSe:DCYCle 50
     *  将CHI输出占空比设置为50%。
     *
     *  占空比(DutyCycle)这个概念主要适用于那些具有周期性开关或者脉冲性
     * 质的波形,即在一个周期内,波形在某个特定电平(通常是高电平)上停留的时间与整个周期时间的比
     * 例。因此,占空比通常与以下波形相关:
     * 1.方波(SquareWave):在一个周期内,方波有一半的时间处于高电平,另一半时间处于低电平,因此占空比通常为50%。
     * 2.脉冲波(PulseWave):脉冲波形在周期内的某个时间段P为是高电平,其余时间为低电平,因此可以根据需要调整占空比。
     */
    public static final String SOURCE_PULSE_DUTY = "SOURce%d:PULSe:DCYCle %s";

    /**
     * 描述：该命令用于设置或查询指定通道的输出波形幅值。当运行模式设置为扫频以外时,该命令可用。
     * 语法
     * SOURCE1:VOLTage:LEVel:IMMediate:AMPLitude 5Vpp
     *  当运行模式设置为扫频以外时,将CHI输出振幅值设置为5Vpp。
     */
    public static final String SOURCE_AMPLITUDE = "SOURce%d:VOLTage %dVpp";


    /**
     * 设置高电平幅值
     *
     * 使用命令 [SOURce[1|2]]:VOLTage:HIGH 来设置高电平的幅值。
     * 语法：[SOURce[1|2]]:VOLTage:HIGH {<电压>|最小值|最大值}
     * 参数 <电压> 可以是具体的数值，或者是 MINimum、MAXimum 来表示最小值或最大值。
     * 例如：SOURce1:VOLTage:HIGH 5V 设置通道1的高电平幅值为5伏。
     */
    public static final String SOURCE_HIGH_AMPLITUDE = "SOURce%d:VOLTage:HIGH %dVpp";

    /**
     * 这条命令用于设置或查询指定通道的偏移电平。
     * [SOURce[1|2]]:VOLTage[:LEVel][:IMMediate]:OFFSet {<电压>|最小值|最大值}。
     * [SOURce[1|2]] :VOLTage [:LEVel][:IMMediate]: OFFSet?【最小值 最大值】
     * 示例
     * SOURce1:VOLTage:LEVel:IMMediate:OFFSet 500mV  //将CH1偏移电平设置为500mV。
     */
    public static final String SOURCE_VOLTAGE_OFFSET = "SOURce%d:VOLTage:LEVel:IMMediate:OFFSet %s";

    /**
     * 设置脉宽
     * 使用命令 [SOURce[1|2]]:PULSe:WIDTH 来设置脉冲宽度。
     * 语法：[SOURce[1|2]]:PULSe:WIDTH {<时间>|最小值|最大值}
     * 参数 <时间> 可以是具体的数值，或者是 MINimum、MAXimum 来表示最小值或最大值。
     * 例如：SOURce1:PULSe:WIDTH 500us 设置通道1的脉冲宽度为500微秒。
     * */

    public static final String SOURCE_PULSE_WIDTH = "SOURce[1|2]]:PULSe:WIDTH %s";

    /**
     * 设置通道输出开关
     * */
    public static final String SET_OUTPUT_STATE = "OUTPut%d:STATe %s";


 // 用于格式化需要特定值的命令
    public static String formatCommand(String command, Object... values) {
        return String.format(command, values);
    }
}
