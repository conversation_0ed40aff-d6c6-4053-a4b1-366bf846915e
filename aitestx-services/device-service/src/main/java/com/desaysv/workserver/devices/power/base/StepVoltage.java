package com.desaysv.workserver.devices.power.base;

import com.desaysv.workserver.base.operation.invoker.OperationResultFormatter;
import lombok.Data;

@Data
public class StepVoltage implements OperationResultFormatter {

    private float startVoltage; //起始电压
    private float endVoltage; //终止电压
    private float stepVoltage;//步进电压
    private int stepInterval;//步进间隔(s)

    @Override
    public String format() {
        return String.format("步进电压开始:%.2fV\n结束:%.2fV", startVoltage, endVoltage);
    }
}
