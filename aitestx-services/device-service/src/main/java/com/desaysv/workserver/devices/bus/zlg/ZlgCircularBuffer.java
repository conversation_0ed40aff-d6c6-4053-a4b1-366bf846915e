package com.desaysv.workserver.devices.bus.zlg;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/6/6 15:04
 * @Version: 1.0
 * @Desc : 单项循环缓冲区
 */
public class ZlgCircularBuffer<CanMessageVo> {
    private final CanMessageVo[] buffer;
    private int writeIndex = 0;
    private int readIndex = 0;

    public ZlgCircularBuffer(int capacity) {
        buffer = (CanMessageVo[]) new Object[capacity];
    }

    public void write(CanMessageVo item) {
        buffer[writeIndex] = item;
        writeIndex = (writeIndex + 1) % buffer.length;
        if (writeIndex == readIndex) {
            readIndex = (readIndex + 1) % buffer.length;
        }
    }

    public CanMessageVo read() {
        if (isEmpty()) {
            throw new RuntimeException("Buffer is empty");
        }
        CanMessageVo item = buffer[readIndex];
        readIndex = (readIndex + 1) % buffer.length;
        return item;
    }

    public boolean isEmpty() {
        return readIndex == writeIndex;
    }

    public boolean isFull() {
        return (writeIndex + 1) % buffer.length == readIndex;
    }

    /**
     * 获取缓冲区中元素的数量
     * @return 元素数量
     */
    public int size() {
        if (writeIndex >= readIndex) {
            return writeIndex - readIndex;
        } else {
            return buffer.length - (readIndex - writeIndex);
        }
    }

    /**
     * 获取从尾部开始的指定位置的数据
     * @param index 从尾部开始计算的索引位置（0表示最后一个元素）
     * @return 指定位置的数据
     */
    public CanMessageVo getFromTail(int index) {
        if (index < 0 || index >= size()) {
            throw new IndexOutOfBoundsException("Index out of bounds");
        }
        // 计算实际索引：从写指针向前移动(index+1)个位置
        int actualIndex = (writeIndex - index - 1 + buffer.length) % buffer.length;
        return buffer[actualIndex];
    }
    
    /**
     * 获取缓冲区头部序号（下一个要读取的位置）
     * @return 缓冲区头部序号
     */
    public int getHeadIndex() {
        return readIndex;
    }
    
    /**
     * 获取缓冲区尾部序号（下一个要写入的位置）
     * @return 缓冲区尾部序号
     */
    public int getTailIndex() {
        return writeIndex;
    }
}
