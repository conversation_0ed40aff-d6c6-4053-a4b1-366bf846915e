package com.desaysv.workserver.monitor.data;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Consumer;

/**
 * 数据分发线程类
 */
@Slf4j
public class DataDistributor<T> implements Runnable, Consumer<T> {

    private final List<Consumer<T>> consumers;
    private boolean running;
    private boolean pausing;
    private final BlockingQueue<T> blockingQueue = new LinkedBlockingQueue<>();
    private final ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();

    public DataDistributor() {
        consumers = new ArrayList<>();
    }

    public DataDistributor(Consumer<T> consumer) {
        this();
        addConsumer(consumer);
        running = false;
        pausing = false;
    }

    public Consumer<T> addConsumer(Consumer<T> consumer) {
        consumers.add(consumer);
        return consumer;
    }

    public void removeConsumer(Consumer<T> consumer) {
        consumers.remove(consumer);
    }


    @Override
    public void run() {
        while (running) {
            try {
                if (pausing) {
                    synchronized (this) {
                        wait();
                    }
                }
                T data = blockingQueue.take();
                for (Consumer<T> consumer : consumers) {
                    consumer.accept(data);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 开始运行
     */
    public void start() {
        if (!running) {
            singleThreadExecutor.execute(this);
            running = true;
        }
    }

    /**
     * 停止运行
     */
    public void stop() {
        running = false;
        synchronized (this) {
            notifyAll();
        }
    }

    /**
     * 暂停运行
     */
    public void pause() {
        pausing = true;
    }

    /**
     * 恢复运行
     */
    public void resume() {
        pausing = false;
        synchronized (this) {
            notifyAll();
        }
    }

    @Override
    public void accept(T t) {
        try {
            if (consumers.size() > 0 && running) {
                blockingQueue.put(t);
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            log.warn("发生异常时数据:{}", t);
        }
    }

    public static void main(String[] args) {
        BlockingQueue<String> blockingQueue = new LinkedBlockingQueue<>();
        try {
            blockingQueue.put(null);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }
}
