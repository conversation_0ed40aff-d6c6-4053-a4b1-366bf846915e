package com.desaysv.workserver.devices.bus.nican;

import cantools.dbc.DbcReader;
import cantools.dbc.DecodedSignal;
import cantools.dbc.Message;
import cantools.dbc.Signal;
import cantools.exceptions.DecodingFrameLengthException;
import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.queue.FixedSizeQueue;
import com.desaysv.workserver.canlog.service.CanLogService;
import com.desaysv.workserver.canlog.service.impl.CanLogServiceImpl;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanLogRealTimeSaveParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageRealTimeSave;
import com.desaysv.workserver.devices.bus.base.can.SequenceableCanBus;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.desay.sv.exceptions.NICanException;
import org.desay.sv.model.NiCanR;
import org.desay.sv.service.CANServiceImpl;
import org.desay.sv.service.impl.CANService;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认数据库：
 * C:\Users\<USER>\Documents\National Instruments\NI-XNET\Examples
 **/
@Slf4j
public abstract class NICan extends SequenceableCanBus {
    private final Map<Integer, CANService> canServiceMap;
    private long initialTimestamp = -1; // 初始时间戳
    private CanLogService canLogService;
    private FixedSizeQueue<CanMessageVo> fixedQueue;

    public NICan() {
        this(new DeviceOperationParameter());
    }

    public NICan(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        canServiceMap = new ConcurrentHashMap<>();
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return openDevice(false);
    }

    @Override
    public boolean autoOpen() throws DeviceOpenException {
        return openDevice(true);
    }

    /**
     * 打开NI CAN设备。
     * 该方法根据传入的autoOpen参数决定是否自动打开CAN通道。如果autoOpen为true，则从CanConfig中获取配置参数并打开对应的CAN通道。
     *
     * @param autoOpen 是否自动打开CAN通道
     * @return 返回true表示打开成功
     * @throws DeviceOpenException 如果打开设备时发生异常
     */
    public boolean openDevice(boolean autoOpen) throws DeviceOpenException {
        // 从CanConfig获取配置参数
        if (autoOpen) {
            CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            Map<String, CanConfigParameter> configParameters = deviceConfig.getConfigParameters();
            Object ch0OpenParams = configParameters.get("1");
            Object ch1OpenParams = configParameters.get("2");
            if (ch0OpenParams != null) {
                openChannel(JSON.parseObject(JSON.toJSONString(ch0OpenParams), CanConfigParameter.class));
            }
            if (ch1OpenParams != null) {
                openChannel(JSON.parseObject(JSON.toJSONString(ch1OpenParams), CanConfigParameter.class));
            }
        }
        return true; //TODO:增加查询是否打开接口
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        log.info("关闭NI CAN:{}", getDeviceName());
        boolean isOk = super.close();
        if (!canServiceMap.isEmpty()) {
            for (CANService canService : canServiceMap.values()) {
                try {
                    canService.stop();
                } catch (NICanException e) {
                    throw new DeviceCloseException(e);
                }
            }
            canServiceMap.clear();
        }
        return isOk;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        log.info("打开NI CAN{}通道{}:\n{}",
                getDeviceName(),
                canConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(canConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        setChannelConfigured(true);
        if (!isSimulated()) {
            CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            Map<String, CanConfigParameter> configParameters = deviceConfig.getConfigParameters();
            int channel = canConfigParameter.getChannel();
            if (channel == 1) {
                configParameters.put("1", canConfigParameter);
            } else if (channel == 2) {
                configParameters.put("2", canConfigParameter);
            } else {
                canConfigParameter.setChannel(1);
                configParameters.put("1", canConfigParameter);

                //复制一个新的参数
                CanConfigParameter canConfigParameterCopy = SerializationUtils.clone(canConfigParameter);
                canConfigParameterCopy.setChannel(2);
                configParameters.put("2", canConfigParameterCopy);
            }
            updateConfig(deviceConfig);
            try {
                if (channel == -1) {
                    CANService canService1 = new CANServiceImpl(0);
                    CANService canService2 = new CANServiceImpl(1);
                    canService1.startWrite();
                    canService2.startWrite();
                    canServiceMap.put(1, canService1);
                    canServiceMap.put(2, canService2);
                } else {
                    CANService canService = new CANServiceImpl(channel - 1);
                    canService.startWrite();
                    canServiceMap.put(channel, canService);
                }
            } catch (NICanException e) {
                setChannelConfigured(false);
                throw new DeviceOpenException(e);
            }
        }
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        CANService canService = canServiceMap.get(channel);
        if (canService != null) {
            try {
                canService.stop();
            } catch (NICanException e) {
                throw new DeviceCloseException(e);
            }
        }
        return true;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        CANService canService = canServiceMap.get(message.getChannel());
        if (canService != null) {
            try {
                canService.writeStream(Integer.toHexString(message.getArbitrationId()),
                        message.getData(),
                        message.isCanFd(),
                        message.isExtendedId());
            } catch (NICanException e) {
                throw new BusError(e);
            }
        }
    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        CANService canService = canServiceMap.get(channel);
        if (canService != null) {
            try {
                canService.readStream();
            } catch (NICanException e) {
                throw new BusError(e);
            }
        }
        return null;
    }

    @Override
    public CyclicTask sendCanMessage(Integer deviceChannel, CanMessage message) throws BusError {
        CANService canService = canServiceMap.get(deviceChannel);
        log.info("NI CAN通道{}缓冲区剩余数量:{}", deviceChannel, canService.getNumberUnused());
        return super.sendCanMessage(deviceChannel, message);
    }

    @Override
    public void startDbcReceiver(Integer deviceChannel, DbcConfig dbcConfig) throws NICanException {
        CANService canService = canServiceMap.get(deviceChannel);
        canService.setDBCReadFlag(true);//开启读取开关
        canService.startRead();//设置session
        canService.readStream();
        List<Message> dbcMessages = new ArrayList<>();
        for (String s : dbcConfig.getDbcPaths()) {
            DbcReader dbcReader = new DbcReader();
            dbcReader.parseFile(new File(s));
            dbcMessages.addAll(dbcReader.getBus().getMessages());
        }
        canService.setCanDataReceivedDbcListener(niCanR -> {
            if (initialTimestamp == -1) {
                initialTimestamp = niCanR.getTimestamp();
            }
            long timestampDifference = niCanR.getTimestamp() - initialTimestamp;
            CanMessageVo niCanMessageVo = getData(niCanR, dbcMessages, convert100nsToSeconds(timestampDifference));
            if (niCanMessageVo != null) {
                niCanMessageVo.setChn(String.valueOf(deviceChannel)); //设置channel
                String jsonString = JSON.toJSONString(niCanMessageVo);
                SseUtils.pubMsg(SseConstants.CAN_DBC_RECEIVER + deviceChannel, jsonString);
                SseUtils.pubMsg(SseConstants.CAN_DBC_RECEIVER + deviceChannel + deviceChannel, jsonString);   //二次面板接收

                //实时保存
                if (canLogService != null) {
                    canLogService.realTimeSaveData(Collections.singletonList(niCanMessageVo));
                }
            }
        });
    }

    @Override
    public void startFrameReceiver(Integer deviceChannel) throws NICanException {
        fixedQueue = new FixedSizeQueue<>(99999);
        CANService canService = canServiceMap.get(deviceChannel);
        canService.setReadFlag(true);//开启读取开关
        canService.startRead();//设置session
        canService.readStream();
        canService.setCanDataReceivedListener(niCanR -> {
            if (initialTimestamp == -1) {
                initialTimestamp = niCanR.getTimestamp();
            }
            long timestampDifference = niCanR.getTimestamp() - initialTimestamp;
            CanMessageVo niCanMessageVo = getData(niCanR, convert100nsToSeconds(timestampDifference));
            if (niCanMessageVo.getDlc() != 0) {
                niCanMessageVo.setChn(String.valueOf(deviceChannel)); //设置channel
                String jsonString = JSON.toJSONString(niCanMessageVo);
                SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER + deviceChannel, jsonString);
                //帧保存数据
                try {
                    fixedQueue.add(niCanMessageVo);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                //实时保存
                if (canLogService != null) {
                    canLogService.realTimeSaveData(Collections.singletonList(niCanMessageVo));
                }
            }
        });
    }

    @Override
    public void stopFrameReceiver(Integer deviceChannel) {
        CANService canService = canServiceMap.get(deviceChannel);
        canService.setReadFlag(false);//关闭读取
    }

    @Override
    public void stopDbcReceiver(Integer deviceChannel) {
        CANService canService = canServiceMap.get(deviceChannel);
        canService.setDBCReadFlag(false);//关闭读取
    }

    private CanMessageVo getData(NiCanR niCanR, List<Message> dbcMessages, double elapsedTime) {
        double elapsedTimeSeconds = Double.parseDouble(String.format("%.6f", elapsedTime));
        String id = String.format("0x%02X", niCanR.getId());
        Message message = dbcMessages.stream().filter(o -> o.getId().equals(id)).findFirst().orElse(null);
        if (message != null) {
            try {
                CanMessageVo canMessageVo = new CanMessageVo();
                canMessageVo.setTime(elapsedTimeSeconds);
                canMessageVo.setId(id);
                //canMessage.setChn(canR.getChn());
                canMessageVo.setEventType(niCanR.getDataLength() > 8 ? "CAN FD" : "CAN");
                canMessageVo.setDir("Rx");
                canMessageVo.setDlc(niCanR.getDataLength());
                //这里如果出现长度不一致的情况直接截断或者补00
                byte[] validPayload = Arrays.copyOf(niCanR.getData(), message.getLength());
                canMessageVo.setData(validPayload);
                List<CanSignalVo> canSignalVoList = new ArrayList<>();
                List<Signal> signals = message.getSignals();
                Map<String, DecodedSignal> decodeData = message.decodeByDecodedSignal(validPayload);
                for (Signal signal : signals) {
                    Map.Entry<String, DecodedSignal> stringDecodedSignalEntry = decodeData.entrySet().stream().filter(o -> o.getKey().equals(signal.getName())).findFirst().get();
                    DecodedSignal decodedSignal = stringDecodedSignalEntry.getValue();
                    CanSignalVo canSignalVoR = new CanSignalVo(signal.getName(), String.valueOf(decodedSignal.getRawValue()), decodedSignal.getPhyValue(), signal.getNotes());
                    canSignalVoList.add(canSignalVoR);
                }
                canMessageVo.setName(message.getName());
                canMessageVo.setCanSignalList(canSignalVoList);
                return canMessageVo;
            } catch (DecodingFrameLengthException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private static double convert100nsToSeconds(long timestamp100ns) {
        return timestamp100ns / 1.0e7;
    }

    private CanMessageVo getData(NiCanR niCanR, double elapsedTime) {
        double elapsedTimeSeconds = Double.parseDouble(String.format("%.6f", elapsedTime));
        String id = String.format("0x%02X", niCanR.getId());
        CanMessageVo canMessageVo = new CanMessageVo();
        canMessageVo.setTime(elapsedTimeSeconds);
        canMessageVo.setId(id);
        //canMessage.setChn(canR.getChn());
        canMessageVo.setEventType(niCanR.getDataLength() > 8 ? "CAN FD" : "CAN");
        canMessageVo.setDir("Rx");
        canMessageVo.setDlc(niCanR.getDataLength());
        byte[] validPayload = Arrays.copyOf(niCanR.getData(), niCanR.getDataLength());
        canMessageVo.setData(validPayload);
        canMessageVo.setName("");
        return canMessageVo;

    }

    @Override
    public String notificationUpgrade(int fileType) {
        return null;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        return false;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) {
        return 0;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) {
        return false;
    }

    @Override
    public void send(FlexrayMessage message, Float timeout) throws BusError {
    }

    @Override
    public void startRealTimeData(Integer deviceChannel, CanMessageRealTimeSave canMessageRealTimeSave) {
        log.info("开启NI CANLog实时保存 通道:{}", deviceChannel);
        canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.startRealTimeData();
    }

    @Override
    public void stopRealTimeData() {
        log.info("关闭NI CANLog实时保存");
        canLogService.stopRealTimeData();
        canLogService = null;
    }

    @Override
    public void startCaptureFrameCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter){
        log.info("添加NI{}通道{}日志(Frame)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}",  canLogRealTimeSaveParameter.getFriendlyString());
        try {
            startFrameReceiver(deviceChannel);
        }catch (Exception e){
            log.error("开启读取NI{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureFrameCanLog(Integer deviceChannel){
        log.info("停止NI{}日志(Frame)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopFrameReceiver(deviceChannel);
        } catch (Exception e){
            log.error("关闭读取NI{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }

    @Override
    public void startCaptureDbcCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter){
        log.info("添加NI{}通道{}日志(DBC)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}",  canLogRealTimeSaveParameter.getFriendlyString());
        try {
            startDbcReceiver(deviceChannel,  canLogRealTimeSaveParameter.getDbcConfig());
        }catch (Exception e){
            log.error("开启读取NI{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureDbcCanLog(Integer deviceChannel){
        log.info("停止NI{}日志(DBC)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopDbcReceiver(deviceChannel);
        } catch (Exception e){
            log.error("关闭读取NI{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }


    @Override
    public void saveLog(CanMessageRealTimeSave canMessageRealTimeSave) throws BlfException {
        log.info("保存NI CANLog");
        CanLogServiceImpl canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.saveLog(fixedQueue);
    }

    @Override
    public byte[] readDataByIdHex(int deviceChannel, int targetCanId, boolean isCanFd, long timeoutMilliseconds) {
        long timeout = isSimulated() ? 0 : timeoutMilliseconds;
        return new byte[]{};
    }

    public static void main(String[] args) {
        CANService canService = new CANServiceImpl(0);
        try {
            if (canService.startWrite()) {
                System.out.println("获取session成功");
                canService.writeStream("301", new byte[]{0x00, 0x08, 0x00, 0x00}, false, false);
            }
        } catch (NICanException e) {
            throw new RuntimeException(e);
        }
    }
}