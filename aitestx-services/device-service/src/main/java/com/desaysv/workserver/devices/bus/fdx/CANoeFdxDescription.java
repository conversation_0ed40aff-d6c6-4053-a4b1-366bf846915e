package com.desaysv.workserver.devices.bus.fdx;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.desaysv.workserver.filemanager.project.FdxFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import lombok.Data;
import lombok.Setter;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
public class CANoeFdxDescription {
    private List<String> fdxFiles;
    private Map<String, DataGroup> dataGroups;
    @Setter
    private File fdxDescriptionFile;

    private static volatile CANoeFdxDescription instance = getInstance();

    public static CANoeFdxDescription getInstance() {
        if (instance == null) {
            synchronized (CANoeFdxDescription.class) {
                if (instance == null) {
                    instance = new CANoeFdxDescription();
                    instance.readFdxConfigFile();
                }
            }
        }
        return instance;
    }

    public Optional<CANoeFdxDescription> readFdxConfigFile() {
        FdxFileManager fdxFileManager =  ProjectFileManager.of(FdxFileManager.class);
        if (!fdxFileManager.isFdxConfigExist())
            return Optional.empty();
        try {
            String readStringFromFile = fdxFileManager.readFdxConfig();
            CANoeFdxDescription canoeFdxDescription = JSON.parseObject(readStringFromFile, CANoeFdxDescription.class);
            CANoeFdxDescription.getInstance().setFdxFiles(canoeFdxDescription.getFdxFiles());
            CANoeFdxDescription.getInstance().setDataGroups(canoeFdxDescription.getDataGroups());
            return Optional.of(canoeFdxDescription);
        } catch (JSONException e) {
            return Optional.empty();
        }
    }


    public static void main(String[] args) {
        CANoeFdxDescription.getInstance().readFdxConfigFile();
        Map<String, DataGroup> dataGroups = CANoeFdxDescription.getInstance().getDataGroups();
        DataGroup canMsgDLC = dataGroups.get("SetCanMsgDLC");
        int groupId = canMsgDLC.getGroupId();
        int groupSize = canMsgDLC.getGroupSize();
        Map<String, FdxItem> items = canMsgDLC.getItems();
        FdxItem nameNodes = items.get("NameNodes");
        int nameNodesSize = nameNodes.getSize();
        FdxItem msgName = items.get("ILcontrolMsgName");
        int msgNameSize = msgName.getSize();
        FdxItem msgDLC = items.get("ILcontrolMsgDLC");
        int msgDLCSize = msgDLC.getSize();
        System.out.println(groupId);
        System.out.println(groupSize);
        System.out.println(nameNodesSize);
        System.out.println(msgNameSize);
        System.out.println(msgDLCSize);

    }

}

