package com.desaysv.workserver.devices.resistance;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 电阻仪连接
 * @date: 2024/8/20 17:52
 */

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.net.SocketTimeoutException;

import static com.desaysv.workserver.devices.resistance.RemoteResistanceDevice.MSG_GET;

@Slf4j
public class ResistanceConnect {
    private Socket socket;
    private OutputStream output;
    private InputStream input;

    public void connect(String host, int port, int connectionTimeout, int readTimeout) throws IOException {
        socket = new Socket();
        socket.connect(new java.net.InetSocketAddress(host, port), connectionTimeout);
        socket.setSoTimeout(readTimeout);  // 设置读取超时

        output = socket.getOutputStream();
        input = socket.getInputStream();
    }

    public byte[] convertStringToBytes(String message) {
        String cleanedMessage = message.replaceAll("\\s", "");
        int byteCount = (cleanedMessage.length() + 1) / 2;
        byte[] result = new byte[byteCount];

        for (int i = 0; i < byteCount; i++) {
            int start = i * 2;
            int end = Math.min(start + 2, cleanedMessage.length());
            String chunk = cleanedMessage.substring(start, end);
            result[i] = (byte) Integer.parseInt(chunk, 16);
        }

        return result;
    }

    public void sendMessage(String message) throws IOException {
        if (socket == null || socket.isClosed()) {
            throw new IllegalStateException("连接失败");
        }
        byte[] messageBytes = convertStringToBytes(message);

        output.write(messageBytes);
        output.flush();
    }

    public String receiveMessage() throws IOException {
        if (socket == null || socket.isClosed()) {
            throw new IllegalStateException("连接失败");
        }
        try {
            byte[] buffer = new byte[1024];
            int bytesRead = input.read(buffer);
            if (bytesRead == -1) {
                return null;
            }
            StringBuilder hexString = new StringBuilder();
            for (int i = 0; i < bytesRead; i++) {
                String hex = Integer.toHexString(0xFF & buffer[i]);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (SocketTimeoutException e) {
            return null;  // 读取超时返回 null
        }
    }

    public void disconnect() {
        try {
            if (output != null) output.close();
            if (input != null) input.close();
            if (socket != null && !socket.isClosed()) socket.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static float convertIEEE754ToDecimal(String ieee754HexString) {
        // 将16进制字符串转换为32位整数
        int intBits = (int) Long.parseLong(ieee754HexString, 16);
        // 使用 Float.intBitsToFloat 方法将整数转换为浮点数
        return Float.intBitsToFloat(intBits);
    }

    public static void main(String[] args) {
        ResistanceConnect resistanceConnect = new ResistanceConnect();
        try {
            resistanceConnect.connect("192.168.1.80", 13, 5000, 5000);  // 连接超时 5 秒，读取超时 5 秒
            //client.sendMessage(generateSendStr(1000));
            resistanceConnect.sendMessage(MSG_GET);
            String response = resistanceConnect.receiveMessage();
            if (response != null) {
                response = response.substring(6, 14);
                //05000251
                response = String.valueOf(convertIEEE754ToDecimal(response));
                System.out.println("接收数据: " + response);
            }
        } catch (SocketTimeoutException e) {
            System.err.println("发送、接收超时");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            resistanceConnect.disconnect();
        }
    }
}