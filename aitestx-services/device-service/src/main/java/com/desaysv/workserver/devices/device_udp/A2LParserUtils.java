package com.desaysv.workserver.devices.device_udp;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class A2LParserUtils {
    public static Map<String, VariableInfo> parse(String filePath) throws IOException {
        Map<String, VariableInfo> result = new HashMap<>();
        String pattern = "^("
                + "FLOAT64_IEEE|FLOAT32_IEEE|"      // 优先匹配更长的浮点类型
                + "__ULONG_S|ULONG|"                // 带双下划线的类型优先
                + "SLONG_S|SLONG|"
                + "SWORD_S|SWORD|"
                + "__UBYTE_S|UBYTE|"
                + "SBYTE_S|SBYTE|"
                + "UWORD"                           // 最后匹配基础类型
                + ").*";

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            String currentBlockType = null;
            String currentVarName = null;
            String currentDataType = null;
            String currentVarType = null;
            String currentAddress = null;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 检测块开始
                if (line.startsWith("/begin ")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentBlockType = parts[1];
                        if (currentBlockType.equals("CHARACTERISTIC") || currentBlockType.equals("MEASUREMENT")) {
                            currentVarName = parts[2].replace("\"", ""); // 提取变量名
                            currentVarType = currentBlockType;
                        }
                    }
                }

                // 解析块内容
                if (currentBlockType != null) {
                    // 数据类型提取
                    if (line.matches(pattern)) {
//                    if (line.matches("^(UBYTE|UWORD|__UBYTE_S|FLOAT32_IEEE|\\w+).*")) {
                        String[] tokens = line.split("\\s+");
                        if (currentBlockType.equals("MEASUREMENT") && tokens.length > 0) {
                            currentDataType = tokens[0]; // MEASUREMENT 的数据类型在第一列
                        }
                    }

                    // 地址提取逻辑
                    if (line.startsWith("ECU_ADDRESS ")) {
                        currentAddress = line.split("\\s+")[1]; // 直接提取地址
                    } else if (line.startsWith("VALUE ") && currentBlockType.equals("CHARACTERISTIC")) {
                        currentAddress = line.split("\\s+")[1]; // CHARACTERISTIC 的地址在 VALUE 行
                        currentDataType = line.split("\\s+")[2];
                    } else if (line.contains("LINK_MAP")) {
                        String[] mapParts = line.split("\\s+");
                        if (mapParts.length >= 3) {
                            currentAddress = mapParts[2]; // 后备地址来源
                        }
                    }
                }

                // 检测块结束
                if (line.startsWith("/end ")) {
                    if (currentVarName != null && currentAddress != null && currentDataType != null) {
                        result.put(currentVarName, new VariableInfo(currentVarType, currentAddress, currentDataType));
                    }
                    // 重置状态
                    currentBlockType = null;
                    currentVarName = null;
                    currentDataType = null;
                    currentAddress = null;
                    currentVarType = null;
                }
            }
        }
        return result;
    }

    public static void main(String[] args) throws IOException {
        String filePath = "D:\\uidq9000\\理想BCM资料\\FBCM_EOL_0x4026.a2l";
        Map<String, VariableInfo> a2lMap = parse(filePath);
        System.out.println(a2lMap.size());

    }
}