package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.MPS3610HProtocol;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Arrays;

/**
 * MP3610H系列电源
 */
public class MPS3610H extends DefaultPower {
    public MPS3610H() {
        this(new DeviceOperationParameter());
    }

    public MPS3610H(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setPowerProtocol(new MPS3610HProtocol(this));
    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.MPS3610H;
    }


    public static void main(String[] args) throws DeviceOpenException, DeviceCloseException, JVisaException, DeviceReadException {
        JVisaResourceManager resourceManager = new JVisaResourceManager();
        String[] res = resourceManager.findResources();
        System.out.println(Arrays.toString(res));
        DeviceOperationParameter operationParameter = new DeviceOperationParameter();
        operationParameter.put("protocol", CommProtocol.RS232);
        MPS3610H power = new MPS3610H(operationParameter);
        power.setBaudRate(9600);
        power.setDevicePort(3);
        power.setDeviceName(res[0]);
        power.open();
        int channel = 0;
        power.outputOff(channel);
        power.outputOn(channel);
        power.setVoltage(channel, 12);
        float readVoltage = power.fetchVoltage(channel);
        System.out.println("readVoltage---" + readVoltage);
        power.setCurrent(channel, 10);
        float readCurrent = power.fetchCurrent(channel);
        System.out.println("readCurrent---" + readCurrent);
        power.close();
        resourceManager.close();
    }
}
