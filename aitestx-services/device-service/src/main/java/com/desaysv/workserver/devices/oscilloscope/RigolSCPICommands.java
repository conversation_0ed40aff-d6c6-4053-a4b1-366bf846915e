package com.desaysv.workserver.devices.oscilloscope;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/10/08 14:29
 */
public class RigolSCPICommands {
    // 基本操作命令
    public static final String RESET_INSTRUMENTS = "*RST";
    public static final String CLEAR_SCREEN = ":CLEar";

    // 通道设置命令
    public static final String CHANNEL_SCALE = ":CHANnel%d:SCALe %s";
    public static final String CHANNEL_DISPLAY = ":CHANnel%d:DISPlay %s";

    // 水平时基设置命令
    public static final String HORIZONTAL_SCALE = ":TIMebase:MAIN:SCALe %s";
    public static final String HORIZONTAL_OFFSET = ":TIMebase:MAIN:OFFSet %s";

    // 触发设置命令
    public static final String TRIGGER_MODE = ":TRIGger:SWEep %s";
    public static final String TRIGGER_SOURCE = ":TRIGger:EDGE:SOURce %s";
    public static final String TRIGGER_LEVEL = ":TRIGger:EDGE:LEVel %s";

    // 耦合设置命令
    public static final String COUPLING = ":CHANnel%d:COUPling %s";

    // 测量设置命令
    public static final String MEASURE_VOLTAGE_DC = ":DVM:MODE DC;:DVM:CURRent?";
    public static final String MEASURE_VOLTAGE_AC = ":DVM:MODE AC;:DVM:CURRent?";
    public static final String MEASURE_CURRENT_DC = ":MEASure:ITEM CURRent:DC?";
    public static final String MEASURE_CURRENT_AC = ":MEASure:ITEM CURRent:AC?";

    // 波形录制设置命令
    public static final String WAVEFORM_RECORD_ENABLE = ":RECord:WRECord:ENABle %s";
    public static final String WAVEFORM_PLAY = ":RECord:WREPlay:OPERate %s";

    // 快捷操作命令
    public static final String QUICK_OPERATION = ":QUICk:OPERation %s";

    // 其他设置命令
    public static final String SET_TRIGGER_COUPLING = ":TRIGger:COUPling %s";
    public static final String SET_BANDWIDTH_LIMIT = ":CHANnel%d:BWLimit %s";

    // 用于格式化通道相关的命令
    public static String formatChannelCommand(String command, int channel) {
        return command.replace("%d", String.valueOf(channel));
    }

    // 用于格式化需要特定值的命令
    public static String formatCommand(String command, Object... values) {
        return String.format(command, values);
    }
}