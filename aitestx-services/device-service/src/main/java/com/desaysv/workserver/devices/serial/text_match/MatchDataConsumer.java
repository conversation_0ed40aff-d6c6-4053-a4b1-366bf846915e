package com.desaysv.workserver.devices.serial.text_match;

import lombok.Data;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-8 11:46
 * @description :
 * @modified By :
 * @since : 2022-7-8
 */
@Data
public abstract class MatchDataConsumer implements Consumer<String> {

    private String findText;

    public MatchDataConsumer(String findText) {
        this.findText = findText;
    }

    public abstract List<String> getMatchingText();

    public abstract void clear();

}
