package com.desaysv.workserver.devices.serial;

import com.desaysv.workserver.common.port.PortConfig;
import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.utils.ArrayUtils;
import com.desaysv.workserver.utils.ByteUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 串口工具类
 */
@Slf4j
public class SerialUtils {

    /**
     * 遍历串口列表
     *
     * @return 端口列表
     */
    public static List<SerialPort> findPorts() {
        // 获得当前所有可用串口
        SerialPort[] commPorts = SerialPort.getCommPorts();
        return Arrays.asList(commPorts);
    }

    /**
     * 判断端口名是否被占用
     *
     * @param portName 端口名
     * @return 端口名是否被占用
     */
    public static boolean isCurrentlyOwned(String portName) {
        return SerialPort.getCommPort(portName).isOpen();
    }

    /**
     * 打开串口
     *
     * @param portName 端口名称
     * @param baudRate 波特率
     * @return 串口对象
     * @throws SerialExceptions.NotSerialPort 端口指向设备不是串口类型
     */
    public static SerialPort openPort(String portName, int baudRate) throws
            SerialExceptions.NotSerialPort, SerialExceptions.PortOpenFail {
        return openPort(portName, new PortConfig(baudRate));
    }

    /**
     * 打开串口
     *
     * @param portName   端口名称
     * @param portConfig 端口配置
     * @return 串口对象
     * @throws SerialExceptions.NotSerialPort              端口指向设备不是串口类型
     * @throws SerialExceptions.SerialPortParameterFailure 设置串口参数失败
     * @throws SerialExceptions.NoSuchPort                 没有该端口对应的串口设备
     * @throws SerialExceptions.PortInUse                  端口已被占用
     */
    public static SerialPort openPort(String portName, PortConfig portConfig) throws
            SerialExceptions.NotSerialPort, SerialExceptions.PortOpenFail {
        portName = portName.toUpperCase();
        // 通过端口名识别端口
        SerialPort serialPort = SerialPort.getCommPort(portName);
        if (serialPort == null) {
            throw new SerialExceptions.NotSerialPort(portName);
        }
        if (!serialPort.isOpen()) {
            // 设置一下串口的波特率等参数
            serialPort.setComPortParameters(
                    portConfig.getBaudRate(),
                    portConfig.getDataBit(),
                    portConfig.getStopBit(),
                    portConfig.getParity());
            serialPort.setComPortTimeouts(SerialPort.TIMEOUT_WRITE_BLOCKING, 0, 5000);
            serialPort.setComPortTimeouts(SerialPort.TIMEOUT_READ_BLOCKING, 2000, 0);
            boolean isOpen = serialPort.openPort();
            if (!isOpen) {
                throw new SerialExceptions.PortOpenFail(portName);
            }
        }
        return serialPort;
    }

    /**
     * 关闭串口
     *
     * @param serialPort 待关闭的串口对象
     */
    public static void closePort(SerialPort serialPort) {
        if (serialPort != null) {
            try {
                serialPort.getInputStream().close();
                serialPort.getOutputStream().close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                serialPort.closePort();
            }
        }
    }

    /**
     * 往串口发送数据
     *
     * @param serialPort 当前已建立连接的串口对象
     * @param data       待发送数据
     * @throws SerialExceptions.SendDataToSerialPortFailure        向串口发送数据失败
     * @throws SerialExceptions.SerialPortOutputStreamCloseFailure 关闭串口对象的输出流出错
     */
    public static boolean sendToPort(SerialPort serialPort, String data) throws SerialExceptions.SendDataToSerialPortFailure {
        return sendToPort(serialPort, data, null);
    }

    /**
     * 往串口发送数据
     *
     * @param serialPort    当前已建立连接的串口对象
     * @param data          待发送数据
     * @param lineSeparator 分隔符
     * @return 是否发送成功
     * @throws SerialExceptions.SendDataToSerialPortFailure
     */
    public static boolean sendToPort(SerialPort serialPort, String data, String lineSeparator) throws SerialExceptions.SendDataToSerialPortFailure {
        if (lineSeparator != null) {
            data += lineSeparator;
        }
//        System.out.println(Arrays.toString(data.getBytes()));
        return sendToPort(serialPort, data.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 往串口发送数据
     *
     * @param serialPort 当前已建立连接的串口对象
     * @param data       待发送数据
     * @return 是否发送成功
     * @throws SerialExceptions.SendDataToSerialPortFailure 向串口发送数据失败
     */
    public static synchronized boolean sendToPort(SerialPort serialPort, byte[] data) throws SerialExceptions.SendDataToSerialPortFailure {
        OutputStream out;
        try {
            out = serialPort.getOutputStream();
            out.write(data);
//            out.flush();
            return true;
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 从串口读取指定结束符的数据
     *
     * @param serialPort 当前已建立连接的串口对象
     * @param terminator 结束符
     * @return 读取的数据
     * @throws SerialExceptions.ReadDataFromSerialPortFailure
     */
    public static byte[] readFromPort(SerialPort serialPort, char terminator) throws SerialExceptions.ReadDataFromSerialPortFailure {
        InputStream in;
        byte[] bytes = {};

        try {
            in = serialPort.getInputStream();
            byte[] readBuffer;
            // 获取buffer里的数据长度
            int bufflenth = in.available();
            do {
                // 初始化byte数组为buffer中数据的长度
                readBuffer = new byte[bufflenth];
                in.read(readBuffer);
                bufflenth = in.available();
                bytes = ArrayUtils.concat(bytes, readBuffer);
            } while (bytes[bytes.length - 1] != (byte) terminator);
        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure();
        }
        return bytes;
    }


    @Deprecated
    public static byte[] readFromPortToLength(SerialPort serialPort, int length) throws SerialExceptions.ReadDataFromSerialPortFailure {
        if (length <= 0) {
            return readFromPort(serialPort);
        }
        InputStream in;
        byte[] bytes = new byte[length];
        try {
            in = serialPort.getInputStream();
            in.read(bytes, 0, length);
        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure();
        }
        return bytes;
    }

    /**
     * 从串口读取指定长度的数据
     *
     * @param serialPort 当前已建立连接的串口对象
     * @param length     指定长度
     * @return 读取的数据
     * @throws SerialExceptions.ReadDataFromSerialPortFailure
     */
    public static byte[] readFromPort2(SerialPort serialPort, int length) throws SerialExceptions.ReadDataFromSerialPortFailure {
        if (length <= 0) {
            return readFromPort(serialPort);  // 处理默认长度读取的情况
        }
        InputStream in;
        byte[] bytes;
//        int lengthTemp = length;
        int index = 0;
        int readSize;
        try {
            in = serialPort.getInputStream();
            int available = in.available();
            if (available == 0) {
                bytes = new byte[0];
            } else {
                int lengthTemp = length;
//                int lengthTemp = Math.min(available, length);
                bytes = new byte[lengthTemp];
                while ((readSize = in.read(bytes, index, lengthTemp)) != -1) {
                    lengthTemp -= readSize;
                    if (lengthTemp == 0) {
                        break;
                    }
                    index += readSize;
                }
            }
        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure(e);
        }
        return bytes;
    }

    public static byte[] readFromPort(SerialPort serialPort, int length) throws SerialExceptions.ReadDataFromSerialPortFailure {
        if (length <= 0) {
            return readFromPort(serialPort);  // 处理默认长度读取的情况
        }

        InputStream in;
        byte[] bytes = new byte[length];  // 创建指定长度的字节数组
        int index = 0;
        int readSize;

        try {
            in = serialPort.getInputStream();

            // 持续读取直到获取到足够的字节
            while (index < length) {
                // 每次读取剩余需要的数据
                readSize = in.read(bytes, index, length - index);

                if (readSize == -1) {  // 如果到达流的末尾
                    break;
                }
                index += readSize;  // 更新已读取的字节数
            }

            // 如果读取的字节数不足预期，可能需要返回部分数据
            if (index < length) {
                byte[] result = new byte[index];
                System.arraycopy(bytes, 0, result, 0, index);
                return result;
            }

        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure(e);
        }

        return bytes;  // 返回完整的字节数组
    }


    /**
     * 从串口读取数据
     *
     * @param serialPort 当前已建立连接的串口对象
     * @return 读取到的数据
     * @throws SerialExceptions.ReadDataFromSerialPortFailure     从串口读取数据时出错
     * @throws SerialExceptions.SerialPortInputStreamCloseFailure 关闭串口对象输入流出错
     */
    public static byte[] readFromPort(SerialPort serialPort) throws SerialExceptions.ReadDataFromSerialPortFailure {
        InputStream in;
        byte[] bytes = {};

        try {
            in = serialPort.getInputStream();
            byte[] readBuffer;
//            // 获取buffer里的数据长度
            int bufflenth = in.available();
            while (bufflenth != 0) {
                // 初始化byte数组为buffer中数据的长度
                readBuffer = new byte[bufflenth];
                in.read(readBuffer);
                bufflenth = in.available();
                bytes = ArrayUtils.concat(bytes, readBuffer);
            }
//            System.out.print(new String(bytes));
        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure();
        }
        return bytes;
    }

    public static String sendToPortAndReceive(SerialPort serialPort, String data, int timeout)
            throws SerialExceptions.SendDataToSerialPortFailure,
            SerialExceptions.ReadTimeoutFromSerialPortFailure,
            SerialExceptions.ReadDataFromSerialPortFailure {
        sendToPort(serialPort, data);
        return receiveResponse(serialPort, timeout);
    }

    public static String receiveResponse(SerialPort serialPort, float timeout)
            throws SerialExceptions.ReadTimeoutFromSerialPortFailure, SerialExceptions.ReadDataFromSerialPortFailure {
        long startTime = System.currentTimeMillis();
        StringBuilder response = new StringBuilder();
        timeout *= 1000;
        try {
            InputStream inputStream = serialPort.getInputStream();
            byte[] bytes = {};
            while (System.currentTimeMillis() - startTime < timeout) {
                if (inputStream.available() > 0) {
                    byte[] buffer = readFromPort(serialPort);
                    bytes = ArrayUtils.concat(bytes, buffer);
                }
            }
            response.append(new String(bytes));
        } catch (IOException e) {
            throw new SerialExceptions.ReadDataFromSerialPortFailure(e);
        }
        if (response.length() == 0) {
            throw new SerialExceptions.ReadTimeoutFromSerialPortFailure("返回数据为空");
        }
        return response.toString();
    }

    /**
     * 添加监听器
     *
     * @param serialPort 串口对象
     * @param listener   串口监听器
     * @throws SerialExceptions.TooManyListeners 监听类对象过多
     */
    public static void addListener(SerialPort serialPort, SerialPortDataListener listener)
            throws SerialExceptions.TooManyListeners, SerialExceptions.UnsupportedCommOperationException {
        //给串口添加监听器
        serialPort.addDataListener(listener);
    }

    /**
     * 移除监听器
     *
     * @param serialPort 串口对象
     */
    public static void removeListener(SerialPort serialPort) {
        serialPort.removeDataListener();
    }

    public static byte[] hexStringToByteArray(String message) {
        return hexStringToByteArray(message, false);
    }

    public static byte[] hexStringToByteArray(String message, boolean terminator) {
        message = message.replaceAll("\\s+|\n", "");
        byte[] bytes = ByteUtils.hexStringToByteArray(message);
        if (terminator) {
            byte[] newBytes = new byte[bytes.length + 1];
            System.arraycopy(bytes, 0, newBytes, 0, bytes.length);
            newBytes[bytes.length] = 10;
            return newBytes;
        } else {
            return bytes;
        }
    }

    public static void main(String[] args) {
        System.out.println(SerialUtils.findPorts());
//        SerialPort serialPort = null;
//        try {
//            PortConfig portConfig = new PortConfig(115200);
//            portConfig.setParity(SerialPort.PARITY_EVEN);
//
//            serialPort = SerialUtils.openPort("COM9", portConfig);
//            addListener(serialPort, new UsbSerialPortEventListener(serialPort));
//        } catch (SerialExceptions.NotSerialPort | SerialExceptions.SerialPortParameterFailure |
//                 SerialExceptions.PortInUse | SerialExceptions.NoSuchPort | SerialExceptions.TooManyListeners |
//                 SerialExceptions.UnsupportedCommOperationException e) {
//          log.error(e.getMessage(), e);
//        }
//        System.out.println("serialPort:" + serialPort);
//        if (serialPort != null) {
//            try {
////                SerialUtils.sendToPort(serialPort, "Aa123456"+System.lineSeparator());
////                SerialUtils.sendToPort(serialPort, ""+System.lineSeparator());
//                SerialUtils.sendToPort(serialPort, stringToByteArray("5A 01 08 02 02 01 68"));
//                try {
//                    Thread.sleep(100);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//                System.out.println("读取结果:");
//                System.out.println(Arrays.toString(SerialUtils.readFromPort(serialPort)));
//            } catch (SerialExceptions.SendDataToSerialPortFailure | SerialExceptions.ReadDataFromSerialPortFailure e) {
//                 log.error(e.getMessage(), e);
//            }
//            while (true) {
//                try {
//                    byte[] bytes = DeviceUtils.readFromPort(serialPort);
//                    if (bytes == null) {
//                        continue;
//                    }
//                    System.out.println(new String(bytes, "gbk"));
//                } catch (SerialExceptions.ReadDataFromSerialPortFailure |
//                         SerialExceptions.SerialPortInputStreamCloseFailure |
//                         UnsupportedEncodingException e) {
//                     log.error(e.getMessage(), e);
//                }
//            }
//        }
//
//    }
    }


}
