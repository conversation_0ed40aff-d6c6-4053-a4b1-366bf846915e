package com.desaysv.workserver.screen.config;

import lombok.Data;

@Data
public class GestureCode {
    public final static String PRESS = "按下";
    public final static String DRAG = "拖动";
    public final static String RELEASE = "释放";

    private int pressHexCode;
    private int dragHexCode;
    private int releaseHexCode;
    
    public String getGestureString(Integer hexCode) {
        if (hexCode == null) {
            return null;
        }
        if (pressHexCode == hexCode) {
            return PRESS;
        } else if (dragHexCode == hexCode) {
            return DRAG;
        } else if (releaseHexCode == hexCode) {
            return RELEASE;
        }
        return null;
    }
}