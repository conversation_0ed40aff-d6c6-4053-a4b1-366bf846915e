package com.desaysv.workserver.devices.videocapture;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.manager.ImageFileManager;
import com.desaysv.workserver.manager.VideoCaptureImageFileManager;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.NetworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

/**
 * @author: QinHao
 * @description:
 * @date: 2024/12/23 18:03
 */
@Slf4j
public class VideoCaptureDevice extends Device implements IVideoCapture {
    public static final List<String> IP_ADDRESS = new ArrayList<>(Collections.singletonList(DeviceParams.defaultVideoCaptureIP));
    public static final int PORT = 55003;
    private final RestTemplate restTemplateClient = new RestTemplate();
    private VLA804 vla804;
    private volatile ScheduledExecutorService scheduler;
    private ScheduledFuture<?> scheduledTask;


    public VideoCaptureDevice() {
        this(new DeviceOperationParameter());
    }

    public VideoCaptureDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_VIDEO_CAPTURE;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.VideoCapture.VIDEO_CAPTURE;
    }


    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        vla804 = new VLA804(DeviceParams.defaultVideoCaptureIP, PORT);
        try {
            vla804.connect().join(); // 阻塞直到连接完成
        } catch (CompletionException e) {
            throw new DeviceOpenException(e);
        }
        GrabRequest grabRequest = new GrabRequest();
        grabRequest.setDeviceUUID(getDeviceUniqueCode());
        grabRequest.setDeviceName(getDeviceName());
        grabRequest.setDeviceModel(getDeviceModel());
        grabRequest.setWidth(vla804.getImageWidth());
        grabRequest.setHeight(vla804.getImageHeight());
        ParameterizedTypeReference<ResultEntity<Object>> typeRef = new ParameterizedTypeReference<ResultEntity<Object>>() {
        };
        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/grab", NetworkUtils.getServerPort());
        ResultEntity<Object> entity = restTemplateClient.exchange(url, HttpMethod.POST, new HttpEntity<>(grabRequest), typeRef).getBody();
        if (entity == null) {
            throw new DeviceOpenException(String.format("Post请求失败:%s", url));
        }
        if (!entity.isOk()) {
            throw new DeviceOpenException(entity.getMessage());
        }

        return entity.isOk();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        stopScheduledVideoCaptureScreenShoot();
        if (vla804 != null) {
            vla804.disconnect();
        }
        if (scheduledTask != null) {
            scheduledTask.cancel(true);
        }
        shutdownScheduler();
        return !vla804.isConnected();
    }

    @Override
    public String videoCaptureScreenShoot() throws OperationFailNotification {
        String targetFileName;
        String fileName = "screenshot.png";
        File toPath = ((VideoCaptureImageFileManager) ImageFileManager.getImageFileManager(this,
                getDeviceOperationParameter().getProject())).getScreenshotFile();
        targetFileName = new File(toPath, fileName).getAbsolutePath();
        try {
            if (isSimulated()) {
                BufferedImage bufferedImage = ImageUtils.generateRandomColorImage(1920, 1080);
                ImageIO.write(bufferedImage, "png", new File(targetFileName));
            } else {
                if (!vla804.readCapturedImage(targetFileName)) {
                    ImageUtils.createBlackImage(targetFileName);
                }
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }

        return targetFileName;
    }

    public void startScheduledVideoCaptureScreenShoot() {
        // 检查线程池状态
        if (scheduler == null || scheduler.isShutdown()) {
            // 重新创建线程池，建议根据业务设置合理corePoolSize
            scheduler = Executors.newScheduledThreadPool(2);
        }

        scheduledTask = scheduler.scheduleAtFixedRate(() -> {
            try {
                if (vla804.isConnected()) {
                    videoCaptureScreenShoot();
                }
            } catch (OperationFailNotification e) {
                log.error("定时视频截图时出错", e);
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);
    }

    public void shutdownScheduler() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(3, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    public void pauseScheduledVideoCaptureScreenShoot() {
        if (scheduledTask != null && !scheduledTask.isCancelled()) {
            scheduledTask.cancel(false);
        }
    }

    public void resumeScheduledVideoCaptureScreenShoot() {
        if (scheduledTask == null || scheduledTask.isCancelled()) {
            startScheduledVideoCaptureScreenShoot();
        }
    }

    public void stopScheduledVideoCaptureScreenShoot() {
        if (scheduledTask != null) {
            scheduledTask.cancel(false);
            scheduledTask = null; // 清除引用
        }
        if (scheduler != null) {
            scheduler.shutdown(); // 正常关闭
        }
    }
}
