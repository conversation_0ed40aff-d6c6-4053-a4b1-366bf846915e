package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.devices.power.base.PowerDevice;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PowerControlBoxProtocol implements SimplePowerProtocol {
    private final PowerDevice device;

    public PowerControlBoxProtocol(PowerDevice device) {
        this.device = device;
    }

    public boolean sendByte(byte b) {
        try {
            return device.send(new byte[]{b});
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    //FIXME：可能逻辑反了

    private byte getOpenCommand(int channel) {
        byte b;
        switch (channel) {
            case 2:
                b = 0x21;
                break;
            case 3:
                b = 0x31;
                break;
            case 4:
                b = 0x41;
                break;
            case 1:
            default:
                b = 0x11;
        }
        return b;
    }

    private byte getCloseCommand(int channel) {
        byte b;
        switch (channel) {
            case 2:
                b = 0x20;
                break;
            case 3:
                b = 0x30;
                break;
            case 4:
                b = 0x40;
                break;
            case 1:
            default:
                b = 0x10;
        }
        return b;
    }

    /**
     * 控制电源输出状态
     *
     * @param channel  电源通道（可选）
     * @param isOutput 电源输出状态（0为输出OFF，1为输出ON）
     * @return
     */
    public boolean setOutput(Integer channel, boolean isOutput) {
        return isOutput ? sendByte(getCloseCommand(channel)) : sendByte(getOpenCommand(channel));
    }

    @Override
    public boolean fetchOutput() {
        return false;
    }
}
