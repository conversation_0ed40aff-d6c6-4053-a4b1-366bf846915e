package com.desaysv.workserver.executor;

import com.desaysv.workserver.action_sequence.ActionSequenceConfigManager;
import com.desaysv.workserver.action_sequence.ActionSequenceEventBus;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.context.ActionSequenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ActionSequenceExecutorHandlerImpl implements ActionSequenceExecutorHandler {

    private boolean pause;
    private boolean stop;

    @Override
    public boolean shouldPause() {
        return pause;
    }

    @Override
    public boolean shouldStop() {
        return stop;
    }

    @Override
    public void setTestExecute(boolean stopFlag) {
        stop = stopFlag;
        if (stop) {
            log.info("~~~请求测试停止~~~");
            if (pause) {
                log.info("检测当前已暂停");
                resume();
            }
            pause = false;
        }
    }


    public void startExecute(ActionSequenceContext actionSequenceContext) {
        stop = false;
        pause = false;
        ActionSequenceEventBus.remove(ActionSequenceEventBus.PAUSE_ACTION_EVENT);
        ActionSequenceConfigManager.setConfig(actionSequenceContext.getTestConfig());
    }

    public void pauseImmediately() {
        log.info("~~~请求测试暂停~~~");
        ActionSequenceEventBus.setPause(true);
        pause = true;
    }

    public void resume() {
        log.info("~~~请求测试恢复~~~");
        ActionSequenceEventBus.setPause(false);
        pause = false;
        synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
            ActionSequenceLock.getInstance().getPauseLock().notify();
        }
    }

    public void stopImmediately() {
        log.info("~~~请求测试停止~~~");
        stop = true;
        if (pause) {
            log.info("检测当前已暂停");
            resume();
        }
        pause = false;
    }

}
