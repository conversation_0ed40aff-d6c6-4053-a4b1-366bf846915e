package com.desaysv.workserver.parser;

import com.desaysv.workserver.components.ActionSequence;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 多个ActionSequence组合捆绑
 */
public class ActionSequenceGroup extends ActionSequence {


    @Getter
    private final List<ActionSequence> actionSequenceGroups = new ArrayList<>();

    @Setter
    @Getter
    private int loopCycle;

    public ActionSequenceGroup() {
        setCheckOk(true);
        getOptions().setGrouped(true);
    }

    @Override
    public boolean isExecuted() {
        return actionSequenceGroups.stream().anyMatch(ActionSequence::isExecuted);
    }

    @Override
    public boolean isExecuteOk() {
        if (!isExecuted()) {
            return false;
        }

        for (ActionSequence actionSequence : actionSequenceGroups) {
            if (actionSequence.isExecuted() && !actionSequence.isExecuteOk()) {
                return false;
            }
        }
        return true;
    }

    public void addToGroup(ActionSequence actionSequence) {
        actionSequenceGroups.add(actionSequence);
    }

    @Override
    public String toString() {
        return String.format("checkOk:%s | executed:%s | executeOk:%s | loopCycle:%d | userSeqOrder:%s | %s | %s",
                isCheckOk(), isExecuted(), isExecuteOk(),
                loopCycle,
                getUserSequenceOrder(),
                getOptions().isEnable() ? "enable" : "disable",
                getRawExpression() != null ? getRawExpression() : "");
    }

}
