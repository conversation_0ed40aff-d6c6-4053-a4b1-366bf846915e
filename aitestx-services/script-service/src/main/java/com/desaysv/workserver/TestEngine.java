package com.desaysv.workserver;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.power.IT68xx;
import com.desaysv.workserver.devices.power.base.interfaces.IPowerDevice;
import com.desaysv.workserver.regex.PowerRegexRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.ReflectionUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class TestEngine {

    public static void main(String[] args) throws NoSuchMethodException {
        Annotation[] annotations = IPowerDevice.class.getAnnotations();     // 获取接口上的全部Annotation信息
        for (Annotation annotation : annotations) {
            System.out.println("PowerDevice - annotation: " + annotation);
        }
        Map<String, Method> methodMap = new HashMap<>();
        Method[] methods = ReflectionUtils.getDeclaredMethods(IPowerDevice.class);
        List<Method> annotationMethods = new ArrayList<>();
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (Method method : methods) {
            if (method.isAnnotationPresent(RegexRule.class)) {
                annotationMethods.add(method);
                for (String rule : method.getAnnotation(RegexRule.class).rule()) {
//                    System.out.println("parsing:" + rule);
                    methodMap.put(parser.parseExpression(rule).getValue(String.class), method);
                }
            }
        }
        System.out.println("annotationMethods:" + annotationMethods);
        System.out.println("methodMap:" + methodMap);

        try {
            methodMap.get(PowerRegexRule.CHANGE_VOLTAGE).invoke(new IT68xx(), 10);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error(e.getMessage(), e);
        }
    }

}
