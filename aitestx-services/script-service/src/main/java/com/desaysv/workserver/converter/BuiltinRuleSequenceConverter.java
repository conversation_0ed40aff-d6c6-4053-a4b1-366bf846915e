package com.desaysv.workserver.converter;

import cantools.dbc.DbcReader;
import cantools.dbc.Message;
import cantools.dbc.Signal;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import com.desaysv.workserver.devices.bus.DbcUtils;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 内置规则序列解析器
 */
@Data
@Component
@Lazy
public class BuiltinRuleSequenceConverter implements SequenceConverter {
    /**
     * 内置模板策略
     */
    private static class PatternStrategy {
        private final Pattern pattern;
        private final ConversionStrategy strategy;

        public PatternStrategy(Pattern pattern, ConversionStrategy strategy) {
            this.pattern = pattern;
            this.strategy = strategy;
        }
    }

    /**
     * DBC文件配置
     */
    @Data
    private static class DbcFileConfig {
        private int deviceIndex = 1;
        private int deviceChannel = 1;
        private List<String> dbcFilePaths;
    }

    /**
     * 模板转换策略接口
     */
    interface ConversionStrategy {
        String convert(Matcher matcher);
    }


    private List<Device> previousCanDevices = new ArrayList<>();
    private List<DbcFileConfig> dbcFileConfigs = new ArrayList<>();
    private static final List<PatternStrategy> PATTERN_LIST = new ArrayList<>();

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    private static final Pattern timePattern = Pattern.compile("(?:等待|持续|等|延迟)(\\d+)(秒|秒钟|s|S)");
    private static final Pattern setVoltagePattern = Pattern.compile("Power=(\\d+\\.?\\d*)V");
    private static final Pattern pwmPattern = Pattern.compile("(?:Check )PWM=(\\d+\\.?\\d*)%");
    private static final Pattern adsToolLogPattern = Pattern.compile("(?:Set )ADSToolLog( start| stop)");
    private static final Pattern canLogPattern = Pattern.compile("(?:Set )CANLog( start| stop)");
    private static final Pattern setMessageValidTimePattern = Pattern.compile("(?:Set )(\\w+)?( loss| recovery)?( for | more than )(\\d+)(秒|秒钟|s|S)");
    private static final Pattern setMessageSignalTimePattern = Pattern.compile("(?:Set )(\\w+):(\\w+)=(\\d+)?( for | more than )(\\d+)(秒|秒钟|s|S)");
    private static final Pattern setSignalRawValuePattern = Pattern.compile("(?:Set )(\\w+):(\\w+)=(0[xX][0-9a-fA-F]+)");
    private static final Pattern setSignalPattern = Pattern.compile("(?:Set )(\\w+):(\\w+)=(\\d+)");
    private static final Pattern signalPattern = Pattern.compile("(\\w+)=(\\d+)");
    private static final Pattern messageSignalPattern = Pattern.compile("(\\w+)-(\\w+)=(\\d+)");
    private static final Pattern voltagePattern = Pattern.compile("测试电压(\\d+\\.?\\d*)V");
    private static final Pattern messageValidPattern = Pattern.compile("(\\w+)(?:报文)?(有效|无效|恢复|丢失)");
    private static final Pattern changeVoltagePattern = Pattern.compile("电压(\\d+\\.?\\d*)V");
    private static final Pattern warningLightNamePattern = Pattern.compile("(\\p{IsHan}+.*?)指示灯(?:保持)?常?(亮|灭|不亮)");
    private static final Pattern warningLightNamePattern2 = Pattern.compile("(屏蔽|开启)(\\p{IsHan}+.*?)");
    private static final Pattern setPowerStatusPattern = Pattern.compile("(?:Set )Power( ON| OFF)");
    private static final Pattern checkMessageSignalRawValuePattern = Pattern.compile("(?:Check )(\\w+):(\\w+)=(0[xX][0-9a-fA-F]+)");
    private static final Pattern checkMessageSignalRangePattern = Pattern.compile("(?:Check )(\\w+):(\\w+)=\\d+\\,?\\d*");


    public BuiltinRuleSequenceConverter() {
        PATTERN_LIST.add(new PatternStrategy(timePattern, new TimeConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(setVoltagePattern, new VoltageConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(pwmPattern, new PWMConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(adsToolLogPattern, new ADSToolLogConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(canLogPattern, new CANLogConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(setMessageValidTimePattern, new MessageValidTimeConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(setMessageSignalTimePattern, new MessageSignalTimeConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(setSignalRawValuePattern, new SetSignalConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(setSignalPattern, new SetSignalConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(messageSignalPattern, new MessageAndSignalConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(signalPattern, new SignalConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(voltagePattern, new VoltageConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(messageValidPattern, new MessageValidConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(changeVoltagePattern, new VoltageConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(warningLightNamePattern, new WarningLightConversionStrategy()));
        PATTERN_LIST.add(new PatternStrategy(warningLightNamePattern2, new WarningLightConversionStrategy2()));
    }

    static class TimeConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            int timeInSeconds = Integer.parseInt(matcher.group(1));
            int timeInMillis = timeInSeconds * 1000;
            return String.format("Wait-%s", timeInMillis);
        }
    }

    class MessageAndSignalConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String messageName = matcher.group(1);
            String signalName = matcher.group(2);
            String signalValue = matcher.group(3);
            if (!dbcFileConfigs.isEmpty()) {
                for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                    Message message = null;
                    for (int i = 0; i < dbcReaders.size(); i++) {
                        message = dbcReaders.get(i).getBus().getMessage(messageName);
                        if (message != null) {
                            break;
                        }
                    }
                    if (message != null) {
                        for (Signal signal : message.getSignals()) {
                            if (signal.getName().equalsIgnoreCase(signalName)) {
                                signalName = signal.getName();
                                break;
                            }
                        }
                        return String.format("CAN%s-%d-Sig-%s-%s-%s",
                                dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                                dbcFileConfig.getDeviceChannel(),
                                message.getName(),
                                signalName,
                                signalValue);
                    }
                }
            }
            return "dbc文件未找到信号:" + matcher.group(0);
        }
    }

    class SetSignalConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String messageId = matcher.group(1);
            String signalName = matcher.group(2);
            String signalValue = matcher.group(3);
            if (!dbcFileConfigs.isEmpty()) {
                for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                    Message message = null;
                    for (int i = 0; i < dbcReaders.size(); i++) {
                        message = dbcReaders.get(i).getBus().getMessage(messageId);
                        if (message != null) {
                            break;
                        }
                    }
                    if (message != null) {
                        for (Signal signal : message.getSignals()) {
                            if (signal.getName().equalsIgnoreCase(signalName)) {
                                signalName = signal.getName();
                                break;
                            }
                        }
                        return String.format("CAN%s-%d-Sig-%s-%s-%s",
                                dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                                dbcFileConfig.getDeviceChannel(),
                                message.getName(),
                                signalName,
                                signalValue);
                    }
                }
            }
            return "dbc文件未找到报文:" + matcher.group(1);
        }
    }

    class SignalConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String signalName = matcher.group(1);
            String signalValue = matcher.group(2);
            if (!dbcFileConfigs.isEmpty()) {
                for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                    Message message = null;
                    for (int i = 0; i < dbcReaders.size(); i++) {
                        message = dbcReaders.get(i).getBus().getSignal(signalName);
                        if (message != null) {
                            break;
                        }
                    }
                    if (message != null) {
                        for (Signal signal : message.getSignals()) {
                            if (signal.getName().equalsIgnoreCase(signalName)) {
                                signalName = signal.getName();
                                break;
                            }
                        }
                        return String.format("CAN%s-%d-Sig-%s-%s-%s",
                                dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                                dbcFileConfig.getDeviceChannel(),
                                message.getName(),
                                signalName,
                                signalValue);
                    }
                }
            }
            return "dbc文件未找到信号:" + matcher.group(0);
        }
    }

    static class PWMConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String pwm = matcher.group(1);
            return String.format("ADSTool-GetPWM-%s%%", pwm);
        }
    }

    static class ADSToolLogConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String logFlag = matcher.group(1).trim();
            return String.format("ADSTool-SetADSToolLog-%s", logFlag.equals("start") ? 0 : 1);
        }
    }

    static class CANLogConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String logFlag = matcher.group(1).trim();
            return String.format("CAN-CANLog-%s", logFlag.equals("start") ? 0 : 1);
        }
    }

    static class VoltageConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String voltage = matcher.group(1);
            return String.format("Pwr-SupplyPower-%s", voltage);
        }
    }

    class MessageValidConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String message = matcher.group(1);
            String valid = matcher.group(2);
            for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                Message msg = null;
                for (int i = 0; i < dbcReaders.size(); i++) {
                    msg = dbcReaders.get(i).getBus().getMessage(message);
                    if (message != null) {
                        break;
                    }
                }
                if (msg != null) {
                    return String.format("CAN%s-%d-Msg-%s-%s",
                            dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                            dbcFileConfig.getDeviceChannel(),
                            msg.getName(),
                            valid.equals("有效") || valid.equals("恢复") ? 1 : 0);
                }
            }
            return "dbc文件未找到报文:" + matcher.group(0);
        }
    }


    class MessageValidTimeConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String messageId = matcher.group(1);
            String valid = matcher.group(2).trim();
            String wait = matcher.group(3);
            int timeInSeconds = Integer.parseInt(matcher.group(4));
            int timeInMillis = timeInSeconds * 1000;
            if (!dbcFileConfigs.isEmpty()) {
                for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                    Message message = null;
                    for (int i = 0; i < dbcReaders.size(); i++) {
                        message = dbcReaders.get(i).getBus().getMessage(messageId);
                        if (message != null) {
                            break;
                        }
                    }
                    if (message != null) {
                        return String.format("CAN%s-%d-Msg-%s-%s-Wait-%s",
                                dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                                dbcFileConfig.getDeviceChannel(),
                                message.getName(),
                                valid.equals("loss") || valid.equals("recovery") ? 1 : 0,
                                wait.equals(" for ") ? timeInMillis : timeInMillis + 1000);
                    }
                }
            }
            return "dbc文件未找到报文:" + matcher.group(1);
        }
    }

    class MessageSignalTimeConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String messageId = matcher.group(1);
            String signalName = matcher.group(2);
            String signalValue = matcher.group(3);
            String wait = matcher.group(4);
            int timeInSeconds = Integer.parseInt(matcher.group(5));
            int timeInMillis = timeInSeconds * 1000;
            if (!dbcFileConfigs.isEmpty()) {
                for (DbcFileConfig dbcFileConfig : dbcFileConfigs) {
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcFileConfig.getDbcFilePaths());
                    Message message = null;
                    for (int i = 0; i < dbcReaders.size(); i++) {
                        message = dbcReaders.get(i).getBus().getMessage(messageId);
                        if (message != null) {
                            break;
                        }
                    }
                    if (message != null) {
                        for (Signal signal : message.getSignals()) {
                            if (signal.getName().equalsIgnoreCase(signalName)) {
                                signalName = signal.getName();
                                break;
                            }
                        }
                        return String.format("CAN%s-%d-Sig-%s-%s-%s-Wait-%s",
                                dbcFileConfig.getDeviceIndex() == 1 ? "" : "#" + dbcFileConfig.getDeviceIndex(),
                                dbcFileConfig.getDeviceChannel(),
                                message.getName(),
                                signalName,
                                signalValue,
                                wait.equals(" for ") ? timeInMillis : timeInMillis + 1000);
                    }
                }
            }
            return "dbc文件未找到报文:" + matcher.group(1);

        }
    }

    static class WarningLightConversionStrategy implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String patternName = matcher.group(1).replaceAll("[(（]", "(").replaceAll("[)）]", ")");
            String state = matcher.group(2);
            if (state.equals("亮")) {
                return String.format("Vision-Pattern-%s", patternName);
            } else {
                return String.format("Vision-Pattern-NoExist-%s", patternName);
            }
        }
    }

    static class WarningLightConversionStrategy2 implements ConversionStrategy {
        @Override
        public String convert(Matcher matcher) {
            String enabled = matcher.group(1);
            String patternName = matcher.group(2)
                    .replaceAll("[(（]", "(")
                    .replaceAll("[)）]", ")")
                    .replaceAll("指示灯功能", "")
                    .replaceAll("指示灯", "");
            if (enabled.equals("开启")) {
                return String.format("Vision-Pattern-%s", patternName);
            } else {
                return String.format("Vision-Pattern-NoExist-%s", patternName);
            }
        }
    }


    /**
     * 将自然语言句子转换成动作序列
     *
     * @param naturalLanguage 自然语言
     * @return
     */
    @Override
    public String convertLine(String naturalLanguage) {
        String preprocessedInput = naturalLanguage.trim();
        for (PatternStrategy patternStrategy : PATTERN_LIST) {
            Matcher matcher = patternStrategy.pattern.matcher(preprocessedInput);
            if (matcher.find()) {
                ConversionStrategy strategy = patternStrategy.strategy;
                if (strategy != null) {
                    return strategy.convert(matcher);
                }
            }
        }
        return "//无法匹配:" + naturalLanguage;
    }


    /**
     * 获取所有DBC文件配置
     *
     * @return List<DbcFileConfig>
     */
    @SuppressWarnings("unchecked")
    private List<DbcFileConfig> getDbcConfig() {
        List<Device> canDevices = deviceRegisterManager.getDevicesByType(DeviceType.DEVICE_CAN);
        // 检查canDevices是否发生变化
        if (canDevices.equals(previousCanDevices)) {
            return dbcFileConfigs; // 没有变化，返回缓存的结果
        }
        List<DbcFileConfig> dbcFileConfigs = new ArrayList<>();
        canDevices.sort(Comparator.comparingInt(Device::getDeviceIndex));
        for (Device device : canDevices) {
            ConfigurableDevice<CanConfig> configurableDevice = (ConfigurableDevice<CanConfig>) device;
            CanConfig canConfig = configurableDevice.getDeviceConfig();
            if (canConfig == null) {
                continue;
            }
            for (Map.Entry<String, DbcConfig> entry : canConfig.getDbcConfigs().entrySet()) {
                List<String> dbcPaths = entry.getValue().getDbcPaths();
                if (dbcPaths.isEmpty()) {
                    continue;
                }
                DbcFileConfig dbcFileConfig = new DbcFileConfig();
                dbcFileConfig.setDeviceIndex(configurableDevice.getDeviceIndex());
                dbcFileConfig.setDeviceChannel(Integer.parseInt(entry.getKey()));
                dbcFileConfig.setDbcFilePaths(dbcPaths);
                dbcFileConfigs.add(dbcFileConfig);
            }
        }
        previousCanDevices = new ArrayList<>(canDevices);
        return dbcFileConfigs;
    }

    /**
     * 将多个连续动作序列转换成动作序列列表
     *
     * @param actionSequenceSimpleContext 动作序列上下文
     * @return
     */
    @Override
    public List<String> convertAll(ActionSequenceSimpleContext actionSequenceSimpleContext) {
        dbcFileConfigs = getDbcConfig();
        List<String> actionSequenceList = new ArrayList<>();
        actionSequenceList.add(convertParagraph(actionSequenceSimpleContext.getPrecondition()));
        actionSequenceList.add(convertParagraph(actionSequenceSimpleContext.getOperationStep()));
        actionSequenceList.add(convertParagraph(actionSequenceSimpleContext.getExpectResult()));
        return actionSequenceList;
    }

    public static void main(String[] args) {
        String s = "1.屏蔽指示灯报警功能\n" +
                "1.开启指示灯报警功能\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯亮，红色\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "3.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "2.IND-B -1被动安全系统（SRS）指示灯亮3s\n" +
                "3.IND-B -1被动安全系统（SRS）指示灯灭\"\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "1.自检灯，自检过程中红色常亮，自检时长6s，自检完成后灭3s常亮\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "6.9s后IND-B -1被动安全系统（SRS）指示灯亮\"\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯亮\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "3.IND-B -1被动安全系统（SRS）指示灯亮\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "3.IND-B -1被动安全系统（SRS）指示灯亮\n" +
                "1.IND-B -1被动安全系统（SRS）指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯常亮，红色\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "2.IND-B-2座椅安全带指示灯常亮4s\n" +
                "3.IND-B-2座椅安全带指示灯闪烁95s后常亮\"\n" +
                "2.IND-B-2座椅安全带指示灯1Hz闪烁10s\n" +
                "3.IND-B-2座椅安全带指示灯常亮\"\n" +
                "2.IND-B-2座椅安全带指示灯常亮\n" +
                "4.IND-B-2座椅安全带指示灯保持常亮\n" +
                "5.IND-B-2座椅安全带指示灯1Hz闪烁，95s后常亮\"\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁\n" +
                "4.IND-B-2座椅安全带指示灯1Hz闪烁\n" +
                "5.IND-B-2座椅安全带指示灯亮\"\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.非自检灯，自检过程熄灭，自检完成后IND-B-2座椅安全带指示灯1Hz闪烁\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "7.IND-B-2座椅安全带指示灯常亮\"\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "3.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "3.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯常亮，红色\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "2.IND-B-2座椅安全带指示灯常亮\n" +
                "3.IND-B-2座椅安全带指示灯闪烁95s后常亮\"\n" +
                "2.IND-B-2座椅安全带指示灯1Hz闪烁10s\n" +
                "3.IND-B-2座椅安全带指示灯亮\"\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "3.IND-B-2座椅安全带指示灯保持亮\n" +
                "5.IND-B-2座椅安全带指示灯1Hz闪烁，95s后常亮\"\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁\n" +
                "3.IND-B-2座椅安全带指示灯1Hz闪烁\n" +
                "5.IND-B-2座椅安全带指示灯常亮\"\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "1.非自检灯，自检过程熄灭，自检完成后根据信号值显示，IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "6.IND-B-2座椅安全带指示灯常亮\"\n" +
                "1.IND-B-2座椅安全带指示灯不亮\n" +
                "1.IND-B-2座椅安全带指示灯亮\n" +
                "1.IND-B-2座椅安全带指示灯不亮\n" +
                "1.IND-B-2座椅安全带指示灯亮\n" +
                "1.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "3.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "3.IND-B-2座椅安全带指示灯常亮\n" +
                "1.IND-B-2座椅安全带指示灯灭\n" +
                "2.屏蔽座椅安全带指示灯功能\n" +
                "2.屏蔽座椅安全带指示灯功能\n" +
                "1.开启座椅安全带指示灯功能\n" +
                "1.将后排座椅安全带数量配置为0\n" +
                "1.将后排座椅安全带数量配置为2\n" +
                "1.将后排座椅安全带数量配置为3\n" +
                "2.IND-B-2后排左座椅安全带指示灯亮，红色\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "2.IND-B-2后排左座椅安全带指示灯亮\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "2.IND-B-2后排右座椅安全带指示灯亮\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n" +
                "2.IND-B-2后排右座椅安全带指示灯亮\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n" +
                "2.IND-B-2后排中座椅安全带指示灯亮\n" +
                "1.IND-B-2后排中座椅安全带指示灯灭\n" +
                "1.IND-B-2后排中座椅安全带指示灯灭\n" +
                "2.IND-B-2后排中座椅安全带指示灯亮\n" +
                "1.IND-B-2后排中座椅安全带指示灯灭\n" +
                "1.IND-B-2后排中座椅安全带指示灯灭\n" +
                "2.IND-B-2后排左座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "2.IND-B-2后排左座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "1.IND-B-2后排左座椅安全带指示灯灭\n" +
                "2.IND-B-2后排右座椅安全带指示灯1Hz闪烁95s后常亮\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n" +
                "1.IND-B-2后排右座椅安全带指示灯灭\n";

//        s = "1.UMM_UsageModeSt=1/3\n" +
//                "2.测试电压13.5V\n" +
//                "3.配置有效\n" +
//                "4.CCU_8报文有效\n" +
//                "5.TPMS_SystemWarningLamp=0\n" +
//                "6.TPMS_TireWarningLamp=1";
        s = "1.Power=13.4V\n" +
                "2.Check PWM=80%\n" +
                "3.0x474:ZR_DimOffs_DU_1=-5(0x0)\n" +
                "4.Set 0x5F0:DI_KL_58xd=255 more than 5s\n" +
                "5.Set 0x5F0 loss more than 5s\n" +
                "6.Set 0x474:ZR_DimOffs_DU_1=122 more than 5s\n" +
                "7.Set 0x5F0:DI_KL_58xd=50";

        String[] inputs = s.split("\n");
        BuiltinRuleSequenceConverter converter = new BuiltinRuleSequenceConverter();
        for (String input : inputs) {
            String result = converter.convertLine(input);
            System.out.println(result);
        }
    }
}
