package com.desaysv.workserver.result;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ActionSequenceCheckResults {

    private final Map<Map<Integer, String>, ActionSequenceResultSet> results = new LinkedHashMap<>();

    public void clear() {
        results.clear();
    }

    public ActionSequenceResultSet getResultSetByCheckId(Map<Integer, String> checkIdMap) {
//        return results.computeIfAbsent(checkId, k -> new ActionSequenceResultSet());
        return results.computeIfAbsent(checkIdMap, k -> new ActionSequenceResultSet());
    }

}
