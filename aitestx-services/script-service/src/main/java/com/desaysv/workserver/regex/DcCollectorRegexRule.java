package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class DcCollectorRegexRule extends BaseRegexRule {
    public static final String GET_CURRENT = wholeCombine(group(NUMBER), RegexRuleConstants.GET_CURRENT, group(CURRENT));
    public static final String GET_VOLTAGE = wholeCombine(group(NUMBER), RegexRuleConstants.GET_VOLTAGE, group(VOLTAGE));
    public static final String GET_VOLTAGE_RANDOM = wholeCombine(group(NUMBER), RegexRuleConstants.GET_VOLTAGE, RegexRuleConstants.RANDOM, group(VOLTAGE), group(VOLTAGE));
    public static final String GET_CURRENT_RANDOM = wholeCombine(group(NUMBER), RegexRuleConstants.GET_CURRENT, RegexRuleConstants.RANDOM, group(CURRENT), group(CURRENT));
    public static final String GET_VOLTAGE_RANGE = wholeCombine(group(NUMBER), RegexRuleConstants.GET_VOLTAGE, group(VOLTAGE), RegexRuleConstants.RANGE, group(VOLTAGE));
    public static final String GET_CURRENT_RANGE = wholeCombine(group(NUMBER), RegexRuleConstants.GET_CURRENT, group(CURRENT), RegexRuleConstants.RANGE, group(CURRENT));
}
