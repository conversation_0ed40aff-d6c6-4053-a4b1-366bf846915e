package com.desaysv.workserver.context;

import com.desaysv.workserver.entity.SmokingTestConfigModel;
import com.desaysv.workserver.filemanager.project.ActionSequenceTestConfig;
import com.desaysv.workserver.model.ExcelCaseModel;
import lombok.Data;
import java.util.List;

/**
 * 测试序列上下文
 */
@Data
public class ActionSequenceContext {
    public static final int ALL = 0;
    public static final int PRECONDITION = 1;
    public static final int OPERATION_STEP = 2;
    public static final int EXPECT_RESULT = 3;
    public static final int PRESSURE_TEST_MODE = 0;

    private String uuid;
    private String tcId;
    private String testKey;
    private String tableName;
    private List<TestStep> precondition;
    private List<TestStep> operationStep;
    private List<TestStep> expectResult;
    private boolean pressureMode;  //压测、功能
    private boolean simulated;
    private boolean pauseWhenTestFailed;
    private boolean singleCase;
    private boolean cancelSingleTestCheckAll;
    private boolean cancelSingleTestCheckAny;
    private int sequenceType;
    private int row;
    private int totalCycleTimes;
    private int currentCycleTimes;
    private int rowCurrentTestTimes;
    private int rowTotalTestTimes;
    private double testNo;
    private String userLogPath;
    private String projectName;

    private ExcelCaseModel excelCaseModel;
    private ActionSequenceTestConfig testConfig;
    private SmokingTestConfigModel smokingTestConfigModel;

    public ActionSequenceContext removeAnnotation() {
        filterSteps(precondition);
        filterSteps(operationStep);
        filterSteps(expectResult);
        return this;
    }

    public ActionSequenceContext removeAndTrimAfterSlashSlash() {
        filterAfterSlashSlash(precondition);
        filterAfterSlashSlash(operationStep);
        filterAfterSlashSlash(expectResult);
        return this;
    }

    public void filterAfterSlashSlash(List<TestStep> steps) {
        if (steps == null) return;
        for (TestStep step : steps) {
            String testStepStr = step.getTestStep();
            int index = testStepStr.indexOf("//");
            if (index != -1) {
                testStepStr = testStepStr.substring(0, index).trim();
            } else {
                testStepStr = testStepStr.trim();  // 如果没有找到 "//"，仅清除首尾空格
            }
            step.setTestStep(testStepStr);
        }
    }


    private void filterSteps(List<TestStep> steps) {
        if (steps != null) {
            steps.removeIf(testStep -> testStep != null && testStep.getTestStep() != null && testStep.getTestStep().startsWith("//"));
        }
    }

}
