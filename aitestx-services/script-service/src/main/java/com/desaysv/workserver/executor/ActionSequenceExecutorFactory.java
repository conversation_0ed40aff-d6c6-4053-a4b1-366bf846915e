package com.desaysv.workserver.executor;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.components.SequenceDeviceKeywords;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceExecutionException;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceStopException;
import com.desaysv.workserver.executor.customized.PowerActionSequenceExecutor;
import com.desaysv.workserver.result.ActionSequenceResultSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动作序列执行工厂 - 负责创建和管理不同类型的动作序列执行策略
 * 采用策略模式,支持自定义执行器的扩展
 */
@Service
@Slf4j
@Lazy
public class ActionSequenceExecutorFactory
        implements ApplicationListener<ContextRefreshedEvent>, ActionSequenceExecutorStrategy {

    /**
     * 存储所有注册的执行策略,key为执行器类型
     */
    private static final Map<String, ActionSequenceExecutorStrategy> MAP = new HashMap<>();

    @Autowired
    private BaseActionSequenceExecutor baseActionSequenceExecutor;

    public void setExecutionContext(ExecutionContext executionContext) {
        baseActionSequenceExecutor.setExecutionContext(executionContext);
    }

    public static void addStrategyManually() {
        MAP.put(SequenceDeviceKeywords.POWER, new PowerActionSequenceExecutor());
    }

    /**
     * 根据动作序列获取对应的执行策略
     * 如果没有找到对应的策略则使用基础执行器
     */
    private ActionSequenceExecutorStrategy getActionSequenceExecutorStrategy(ActionSequenceUnit actionSequence) {
        ActionSequenceExecutorStrategy strategy = null;
        if (actionSequence.getActionSequenceHeader() != null) {
            strategy = MAP.get(actionSequence.getActionSequenceHeader().getExecutorName().toLowerCase());
        }
        if (strategy == null) {
            strategy = baseActionSequenceExecutor;
        }
        return strategy;
    }

    /**
     * 编译动作序列
     *
     * @param actionSequence 动作序列
     * @return
     */
    @Override
    public boolean check(ActionSequenceUnit actionSequence, List<String> resultCheckMarker) {
        ActionSequenceExecutorStrategy strategy = getActionSequenceExecutorStrategy(actionSequence);
        return strategy.check(actionSequence, resultCheckMarker);
    }

    /**
     * 执行动作序列
     *
     * @param actionSequence 动作序列
     * @return
     */
    @Override
    public boolean execute(ActionSequenceUnit actionSequence,
                           ActionSequenceExecutorContext actionSequenceExecutorContext,
                           ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                           ActionSequenceExecutorListener actionSequenceExecutorListener,
                           ActionSequenceResultSet actionSequenceResultSet)
            throws ActionSequenceExecutionException, ActionSequenceStopException {
        ActionSequenceExecutorStrategy strategy = getActionSequenceExecutorStrategy(actionSequence);
        return strategy.execute(actionSequence,
                actionSequenceExecutorContext,
                actionSequenceExecutorHandler,
                actionSequenceExecutorListener,
                actionSequenceResultSet);
    }

    /**
     * Spring容器启动时自动注册所有带@ActionSequenceExecutor注解的执行策略
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // 获取bean工厂带LiveViewCode注解的service实例
        Map<String, Object> beansWithAnnotation = contextRefreshedEvent.getApplicationContext().getBeansWithAnnotation(ActionSequenceExecutor.class);
        beansWithAnnotation.forEach((key, value) -> {
            // 获取实现类注解里的type
//            ActionSequenceParser annotation = AnnotationUtils.findAnnotation(value.getClass(), ActionSequenceParser.class);
            // cglib代理无法获取到注解，这里需要使用spring自带的工具类来操作
            ActionSequenceExecutor annotation = value.getClass().getAnnotation(ActionSequenceExecutor.class);
            String type = annotation.type().toLowerCase();
            boolean flag = annotation.flag();
            if (flag) {
                if (!MAP.containsKey(type)) {
                    MAP.put(type, (ActionSequenceExecutorStrategy) value);
                } else {
                    log.error("type[{}]出现重复，请检查注解type绑定", type);
                }
            } else {
                log.info("策略类[{}]标注失效，不注册策略工厂", value);
            }
        });
    }
}