package com.desaysv.workserver.finder;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.ActionSequenceHeader;
import com.desaysv.workserver.components.DeviceActionSequenceHeader;
import com.desaysv.workserver.entity.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.desaysv.workserver.finder.OperationTargetFinderManager.finderDeviceTypeMap;
import static com.desaysv.workserver.finder.OperationTargetFinderManager.virtualOperationTargetMap;

@Service
@Lazy
@Slf4j
public class BaseDeviceFindStrategy implements DeviceFindStrategy {


    @Override
    public OperationTarget find(ActionSequenceHeader actionSequenceHeader) {
        DeviceActionSequenceHeader deviceActionSequenceHeader = (DeviceActionSequenceHeader) actionSequenceHeader;
        OperationTargetFinderManager.SequenceDevice sequenceDevice = finderDeviceTypeMap.get(deviceActionSequenceHeader.getExecutorName().toLowerCase());
        if (sequenceDevice != null) {
            for (String deviceType : sequenceDevice.getDeviceTypes()) {
                if (sequenceDevice.isVirtual()) {
                    //虚拟设备
                    return virtualOperationTargetMap.get(deviceType);
                } else {
                    Map<Integer, Device> deviceMap = Device.getDeviceInstancesMap().get(deviceType);
                    if (deviceMap != null) {
                        return deviceMap.get(deviceActionSequenceHeader.getDeviceOrder());
                    }
                }
            }
        }
        log.warn("没有找到操作目标映射:{}", actionSequenceHeader);
        return null;
    }

}
