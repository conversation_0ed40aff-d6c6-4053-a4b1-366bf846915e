package com.desaysv.workserver;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;

@Slf4j
public class TCPClient2 {
    public static void main(String[] args) {
        String host = "localhost";
        int port = 12345;
        try (
                Socket socket = new Socket(host, port);
                PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
                BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                BufferedReader stdIn = new BufferedReader(new InputStreamReader(System.in))
        ) {
            String userInput;
            String serverResponse;
            while ((serverResponse = in.readLine()) != null) {
                System.out.println("Server: " + serverResponse);
                if ((userInput = stdIn.readLine()) != null) {
                    out.println(userInput);
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}