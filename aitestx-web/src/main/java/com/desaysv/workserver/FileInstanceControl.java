package com.desaysv.workserver;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.util.concurrent.TimeUnit;

/**
 * 文件实例控制
 */
public class FileInstanceControl {
    private static final Logger log = LoggerFactory.getLogger(FileInstanceControl.class.getSimpleName());
    private static final String LOCK_FILE_PATH = System.getProperty("user.dir") + File.separator + "application.lock";
    private static FileChannel channel;
    private static FileLock lock;

    public static boolean start() {
        File file = new File(LOCK_FILE_PATH);

        // 如果文件存在，尝试获取锁
        if (file.exists()) {
            try (FileChannel existingChannel = new RandomAccessFile(file, "rw").getChannel()) {
                lock = existingChannel.tryLock();
                if (lock == null) {
                    String pid = new BufferedReader(new FileReader(file)).readLine();
                    if (isProcessRunning(pid)) {
                        log.warn("Another instance (PID: {}) is running. Exiting.", pid);
                        System.exit(0);
                        return false;
                    } else {
                        log.warn("Stale lock file detected. Removing stale lock file.");
                        file.delete(); // 删除陈旧的锁文件
                    }
                } else {
                    lock.release(); // 释放试图获取的锁
                }
            } catch (Exception e) {
                log.warn("Lock file exists but cannot be locked. Removing stale lock file.", e);
                file.delete(); // 删除锁文件
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException ex) {
                    log.error(e.getMessage(), e);
                }
                System.exit(-1);
            }
        }

        try {
            channel = new RandomAccessFile(file, "rw").getChannel();
            lock = channel.lock(); // 获取锁

            // 写入当前进程的 PID
            try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
                writer.println(getProcessId());
            }

            Runtime.getRuntime().addShutdownHook(new Thread(FileInstanceControl::stop));
            return true;
        } catch (Exception e) {
            log.error("Error while starting instance control", e);
            return false;
        }
    }

    public static void stop() {
        try {
            if (lock != null) {
                lock.release();
            }
            if (channel != null) {
                channel.close();
            }
            new File(LOCK_FILE_PATH).delete(); // 删除锁文件
            log.info("Instance control stopped, lock released.");
        } catch (Exception e) {
            log.error("Error while stopping instance control", e);
        }
    }

    private static String getProcessId() {
        return java.lang.management.ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
    }

    private static boolean isProcessRunning(String pid) {
        try {
            Process process = Runtime.getRuntime().exec("tasklist /FI \"PID eq " + pid + "\"");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains(pid)) {
                        return true;
                    }
                }
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("Failed to check if process {} is running.", pid, e);
        }
        return false;
    }
}


