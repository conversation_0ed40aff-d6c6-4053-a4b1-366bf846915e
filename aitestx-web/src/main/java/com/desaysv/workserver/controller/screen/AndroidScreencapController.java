package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.dto.VideoStreamRequest;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.AndroidVideoStreamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;

/**
 * Android投屏控制器
 * 提供Android设备视频流的流式接口
 */
@Slf4j
@RestController
@RequestMapping("/android/screencap")
@Lazy
public class AndroidScreencapController {

    @Autowired
    private AndroidVideoStreamService androidVideoStreamService;

    /**
     * 启动Android设备视频流
     *
     * @param deviceName Android设备名称/序列号
     * @return 流式视频响应
     */
    @GetMapping("/stream/{deviceName}")
    public ResponseEntity<StreamingResponseBody> startVideoStream(@PathVariable String deviceName) {
        return startVideoStreamWithParams(deviceName, 0, 0, 0);
    }

    /**
     * 启动Android设备视频流（带参数）
     *
     * @param deviceName Android设备名称/序列号
     * @param width 视频宽度（0表示使用默认值）
     * @param height 视频高度（0表示使用默认值）
     * @param bitRate 视频比特率（0表示使用默认值）
     * @return 流式视频响应
     */
    @GetMapping("/stream/{deviceName}/params")
    public ResponseEntity<StreamingResponseBody> startVideoStreamWithParams(
            @PathVariable String deviceName,
            @RequestParam(defaultValue = "0") int width,
            @RequestParam(defaultValue = "0") int height,
            @RequestParam(defaultValue = "0") int bitRate) {

        VideoStreamRequest request = new VideoStreamRequest(deviceName, width, height, bitRate);
        return processVideoStreamRequest(request);
    }

    /**
     * 启动Android设备视频流（POST方式，支持完整参数）
     *
     * @param request 视频流请求参数
     * @return 流式视频响应
     */
    @PostMapping("/stream")
    public ResponseEntity<StreamingResponseBody> startVideoStreamWithRequest(@Valid @RequestBody VideoStreamRequest request) {
        return processVideoStreamRequest(request);
    }

    /**
     * 处理视频流请求的核心方法
     *
     * @param request 视频流请求参数
     * @return 流式视频响应
     */
    private ResponseEntity<StreamingResponseBody> processVideoStreamRequest(VideoStreamRequest request) {
        log.info("请求Android设备视频流: {}", request);

        try {
            // 启动视频流
            InputStream videoStream = androidVideoStreamService.startVideoStream(
                    request.getDeviceName(),
                    request.getWidth(),
                    request.getHeight(),
                    request.getBitRate()
            );

            if (videoStream == null) {
                log.error("无法启动Android设备视频流: {}", request.getDeviceName());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            // 创建流式响应体
            StreamingResponseBody streamingResponseBody = outputStream -> {
                try (InputStream inputStream = videoStream) {
                    byte[] buffer = new byte[request.getBufferSize()];
                    int bytesRead;

                    log.info("开始传输Android设备视频流: {}", request.getDeviceName());

                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        outputStream.flush();
                    }

                    log.info("Android设备视频流传输完成: {}", request.getDeviceName());
                } catch (IOException e) {
                    log.error("Android设备视频流传输异常: 设备={}, 错误={}", request.getDeviceName(), e.getMessage());
                    throw e;
                } finally {
                    // 确保停止视频流
                    try {
                        androidVideoStreamService.stopVideoStream(request.getDeviceName());
                        log.info("已停止Android设备视频流: {}", request.getDeviceName());
                    } catch (Exception e) {
                        log.warn("停止Android设备视频流时出错: 设备={}, 错误={}", request.getDeviceName(), e.getMessage());
                    }
                }
            };

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.set("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.set("Pragma", "no-cache");
            headers.set("Expires", "0");
            headers.set("Connection", "keep-alive");
            headers.set("X-Device-Name", request.getDeviceName());
            headers.set("X-Video-Resolution", request.getResolutionString());
            headers.set("X-Video-BitRate", request.getBitRateString());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(streamingResponseBody);

        } catch (IllegalArgumentException e) {
            log.error("设备参数错误: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (IOException e) {
            log.error("启动Android设备视频流失败: 设备={}, 错误={}", request.getDeviceName(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("处理Android设备视频流请求时发生未知错误: 设备={}, 错误={}", request.getDeviceName(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 停止Android设备视频流
     *
     * @param deviceName Android设备名称/序列号
     * @return 操作结果
     */
    @PostMapping("/stop/{deviceName}")
    public ResultEntity<String> stopVideoStream(@PathVariable String deviceName) {
        log.info("请求停止Android设备视频流: {}", deviceName);

        try {
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                String message = String.format("未找到Android设备: %s", deviceName);
                log.error(message);
                return ResultEntity.fail(message, null);
            }

            androidDevice.stopVideoStream();
            String message = String.format("已停止Android设备视频流: %s", deviceName);
            log.info(message);
            return ResultEntity.ok(message, deviceName);

        } catch (Exception e) {
            String message = String.format("停止Android设备视频流失败: %s, 错误: %s", deviceName, e.getMessage());
            log.error(message, e);
            return ResultEntity.fail(message, null);
        }
    }

    /**
     * 检查Android设备视频流状态
     *
     * @param deviceName Android设备名称/序列号
     * @return 视频流状态
     */
    @GetMapping("/status/{deviceName}")
    public ResultEntity<Boolean> getVideoStreamStatus(@PathVariable String deviceName) {
        log.info("查询Android设备视频流状态: {}", deviceName);

        try {
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                String message = String.format("未找到Android设备: %s", deviceName);
                log.error(message);
                return ResultEntity.fail(message, false);
            }

            boolean isRunning = androidDevice.isVideoStreamRunning();
            String message = String.format("Android设备视频流状态: %s, 运行中: %s", deviceName, isRunning);
            log.info(message);
            return ResultEntity.ok(message, isRunning);

        } catch (Exception e) {
            String message = String.format("查询Android设备视频流状态失败: %s, 错误: %s", deviceName, e.getMessage());
            log.error(message, e);
            return ResultEntity.fail(message, false);
        }
    }

    /**
     * 获取Android设备实例
     *
     * @param deviceName 设备名称
     * @return Android设备实例，如果不存在或不是Android设备则返回null
     */
    private AndroidDevice getAndroidDevice(String deviceName) {
        try {
            Object device = deviceRegisterManager.getDevice(deviceName);
            if (device instanceof AndroidDevice) {
                return (AndroidDevice) device;
            } else {
                log.warn("设备 {} 不是Android设备类型: {}", deviceName,
                        device != null ? device.getClass().getSimpleName() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取Android设备失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }
}
