package com.desaysv.workserver.controller.config;

import com.desaysv.workserver.GlobalConfig;
import com.desaysv.workserver.GlobalConfigHolder;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * 全局配置管理
 */
@Slf4j
@RestController
@RequestMapping("/config")
@Lazy
public class GlobalConfigController {

    @PostMapping("/global/update")
    public ResultEntity<String> updateGlobalConfig(@RequestBody GlobalConfig globalConfig) {
        log.info("更新全局配置:{}", globalConfig);
        GlobalConfigHolder.setGlobalConfig(globalConfig);
        GlobalConfigHolder.save();
        return ResultEntity.ok();
    }

    @GetMapping("/global/load")
    public ResultEntity<GlobalConfig> loadGlobalConfig() {
        GlobalConfig globalConfig = GlobalConfigHolder.getGlobalConfig();
        log.info("导入全局配置:{}", globalConfig);
        return ResultEntity.ok(globalConfig);
    }
}
