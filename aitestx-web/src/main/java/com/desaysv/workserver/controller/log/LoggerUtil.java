package com.desaysv.workserver.controller.log;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.filter.LevelFilter;
import ch.qos.logback.core.FileAppender;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

public class LoggerUtil {

    public static FileAppender configureLogger(String loggerName, String logFolderName, String logFileRootPath) {
        Logger logger = (Logger) LoggerFactory.getLogger(loggerName);
        LoggerContext loggerContext = logger.getLoggerContext();

        // 配置文件日志
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String logFilePath = logFileRootPath + "\\" + logFolderName + "\\" + timestamp + ".log";

        FileAppender fileAppender = new FileAppender();
        fileAppender.setContext(loggerContext);
        fileAppender.setName("FileLogger");
        fileAppender.setFile(logFilePath);

        PatternLayoutEncoder fileEncoder = new PatternLayoutEncoder();
        fileEncoder.setContext(loggerContext);
        fileEncoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n"); // 去掉线程、级别和类名
        fileEncoder.start();

        fileAppender.setEncoder(fileEncoder);

        LevelFilter levelFilter = new LevelFilter();
        levelFilter.setLevel(ch.qos.logback.classic.Level.INFO);
        levelFilter.setOnMatch(ch.qos.logback.core.spi.FilterReply.ACCEPT);
        levelFilter.setOnMismatch(ch.qos.logback.core.spi.FilterReply.DENY);
        levelFilter.start();

        fileAppender.addFilter(levelFilter);
        fileAppender.start();

        // 仅添加文件日志的 Appender，不影响控制台输出
        logger.addAppender(fileAppender);

        return fileAppender;
    }

    public static void cleanupLogger(String loggerName, FileAppender fileAppender) {
        Logger logger = (Logger) LoggerFactory.getLogger(loggerName);
        logger.detachAppender(fileAppender);
        fileAppender.stop();
    }
}