package com.desaysv.workserver.controller.test.protocol.service;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ExecutionProtocolInterpreterConfig {

    @Bean("executorNotificator")
    @Primary
    public ExecutorNotificator executorNotificator() {
        return new ExecutorNotificator();
    }

    @Bean("threadPoolManager")
    @Primary
    public ThreadPoolManager threadPoolManager() {
        return new ThreadPoolManager("mainThreadPoolManager");
    }

    /**
     * 以下为调试使用Bean
     */
    @Bean("debugExecutorNotificator")
    public ExecutorNotificator debugExecutorNotificator() {
        return new ExecutorNotificator();
    }

    @Bean("debugThreadPoolManager")
    public ThreadPoolManager debugThreadPoolManager() {
        return new ThreadPoolManager("debugThreadPoolManager");
    }

}
