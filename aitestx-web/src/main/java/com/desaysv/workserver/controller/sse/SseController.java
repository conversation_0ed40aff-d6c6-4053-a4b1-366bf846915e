package com.desaysv.workserver.controller.sse;

import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE接口
 */
@RestController
@RequestMapping("/sse")
@Lazy
@Slf4j
public class SseController {

    @GetMapping(value = "/subscribe", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    public SseEmitter subscribe(@RequestParam("subscribeId") String subscribeId) {
        log.info("addSub subscribeId: {}", subscribeId);
        return SseUtils.addSub(subscribeId);
    }

}
