package com.desaysv.workserver.controller.config;

import com.desaysv.workserver.devices.bus.fdx.CANoeFdxDescription;
import com.desaysv.workserver.devices.bus.fdx.MultiXMLParser;
import com.desaysv.workserver.filemanager.project.FdxFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * FDX配置文件管理
 */
@Slf4j
@RestController
@RequestMapping("/fdxConfig")
@Lazy
public class FdxConfigController {
    @Autowired
    private MultiXMLParser multiXMLParser;

    @PostMapping("/readFdxFile")
    public ResultEntity<List<String>> readFdxFile() {
        FdxFileManager fdxFileManager =  ProjectFileManager.of(FdxFileManager.class);
        if (fdxFileManager.isFdxConfigExist()) {
            CANoeFdxDescription.getInstance().readFdxConfigFile();
            return ResultEntity.ok(CANoeFdxDescription.getInstance().getFdxFiles());
        }
        return ResultEntity.fail();
    }

    @PostMapping("/parse")
    public ResultEntity<String> parse(@RequestBody List<String> filePaths) {
        return MultiXMLParser.parseMultipleXML(filePaths) ? ResultEntity.ok() : ResultEntity.fail();
    }
}
