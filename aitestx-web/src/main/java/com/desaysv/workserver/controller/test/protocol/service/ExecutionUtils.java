package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.CommonOperationMethod;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExecutionUtils {

    public interface ExecutorHandler {
        OperationResult execute(OperationContext operationContext, Operation operation);
    }

    public static void executeOperationList(OperationContext operationContext, List<Operation> operationList, ExecutorHandler executorHandler) {
        SmartPseudoCodeParser<Operation> parser = new SmartPseudoCodeParser<>();
        ConditionGetter<Operation> conditionGetter = new ConditionGetter<Operation>() {
            private final Map<Integer, OperationResult> results = new HashMap<>();

            @Override
            public boolean isBeginKeyword(Operation operation) {
                return operation.getOperationMethod().equals(CommonOperationMethod.BEGIN_LOOP);
            }

            @Override
            public boolean isIfKeyword(Operation operation) {
                return operation.getOperationMethod().equals(CommonOperationMethod.IF_EXPRESSION_SUCCESS);
            }

            @Override
            public boolean isElseKeyword(Operation operation) {
                return operation.getOperationMethod().equals(CommonOperationMethod.ELSE_IF);
            }

            @Override
            public boolean isEndIfKeyword(Operation operation) {
                return operation.getOperationMethod().equals(CommonOperationMethod.END_IF);
            }

            @Override
            public boolean isEndKeyword(Operation operation) {
                return operation.getOperationMethod().equals(CommonOperationMethod.END_LOOP);
            }

            @Override
            public int extractLoopCount(Operation operation) {
                return Integer.parseInt(String.valueOf(operation.getOperationObject()));
            }

            @Override
            public boolean isAnnotation(Operation operation) {
                return operation.isAnnotated();
            }

            //TODO：改成UUID
            @Override
            public boolean execute(int index, Operation operation) {
                System.out.println(operation);
                OperationResult operationResult = executorHandler.execute(operationContext, operation);
                results.put(index, operationResult);
                return operationResult.isOk();
            }

            @Override
            public boolean getIfResult(int index) {
                if (index == 0) {
                    return false;
                }
                //获取上一条步骤结果
                OperationResult operationResult = results.get(index - 1);
                return operationResult.isOk();
            }
        };
        parser.parse(operationList, conditionGetter);
        parser.execute();
    }
}
