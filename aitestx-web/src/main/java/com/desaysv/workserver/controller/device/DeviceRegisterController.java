package com.desaysv.workserver.controller.device;

import cn.hutool.json.JSONUtil;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.bus.CanDevice;
import com.desaysv.workserver.devices.testbox.light.LightTestBoxConfig;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 18:11
 * @description : 设备注册web接口
 * @modified By :
 * @since : 2022-3-16
 */
@Slf4j
@RestController
@RequestMapping("device")
@Lazy
//TODO：提供网页方式
public class DeviceRegisterController {
    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    /**
     * 注册设备
     *
     * @param deviceRegisterForm 设备注册表单
     * @return
     */
//    @Deprecated
    @PostMapping("/register")
    public ResultEntity<Device> registerDevice(@RequestBody DeviceRegisterForm deviceRegisterForm) {
        log.info("接收设备注册信息:\n{}", JSONUtil.formatJsonStr(deviceRegisterForm.toString()));
        String deviceName = deviceRegisterForm.getDeviceName();
//        MyLogger.outputServerLog(String.format("接收设备\"%s\"注册信息", deviceName));
        boolean isRegistered = deviceRegisterManager.isRegistered(deviceName);
        if (!isRegistered) {
            deviceRegisterManager.registerDevice(deviceRegisterForm);
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        log.info("返回设备:{}", device);
        if (device != null) {
            device.setSimulated(deviceRegisterForm.isSimulated());
            String info;
            if (isRegistered) {
                info = String.format("设备%s已经注册过", deviceName);
            } else {
                info = String.format("设备注册成功:%s", deviceName);
            }
            ActionSequencesLoggerUtil.info(info);
            return ResultEntity.ok(info, device);
        } else {
            String info = String.format("设备%s无法创建", deviceName);
            ActionSequencesLoggerUtil.info(info);
            return ResultEntity.fail(info, null);
        }
    }

    /**
     * 注册并打开设备
     *
     * @param deviceRegisterForm 设备注册表单
     * @return
     */
    @PostMapping("/registerAndOpen")
    public ResultEntity<Device> registerAndOpen(@RequestBody DeviceRegisterForm deviceRegisterForm) {
        log.info("接收设备注册和打开信息:{}", deviceRegisterForm);
        String deviceName = deviceRegisterForm.getDeviceName();
        boolean isRegistered = deviceRegisterManager.isRegistered(deviceName);
        if (!isRegistered) {
            deviceRegisterManager.registerDevice(deviceRegisterForm);
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device != null) {
            OperationResult operationResult = device.openForOperationResult();
            if (operationResult.isFailed()) {
                String info = String.format("设备连接失败，取消注册:%s", deviceName);
                log.info(info);
                ActionSequencesLoggerUtil.info(info);
                deviceRegisterManager.removeDevice(deviceName);
                return ResultEntity.fail(operationResult.getMessage(), null);
            } else {
                log.info("已经注册设备:{}", device);
                String info = String.format("设备注册成功:%s", deviceName);
                ActionSequencesLoggerUtil.info(info);
                if (isRegistered) {
                    return ResultEntity.ok(String.format("设备%s已经注册过", deviceName), device);
                } else {
                    return ResultEntity.ok(info, device);
                }
            }
        } else {
            String info = String.format("设备%s无法创建", deviceName);
            ActionSequencesLoggerUtil.info(info);
            return ResultEntity.fail(info, null);
        }
    }

    /**
     * 断开连接设备
     *
     * @param deviceName 设备名
     * @return
     */
    //TODO：统一disconnect和close
    @DeleteMapping("/disconnect")
    public ResultEntity<String> closeDevice(@RequestParam("deviceName") String deviceName) {
        try {
            log.info("正在断开设备:{}", deviceName);
            return deviceRegisterManager.closeDevice(deviceName) ?
                    ResultEntity.ok(String.format("设备%s断开成功", deviceName)) :
                    ResultEntity.fail(String.format("设备%s不存在", deviceName));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultEntity.fail(e.getMessage());
        }
    }

    /**
     * 注销设备
     *
     * @param deviceName 设备名
     * @return
     */
    @DeleteMapping("/unregister")
    public ResultEntity<String> unregisterDevice(@RequestParam("deviceName") String deviceName) {
        try {
            log.info("正在注销设备:{}", deviceName);
            return deviceRegisterManager.unregisterAndCloseDevice(deviceName) ?
                    ResultEntity.ok(String.format("设备%s注销成功", deviceName)) :
                    ResultEntity.fail(String.format("设备%s不存在", deviceName));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultEntity.fail(String.format("设备%s注销失败:%s", deviceName, e));
        }
    }

    /**
     * 注销所有设备
     *
     * @return
     */
    @DeleteMapping("/unregister/all")
    public ResultEntity<String> unregisterAllDevices() {
        if (deviceRegisterManager.unregisterAndCloseAllDevices()) {
            log.info("所有设备注销成功");
            return ResultEntity.ok("所有设备注销成功");
        } else {
            log.info("所有设备注销失败");
            return ResultEntity.fail("所有设备注销失败");
        }
    }

    /**
     * 查询设备
     *
     * @param deviceType 设备类型
     * @return
     */
    @GetMapping("/deviceType/{deviceType}")
    public ResultEntity<List<Device>> getDevices(@PathVariable("deviceType") String deviceType) {
        return ResultEntity.ok(deviceRegisterManager.getAllDevices().stream().
                filter(device -> device.getDeviceType().equals(deviceType)).collect(Collectors.toList()));
    }

    @GetMapping("/deviceName/{deviceName}")
    public ResultEntity<Object> getDevice(@PathVariable("deviceName") String deviceName) {
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device == null) {
            return ResultEntity.fail(String.format("设备%s不存在", deviceName), null);
        } else {
            return ResultEntity.ok(device);
        }
    }

    @GetMapping("/testBoxConfig")
    public ResultEntity<String> getTestBoxConfig() {
        File file = LightTestBoxConfig.getInstance().getLoadConfigExcelFile();
        if (file == null) {
            log.warn("无法获取到测试箱配置文件");
            return new ResultEntity<>();
        }
        log.info("获取测试箱配置文件:{}", file.getAbsolutePath());
        return new ResultEntity<>(file.getAbsolutePath());
    }

    @GetMapping("/dbcFiles")
    public ResultEntity<List<DbcFile>> getDbcFiles() {
        ResultEntity<List<Device>> result = getDevices(DeviceType.DEVICE_CAN);
        if (result.isOk()) {
            List<Device> devices = result.getData();
            List<DbcFile> dbcFiles = new ArrayList<>();
            for (Device device : devices) {
                CanDevice canDevice = (CanDevice) device;
                CanConfig canConfig = canDevice.getDeviceConfig();
                if (canConfig != null) {
                    for (Map.Entry<String, DbcConfig> entry : canConfig.getDbcConfigs().entrySet()) {
                        String channel = entry.getKey();
                        DbcConfig dbcConfig = entry.getValue();

                        if (dbcConfig != null && !dbcConfig.getDbcPaths().isEmpty()) {
                            DbcFile dbcFile = new DbcFile();
                            dbcFile.setFilePath(dbcConfig.getDbcPaths().get(0));
                            dbcFile.setFilePaths(dbcConfig.getDbcPaths());
                            dbcFile.setChannel(Integer.parseInt(channel));
                            dbcFile.setDeviceIndex(device.getDeviceIndex());
                            dbcFiles.add(dbcFile);
                        }
                    }
                }
            }
            return new ResultEntity<>(dbcFiles);
        }
        return new ResultEntity<>(new ArrayList<>());

    }
}
