package com.desaysv.workserver.controller.config;

import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.devices.device_udp.A2LParserUtils;
import com.desaysv.workserver.devices.device_udp.UdpConfigService;
import com.desaysv.workserver.devices.device_udp.VariableInfo;
import com.desaysv.workserver.devices.device_udp.config.UdpConfig;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

/**
 * A2L文件管理
 */
@Slf4j
@RestController
@RequestMapping("/udpConfig")
@Lazy
public class UdpConfigController {

    @Autowired
    private UdpConfigService udpConfigService;

    @PostMapping("/loadA2LConfig")
    public ResultEntity<String> load(@RequestSingleParam String projectName) {
        UdpConfig udpConfig = udpConfigService.loadConfig(projectName);
        return udpConfig.getPath() != null ? ResultEntity.ok(udpConfig.getPath()) : ResultEntity.fail();
    }

    @PostMapping("/parseA2LFile")
    public ResultEntity<String> parse(@RequestBody Map<String, String> map) {
        Map<String, VariableInfo> dataMap = null;
        String filePath = map.get("filePath");
        String projectName = map.get("projectName");
        try {
            dataMap = A2LParserUtils.parse(filePath);
        } catch (IOException e) {
            log.error("解析A2L文件失败", e);
            return ResultEntity.fail();
        }
        if (dataMap.isEmpty()) ResultEntity.fail();
        log.info("解析A2L文件成功,变量总数量为：{}", dataMap.size());
        UdpConfig udpConfig = new UdpConfig();
        udpConfig.setVariables(dataMap);
        udpConfig.setPath(filePath);
        udpConfig.setProject(projectName);
        udpConfigService.writeConfig(udpConfig);
        return ResultEntity.ok();
    }
}
