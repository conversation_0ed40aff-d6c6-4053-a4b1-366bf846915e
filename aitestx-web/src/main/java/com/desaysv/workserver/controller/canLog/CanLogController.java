package com.desaysv.workserver.controller.canLog;

import com.desaysv.workserver.canlog.blflog.service.impl.CanLogParserService;
import com.desaysv.workserver.response.ResultEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/canLog")
public class CanLogController {

    private final CanLogParserService canLogParserService;

    public CanLogController(CanLogParserService canLogParserService) {
        this.canLogParserService = canLogParserService;
    }

    @PostMapping("/parse")
    public ResultEntity<?> parseCanLogByPath(@RequestBody String filePath) {
        return canLogParserService.parseCanLogToJsonResponse(filePath);
    }
}
