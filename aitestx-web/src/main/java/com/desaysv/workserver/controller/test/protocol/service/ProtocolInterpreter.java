package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.execution.EnhancedExecution;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.execution.PairedIndex;
import com.desaysv.workserver.base.nodes.FunctionNode;
import com.desaysv.workserver.base.nodes.NodeType;
import com.desaysv.workserver.base.nodes.OperationNode;
import com.desaysv.workserver.base.nodes.base.LoopBreakNotification;
import com.desaysv.workserver.base.nodes.base.LoopNode;
import com.desaysv.workserver.base.nodes.base.NodeContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.base.operation.method.CommonOperationMethod;
import com.desaysv.workserver.base.operation.method.MethodCollector;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.method.PairedCustomizeFunction;
import com.desaysv.workserver.filemanager.project.OperationGroupFileManager;
import com.desaysv.workserver.protocol.OperationProtocolFactory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.Future;

import static com.desaysv.workserver.constants.AppConstants.RANDOM_VALUE_SEPARATOR;

@EqualsAndHashCode(callSuper = true)
@Data
public abstract class ProtocolInterpreter extends BaseProtocolInterpreter {
    protected final static String SPLITTER_LOG = "-------------------------------------------";
    protected final static String RETRY_CALLBACK = "@预设命令#失败重试";

    @Autowired
    private OperationProtocolFactory operationProtocolFactory;

    private final ExecutorNotificator executorNotificator;

    private final ThreadPoolManager threadPoolManager;

    public ProtocolInterpreter(ThreadPoolManager threadPoolManager, ExecutorNotificator executorNotificator) {
        this.threadPoolManager = threadPoolManager;
        this.executorNotificator = executorNotificator;
    }

    /**
     * 转化为测试脚本节点
     *
     * @param enhancedExecution 增强的执行体
     * @return 测试脚本节点
     */
    public static LoopNode transferToNode(EnhancedExecution enhancedExecution, LoopNode rootNode, int totalCycle) {
        Execution execution = enhancedExecution.getExecution();
        List<Operation> operationList = execution.getOperationList();
        int layer = 1;
        rootNode.setCycle(totalCycle);
        rootNode.setOperation(null);
        rootNode.setNodes(new ArrayList<>());
        rootNode.setLayer(0);
        Stack<LoopNode> nodeStack = new Stack<>();
        nodeStack.push(rootNode);
        for (int i = 0; i < operationList.size(); i++) {
            Operation operation = operationList.get(i);
            if (operation.isAnnotated()) {
                continue;
            }
            if (operation.isSubOperationCollection()) {
                // 子操作
                PairedCustomizeFunction pairedCustomizeFunction = enhancedExecution.getMethodCollector().
                        getPairedCustomizeFunctionByPairedCode(operation.getOperationMethod().getPairedCode());
                if (pairedCustomizeFunction != null) {
                    PairedIndex pairedIndex = pairedCustomizeFunction.getPairedIndex();
                    i = pairedIndex.getEndIndex();
                    Execution subExecution = new Execution();
                    subExecution.setOperationContext(execution.getOperationContext());
                    subExecution.setOperationList(pairedCustomizeFunction.getOperationList());
                    EnhancedExecution subEnhancedExecution = new EnhancedExecution(subExecution, enhancedExecution.getMethodCollector());
                    pairedCustomizeFunction.setNode(transferToNode(subEnhancedExecution, new FunctionNode(), 1));
                    continue;
                }
            }
            OperationMethod operationMethod = operation.getOperationMethod();
            LoopNode node;
            if (rootNode.getNodeType().equals(NodeType.FUNCTION_NODE)) {
                node = new FunctionNode(operation);
            } else {
                node = new OperationNode(operation);
            }
            node.setLayer(layer);
            if (operationMethod.equals(CommonOperationMethod.BEGIN_LOOP) ||
                    operationMethod.equals(CommonOperationMethod.BEGIN_RANDOM_LOOP)) {
                //设置是否随机循环
                node.setRandomLoop(operationMethod.equals(CommonOperationMethod.BEGIN_RANDOM_LOOP));
                //创建begin节点
                String loopCount = operation.getOperationObject().toString(); //获取内循环次数
                ++layer;
                if (loopCount.contains("~")) {
                    //随机循环
                    String[] segments = loopCount.split(RANDOM_VALUE_SEPARATOR);
                    if (segments.length != 2) {
                        throw new IllegalArgumentException(String.format("随机循环出现错误 %s", loopCount));
                    }
                    int lowerLoop = Integer.parseInt(segments[0]);
                    int upperLoop = Integer.parseInt(segments[1]);
                    int loop = new Random().nextInt((upperLoop - lowerLoop) + 1) + lowerLoop;
                    node.setCycle(loop);
                    log.info("随机内循环次数\"{}\"", loop);
                } else {
                    //特定循环
                    node.setCycle(Integer.parseInt(String.valueOf(operation.getOperationObject())));
                }
                node.setNodes(new ArrayList<>());
                nodeStack.peek().add(node);
                nodeStack.push(node);
                continue;
            } else if (operationMethod.equals(CommonOperationMethod.END_LOOP) ||
                    operationMethod.equals(CommonOperationMethod.END_RANDOM_LOOP)) {
                //创建end节点
//                nodeStack.pop().add(node);
                --layer;
                node.setLayer(layer);
                nodeStack.pop();
                nodeStack.peek().add(node);
                continue;
            }
            nodeStack.peek().add(node);
        }
//        log.info(String.valueOf(rootNode));
        return rootNode;
    }

    /**
     * 增加行号
     */
    protected static void addLineNo(List<Operation> operationList) {
        int lineNo = 0;
        for (Operation operation : operationList) {
            operation.setLineNo(lineNo++);
            operation.setParentLineNo(operation.getLineNo());
        }
    }

    /**
     * 分析步骤语法
     *
     * @param execution Execution
     * @return EnhancedExecution
     */
    protected static EnhancedExecution analyseGrammar(Execution execution) {
        log.info("进行脚本步骤语法分析");
        MethodCollector methodCollector = new MethodCollector();

        //增强执行
        EnhancedExecution enhancedExecution = new EnhancedExecution(execution, methodCollector);
        //遍历自定义函数
        List<Integer> customizeFunctionIndexes = enhancedExecution.getIndexesOfCustomizeFunction();
        for (int headerIndex : customizeFunctionIndexes) {
            Operation headerOperation = execution.getOperationList().get(headerIndex);
            PairedIndex pairedIndex = enhancedExecution.getPairedIndex(headerOperation.getOperationMethod().getPairedCode());
            if (pairedIndex.invalid()) {
                continue;
            }
            List<Operation> innerOperationList = enhancedExecution.getInnerOperationList(pairedIndex);
            if (!innerOperationList.isEmpty()) {
                methodCollector.registerCustomizeFunction(headerOperation, innerOperationList, pairedIndex);
            }
        }
        return enhancedExecution;
    }

    protected NodeContext createNodeContext(EnhancedExecution enhancedExecution, ExecutionContext executionContext) {
        int testCycle = executionContext.getTestCycle();
        OperationNode rootNode = new OperationNode();
        LoopNode node = transferToNode(enhancedExecution, rootNode, testCycle);
        NodeContext nodeContext = new NodeContext();
        nodeContext.setFailCallback(false);
        nodeContext.setExecuteResults(new LinkedHashMap<>());
        nodeContext.setExecutionContext(executionContext);
        nodeContext.setOperationList(enhancedExecution.getExecution().getOperationList());
        nodeContext.setNode(node);
        return nodeContext;
    }

    /**
     * 分发操作步骤
     *
     * @param node
     * @param nodeContext
     * @param operation
     * @param enterRetryCallback
     * @return
     */
    protected boolean distributeOperation(LoopNode node, NodeContext nodeContext, Operation operation, boolean enterRetryCallback) throws LoopBreakNotification {
        try {
            distributeOperationBegin(operation);
            if (operation.isBackground()) {
                // 提交任务
                Future<Boolean> future = threadPoolManager.submit(() -> {
                    try {
                        log.info("执行后台任务:{}", operation);
                        boolean ok = executeOperation(node, nodeContext, operation, enterRetryCallback);
                        log.info("执行后台任务完成：{}", operation);
                        return ok;
                    } catch (Exception e) {
                        log.warn(e.getMessage(), e);
                        return false;
                    }
                });
                return future.isDone();
            } else {
                return executeOperation(node, nodeContext, operation, enterRetryCallback);
            }
        } finally {
            distributeOperationEnd(operation);
        }
    }

    protected boolean beginExecuteOperation(Operation operation, NodeContext nodeContext) throws LoopBreakNotification {
        boolean ok = true;
        if (operation.getOperationMethod().equals(CommonOperationMethod.LOAD_OPERATION_GROUP)) {
            String groupName = operation.getOperationObject().toString();
            if (groupName.equals(RETRY_CALLBACK)) {
                nodeContext.setFailCallback(true);
                log.info("设置失败重试步骤");
                return true;
            }
            log.info("执行操作步骤组合");
            OperationGroup operationGroup = new OperationGroup();
            operationGroup.setProjectName(nodeContext.getExecutionContext().getProjectName());
            operationGroup.setGroupName(groupName);
            OperationGroupFileManager operationGroupFileManager = OperationGroupFileManager.of(operationGroup.getProjectName(), OperationGroupFileManager.class);
            for (Operation subOperation : operationProtocolFactory.product(operationGroupFileManager.loadJsonOperationGroup(operationGroup))) {
                //FIXME：subOperation不支持内循环
                subOperation.setParentLineNo(operation.getLineNo());
                ok &= distributeOperation(nodeContext.getNode(), nodeContext, subOperation, false);
            }
        } else {
            ok = distributeOperation(nodeContext.getNode(), nodeContext, operation, false);
        }
        return true;
    }

    protected boolean checkIfBuiltinGrammar(NodeContext nodeContext, Operation operation) throws LoopBreakNotification {
//        log.info("执行信息:{}", executeResult.getTagName());
        if (operation.getOperationMethod().equals(CommonOperationMethod.BEGIN_LOOP)) {
            log.info("执行内循环{}次", operation.getOperationObject());
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.END_LOOP)) {
            log.info("结束内循环");
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.BEGIN_RANDOM_LOOP)) {
            log.info("执行随机操作{}次", operation.getOperationObject());
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.END_RANDOM_LOOP)) {
            log.info("结束随机操作");
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.IF_EXPRESSION_SUCCESS)) {
            log.info("进入if条件逻辑");
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.ELSE_IF)) {
            log.info("进入else条件逻辑");
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.END_IF)) {
            log.info("结束if/else条件逻辑");
            return true;
        } else if (operation.getOperationMethod().equals(CommonOperationMethod.BREAK_LOOP)) {
            log.info("手动退出循环");
            throw new LoopBreakNotification();
        }
        return false;
    }

    protected abstract boolean executeOperation(LoopNode node, NodeContext nodeContext, Operation operation, boolean enterRetryCallback) throws LoopBreakNotification;

    protected abstract void distributeOperationBegin(Operation operation);

    protected abstract void distributeOperationEnd(Operation operation);

}
