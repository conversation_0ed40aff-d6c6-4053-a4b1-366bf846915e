package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.utils.FileUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.File;
import java.io.IOException;

/**
 * MySQL安装
 */
@Slf4j
public class MySQLInstaller implements Installer {
    private static final String mySQLPath = "D:/MySQL/";

    public MySQLInstaller() {
    }

    public void install(boolean isDev) throws InstallerException {
        try {
            installMySQL();
        } catch (IOException e) {
            throw new InstallerException(e);
        }
    }

    private void installMySQL() throws IOException {
        File descDir = new File(mySQLPath);
        //TODO：改成查询mysql服务
        File binPath = new File(descDir, "bin/mysql.exe");
        if (!binPath.exists()) {
            unzip(descDir, "installer/mysql/mysql-8.0.19-winx64.zip", "mysql-8.0.19-winx64.zip", true);
            configMySQL();
        } else {
            log.info("已经配置MySQL");
        }
    }

    private void configMySQL() throws IOException {
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources("installer/mysql/components/*");
        File binPath = new File(mySQLPath, "bin");
        File compPath = new File(binPath, "components");
        if (!compPath.exists()) {
            compPath.mkdir();
        }
        for (Resource resource : resources) {
            FileUtils.copyResourceToDirectoryWhenLastModified(resource, compPath);
        }
        File batFile = new File(compPath, "configMySQL.bat");
        CommandUtils.executeCommandToString(batFile.getName(), null, compPath);
        log.debug("配置MySQL ini文件");
        File mysqlAdmin = new File(binPath, "mysqladmin.exe");
        String password = "PtvKeyAdmin";
//        log.debug("设置MySQL密码");
        log.debug("初始化MySQL数据库");
        CommandUtils.executeCommandToString(mysqlAdmin.getAbsolutePath() + " -u root password " + password);
        File initSQLFile = new File(compPath, "initDB.sql");
        File mysqlExe = new File(binPath, "mysql.exe");
        CommandUtils.executeCommandToString(mysqlExe.getAbsolutePath() + " -uroot -p" + password + " < " + initSQLFile.getAbsolutePath());
//        EnvUtils.updatePath(binPath.getAbsolutePath());
        log.info("配置MySQL环境变量:{}", binPath);
    }

    public static void main(String[] args) {
        String path = "configMySQL.bat";
        String dir = "D:\\00-Code\\05-Web_Backend\\AITestXServer\\aitestx-web\\src\\main\\resources\\installer\\mysql\\";
        try {
            Runtime.getRuntime().exec(String.format("cmd /c start %s", path), new String[]{dir});
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
