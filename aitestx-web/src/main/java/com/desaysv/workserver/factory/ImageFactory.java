package com.desaysv.workserver.factory;

import com.desaysv.workserver.entity.Image;
import com.desaysv.workserver.vo.ImageRegisterDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 13:39
 * @description : 图片工厂
 * @modified By :
 * @since : 2022-3-30
 */
@Component
@Lazy
public class ImageFactory implements AbstractImageFactory {

    @Autowired
    private ProjectRegisterFactory projectRegisterFactory;

    @Override
    public Image createImage(ImageRegisterDTO imageRegisterDTO) {
        String imageName = imageRegisterDTO.getTemplatePicture().getName();
//        String projectName = projectRegisterFactory.getCurrentProject();
        return null;
    }

}
