package com.desaysv.workserver.config;

import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 配置管理控制器
 * 用于处理前端传递的JSON配置文件的保存和读取
 */
@RestController
@RequestMapping("/config")
@Slf4j
public class ConfigManagementController {

    private final ConfigManagementService configManagementService;

    @Autowired
    public ConfigManagementController(ConfigManagementService configManagementService) {
        this.configManagementService = configManagementService;
    }

    /**
     * 保存JSON配置
     *
     * @param project    项目名称
     * @param filename   文件名
     * @param jsonConfig JSON配置内容
     * @return 保存结果
     */
    @PostMapping("/save")
    public ResultEntity<?> saveConfig(
            @RequestParam String project,
            @RequestParam String filename,
            @RequestBody Map<String, Object> jsonConfig) {
        try {
            log.info("保存配置: 项目={}, 文件名={}, 配置={}", project, filename, jsonConfig);
            configManagementService.saveConfig(project, filename, jsonConfig);
            return ResultEntity.ok("配置保存成功");
        } catch (Exception e) {
            log.error("保存配置失败: {}", e.getMessage());
            return ResultEntity.fail(e.getMessage());
        }
    }

    /**
     * 获取JSON配置
     *
     * @param project  项目名称
     * @param filename 文件名
     * @return 配置内容
     */
    @GetMapping("/get")
    public ResultEntity<String> getConfig(
            @RequestParam String project,
            @RequestParam String filename) {
        try {
            log.info("获取配置: 项目={}, 文件名={}", project, filename);
            String config = configManagementService.getConfig(project, filename);
            return ResultEntity.data(config);
        } catch (Exception e) {
            log.error("获取配置失败: {}", e.getMessage());
            return ResultEntity.fail(e.getMessage());
        }
    }
}
