package com.desaysv.workserver.config;

import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configuration
public class AccessLogConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(AccessLogConfig.class);

    @Bean
    public HandlerInterceptor accessLogInterceptor() {
        return new HandlerInterceptor() {

            @Override
            public boolean preHandle(@NotNull HttpServletRequest request,
                                     @NotNull HttpServletResponse response,
                                     @NotNull Object handler) {
                // 记录请求开始日志
                String queryString = request.getQueryString() != null ? "?" + request.getQueryString() : "";
                String startPattern = "请求开始: %s%s \"%s\" %s";
                String startLogMsg = String.format(startPattern,
                        request.getRemoteAddr(),
                        queryString,
                        request.getMethod() + " " + request.getRequestURI() + " " + request.getProtocol(),
                        response.getStatus());
                logger.info(startLogMsg);

                request.setAttribute("startTime", System.currentTimeMillis());
                return true;
            }

            @Override
            public void afterCompletion(@NotNull HttpServletRequest request,
                                        @NotNull HttpServletResponse response,
                                        @NotNull Object handler, Exception ex) {
                Long startTime = (Long) request.getAttribute("startTime");
                if (startTime == null) {
                    return;
                }
                long duration = System.currentTimeMillis() - startTime;

                String queryString = request.getQueryString() != null ? "?" + request.getQueryString() : "";
                String pattern = "请求结束: %s%s \"%s\" %s (%d ms)";
                String logMsg = String.format(pattern,
                        request.getRemoteAddr(),
                        queryString,
                        request.getMethod() + " " + request.getRequestURI() + " " + request.getProtocol(),
                        response.getStatus(),
                        duration);

                logger.info(logMsg);
            }
        };
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加AccessLog拦截器
        registry.addInterceptor(accessLogInterceptor())
                .addPathPatterns("/**");
    }
}