package com.desaysv.workserver.config;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 17:14
 * @description :
 * @modified By :
 * @since : 2022-4-12
 */
@Configuration
public class ApplicationConfig {

    @Bean
    public static AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter() {
        return new AutoJsonRpcServiceImplExporter();
    }

    /*解决文件名中含有":\\"等特殊字符时，接口400的问题
     * Tomcat的新版本中增加了一个新特性，就是严格按照 RFC 3986规范进行访问解析，而 RFC 3986规范定义了Url中只允许包含英文字母（a-zA-Z）、数字（0-9）、-_.~4个特殊字符
     * 以及所有保留字符(RFC3986中指定了以下字符为保留字符：! * ’ ( ) ; : @ & = + $ , / ? # [ ])。*/
    @Bean
    public TomcatServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(connector -> {
            String charSet = "\"<>[\\]^`{|}";
            connector.setProperty("relaxedPathChars", charSet);
            connector.setProperty("relaxedQueryChars", charSet);
        });
        return factory;
    }

//    @Bean
//    public UndertowServletWebServerFactory undertowServletWebServerFactory() {
//        UndertowServletWebServerFactory factory = new UndertowServletWebServerFactory();
//        factory.addBuilderCustomizers(builder -> {
//            builder.setServerOption(UndertowOptions.DECODE_SLASH, true)
//                    .setServerOption(UndertowOptions.DECODE_URL, false)
//                    .setServerOption(UndertowOptions.URL_CHARSET, "UTF-8");
//        });
//
//        factory.addDeploymentInfoCustomizers(deploymentInfo -> {
//            deploymentInfo.setUrlEncoding("UTF-8");
//            // 设置允许特殊字符
//            deploymentInfo.setDefaultEncoding("UTF-8");
//            deploymentInfo.setAllowNonStandardWrappers(true);
//        });
//        return factory;
//    }

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(60000);
        httpRequestFactory.setConnectTimeout(60000);
        httpRequestFactory.setReadTimeout(60000);
        return new RestTemplate(httpRequestFactory);
    }

}
