package com.desaysv.workserver.vo;

import com.desaysv.workserver.model.TemplatePicture;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 13:22
 * @description : 图片注册表单
 * @modified By :
 * @since : 2022-3-30
 */
@Data
public class ImageRegisterDTO {

    private TemplatePicture templatePicture;

    @JsonProperty("templatePicture")
    private void unpackTemplateImageParameter(Map<String, Object> params) {
        System.out.println("unpackTemplateImageParameter:" + params);
    }

}
