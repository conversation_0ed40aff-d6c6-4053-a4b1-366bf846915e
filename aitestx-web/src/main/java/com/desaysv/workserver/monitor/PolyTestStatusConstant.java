package com.desaysv.workserver.monitor;

import lombok.Getter;

public class PolyTestStatusConstant {
    @Getter
    public enum Status {
        OPEN(0), //0:打开
        READY(1), //1：登录
        TESTING(2), //2：开始测试
        PAUSING(3), //3:暂停
        RESUME_TESTING(4), //4：继续（恢复）测试
        COMPLETED_PASS(5), //5：测试通过
        COMPLETED_FAIL(6), //6：测试失败
        COMPLETED(7), //7：测试完成
        FAILED(8), //8：异常停止
        MANUALLY_TERMINATED(9), //9：手动停止
        LOGOUT(10), //10、退出登录
        EXITED(11); //11：关闭应用

        private final int value;

        Status(int value) {
            this.value = value;
        }
    }


    public static String getStatusNameByCode(int statusCode) {
        for (PolyTestStatusConstant.Status e : PolyTestStatusConstant.Status.values()) {
            if (e.getValue() == statusCode) return e.name();
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(Status.OPEN.getValue());
    }
}
