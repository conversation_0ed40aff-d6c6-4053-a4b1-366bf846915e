<?xml version="1.0" encoding="UTF-8"?>
<DAQNavi Profile="*******" Version="*******">
    <DaqDevice ModuleIndex="0">
        <Property ID="8">
            <Description>Product ID:</Description>
            <Attribute>0</Attribute>
            <Type>2</Type>
            <EnumType>ProductId</EnumType>
            <Tip>Product ID is used to represent a DAQ Device model and distinguish devices of different models. Each device model owns an unique Product ID.</Tip>
            <Value>235</Value>
        </Property>
    </DaqDevice>
    <DaqAi ModuleIndex="0">
        <Property ID="51">
            <Description>Connection Type:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>AiSignalType</EnumType>
            <Tip>Gets or sets the connection type of each channel.</Tip>
            <Value>1,1,1,1,1,1,1,1</Value>
        </Property>
        <Property ID="52">
            <Description>Value Range Type:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>ValueRange</EnumType>
            <Tip>Gets or sets the ValueRange for the selected channel.</Tip>
            <Value>38,38,1,1,1,1,1,1</Value>
        </Property>
        <Property ID="63">
            <Description>Scan Channel Start:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the physical channel number of the start channel to be scanned by buffered AI or buffered AO.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="64">
            <Description>Scan Channel Count:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the workable channel count to be scanned by buffered AI or buffered AO. The maximum available workable channel count depends on the connection type of channels.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="61">
            <Description>Convert Clock Source:</Description>
            <Attribute>1</Attribute>
            <Type>2</Type>
            <EnumType>SignalDrop</EnumType>
            <Tip>Gets or sets the conversion clock source used by Buffered AI or Buffered AO. The value should be a type of SignalDrop.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="62">
            <Description>Convert Clock Rate:</Description>
            <Attribute>3</Attribute>
            <Type>5</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the convert clock rate of each channel used for Buffered AI or Buffered AO.</Tip>
            <Value>44444</Value>
        </Property>
        <Property ID="50">
            <Description>Channel Count:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>The count of logical channels which can be used to acquire AI samples. This value depends on the channel connection type of the device.</Tip>
            <Value>8</Value>
        </Property>
        <Property ID="60">
            <Description>Interval Count:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the interval count for buffered AI or buffered AO.In Buffered AI,The data buffer is divided into a series of section for sending DataReady events, Intervalcount set the count how many samples per channel acquired, the DataReady event will happened. When each channel samples a size of IntervalCount data, driver would sending DataReady event to notice the user application that the data is ready; In Buffered AO, the data buffer is also divided into a series of section for sending DataTransmitted events, Intervalcount set the count how many samples per channel output, the DataTransmitted event will happened. When each channel output a size of IntervalCount data, driver sending DataTransmitted event to notice the user application that the data has outputted.</Tip>
            <Value>2048</Value>
        </Property>
        <Property ID="390">
            <Description>Record Section Length:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the record section length.</Tip>
            <Value>2048</Value>
        </Property>
        <Property ID="391">
            <Description>Record Section Count:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the record section count.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="349">
            <Description>Record Cycle:</Description>
            <Attribute>3</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the record cycles.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="23">
            <Description>Resolution:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Data resolution refers to the number of the bit that is used to realize analog to digital(or digital to analog) conversion,the bigger bit number of ADC(or DAC) is, the higher the resolution will be</Tip>
            <Value>14</Value>
        </Property>
        <Property ID="26">
            <Description>Channel Number Max:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip> Max number of physical channel.Physical channel refers to visible input/output terminals or pins that can connect wires on a data acquisition card.</Tip>
            <Value>7</Value>
        </Property>
        <Property ID="27">
            <Description>Channel Connection Type:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>AiChannelType</EnumType>
            <Tip>The channel connection type.</Tip>
            <Value>2</Value>
        </Property>
        <Property ID="392">
            <Description>Connection Type:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>AiSignalType</EnumType>
            <Tip>The device supported connection type.</Tip>
            <Value>0,1</Value>
        </Property>
        <Property ID="31">
            <Description>Value Range:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>ValueRange</EnumType>
            <Tip>The device supported Value Range type.</Tip>
            <Value>1,5,4,36,3,37,2,38</Value>
        </Property>
        <Property ID="30">
            <Description>Overall ValueRange type:</Description>
            <Attribute>0</Attribute>
            <Type>2</Type>
            <EnumType>ValueRange</EnumType>
            <Tip>Whether all the channels can only set the same Value Range type, 1 means yes and 0 means no.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="35">
            <Description>Scan Method:</Description>
            <Attribute>0</Attribute>
            <Type>2</Type>
            <EnumType>SamplingMethod</EnumType>
            <Tip>The device channel scan method,it would be Simultaneous or EqualIntervalSwitch.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="38">
            <Description>Convert Clock Sources:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalDrop</EnumType>
            <Tip>The device supported convert clock source list.</Tip>
            <Value>1,17</Value>
        </Property>
        <Property ID="39">
            <Description>Convert Clock Rate Range:</Description>
            <Attribute>2</Attribute>
            <Type>7</Type>
            <EnumType></EnumType>
            <Tip>The supported convert clock rate range.</Tip>
            <Value>9,32,47619</Value>
        </Property>
        <Property ID="36">
            <Description>Scan Channel Start Base:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>The base number of the start scan channel, for example, if this property value is 2, the start scan channel can be only set to 2,4,6,8 and so on.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="37">
            <Description>Scan Channel Count Base:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>The base number of the scan channel count, for example, if scan channel count is 2, the scan channel count can only be set to 2,4,6,8 and so on.</Tip>
            <Value>1</Value>
        </Property>
    </DaqAi>
    <DaqAo ModuleIndex="0">
        <Property ID="52">
            <Description>Value Range Type:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>ValueRange</EnumType>
            <Tip>Gets or sets the ValueRange for the selected channel.</Tip>
            <Value>8,8</Value>
        </Property>
        <Property ID="23">
            <Description>Resolution:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Data resolution refers to the number of the bit that is used to realize analog to digital(or digital to analog) conversion,the bigger bit number of ADC(or DAC) is, the higher the resolution will be</Tip>
            <Value>12</Value>
        </Property>
        <Property ID="26">
            <Description>Channel Number Max:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip> Max number of physical channel.Physical channel refers to visible input/output terminals or pins that can connect wires on a data acquisition card.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="31">
            <Description>Value Range:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>ValueRange</EnumType>
            <Tip>The device supported Value Range type.</Tip>
            <Value>8</Value>
        </Property>
    </DaqAo>
    <DaqDio ModuleIndex="0">
        <Property ID="120">
            <Description>DI Data Mask:</Description>
            <Attribute>0</Attribute>
            <Type>65536</Type>
            <EnumType></EnumType>
            <Tip>DiDataMask is used to describe whether each channel of a digital port is used DI function or not. The value of DataMask has 8 bits and each bit corresponds to one channel of the digital port. Value 1 means that the corresponding channel can be used for DI function. DiDataMask of this port will be changed when the port direction changing.</Tip>
            <Value>255</Value>
        </Property>
        <Property ID="121">
            <Description>DO Data Mask:</Description>
            <Attribute>0</Attribute>
            <Type>65536</Type>
            <EnumType></EnumType>
            <Tip>Do DataMask is used to describe whether each channel of  one digital port could be used for digital output.  Every DataMask has 8 bits and each bit corresponds to one channel of digital port. When the value of one bit is 1, it means the corresponding channel could be used for digital output.DoDataMask of this port will be changed when the port direction changing.</Tip>
            <Value>255</Value>
        </Property>
        <Property ID="354">
            <Description>DI Inverse Ports:</Description>
            <Attribute>1</Attribute>
            <Type>65536</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets whether enable the DI value inverse.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="26">
            <Description>Channel Number Max:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip> Max number of physical channel.Physical channel refers to visible input/output terminals or pins that can connect wires on a data acquisition card.</Tip>
            <Value>7</Value>
        </Property>
        <Property ID="82">
            <Description>Ports Type:</Description>
            <Attribute>0</Attribute>
            <Type>65536</Type>
            <EnumType>DioPortType</EnumType>
            <Tip>The type of all digital port.There are six kinds of port type,the name and meaning, listed in the DioPortType Enumeration.</Tip>
            <Value>2</Value>
        </Property>
    </DaqDio>
    <DaqCounter ModuleIndex="0">
        <Property ID="279">
            <Description>Clock Source:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalDrop</EnumType>
            <Tip>Gets or sets the clock source for primary counter.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="290">
            <Description>Clock Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the clock polarity from the counter supported polarities.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="292">
            <Description>Gate Enabled/Disabled:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets whether enabled the gate control or not.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="291">
            <Description>Gate Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the clock polarity from the gate supported polarities.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="231">
            <Description>Frequency Measurement Method:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>FreqMeasureMethod</EnumType>
            <Tip>Gets or sets the frequency measurement method.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="232">
            <Description>Collection Period Configuration:</Description>
            <Attribute>3</Attribute>
            <Type>65541</Type>
            <EnumType></EnumType>
            <Tip>Gets or Sets the CollectionPeriod for the method of CountingPulseBySysTime,this is the gate width for pulse counting in Frequency Measurement function.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="281">
            <Description>Clock Source:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalDrop</EnumType>
            <Tip>Gets or sets the clock source.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="296">
            <Description>Clock Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the clock polarity from the supported.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="297">
            <Description>Gate Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the gate polarity from the supported.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="298">
            <Description>Out Signal Type:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>OutSignalType</EnumType>
            <Tip>Gets or sets the output signal type from the supported.</Tip>
            <Value>3</Value>
        </Property>
        <Property ID="303">
            <Description>Gate Enabled/Disabled:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets whether enable the gate control.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="304">
            <Description>Gate Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the gate polarity from the supported. </Tip>
            <Value>1</Value>
        </Property>
        <Property ID="305">
            <Description>Out Signal Type:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>OutSignalType</EnumType>
            <Tip>Gets or sets the output signal type from the supported.</Tip>
            <Value>3</Value>
        </Property>
        <Property ID="311">
            <Description>Gate Enabled/Disabled:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets whether enable the gate control.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="312">
            <Description>Gate Polarity:</Description>
            <Attribute>1</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>Gets or sets the gate polarity from the supported.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="313">
            <Description>Out Signal Count:</Description>
            <Attribute>3</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the output signal count from the supported.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="324">
            <Description>Initial Value:</Description>
            <Attribute>3</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the count initial value for counters.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="325">
            <Description>Index Reset Times:</Description>
            <Attribute>3</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets or sets the reset times with index pin for Up-Down counter.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="26">
            <Description>Channel Number Max:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip> Max number of physical channel.Physical channel refers to visible input/output terminals or pins that can connect wires on a data acquisition card.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="23">
            <Description>Resolution:</Description>
            <Attribute>2</Attribute>
            <Type>2</Type>
            <EnumType></EnumType>
            <Tip>Data resolution refers to the number of the bit that is used to realize analog to digital(or digital to analog) conversion,the bigger bit number of ADC(or DAC) is, the higher the resolution will be</Tip>
            <Value>32</Value>
        </Property>
        <Property ID="174">
            <Description>Counter0 Capabilities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>CounterCapability</EnumType>
            <Tip>The supported functions of channel 0.</Tip>
            <Value>2,5</Value>
        </Property>
        <Property ID="287">
            <Description>Clock Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported CLK counting edge types, it would be Positive or Negative,Positive means rising edge and Negative means falling edge. </Tip>
            <Value>1</Value>
        </Property>
        <Property ID="289">
            <Description>Gate Control by Software Setting:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets whether the supported gate control can be enabled by software setting,1 means yes and 0 means no.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="288">
            <Description>Gate Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported gate level types, it would be Positive or Negative,Positive means high level and Negative means low level.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="293">
            <Description>Clock Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported CLK counting edge types, it would be Positive or Negative,Positive means rising edge and Negative means falling edge. </Tip>
            <Value>1</Value>
        </Property>
        <Property ID="294">
            <Description>Gate Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported gate level types, it would be Positive or Negative,Positive means high level and Negative means low level.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="295">
            <Description>Output Signal Types:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>OutSignalType</EnumType>
            <Tip>The supported output signal type.</Tip>
            <Value>3</Value>
        </Property>
        <Property ID="320">
            <Description>Delay Count Range:</Description>
            <Attribute>2</Attribute>
            <Type>7</Type>
            <EnumType></EnumType>
            <Tip>The supported delay count range which DelayCount should be set in this range.</Tip>
            <Value>5,1,0</Value>
        </Property>
        <Property ID="299">
            <Description>Gate Control:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets whether the gate pin control can be set by software setting.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="300">
            <Description>Gate Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported gate level types, it would be Positive or Negative,Positive means high level and Negative means low level.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="301">
            <Description>Output Signal Types:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>OutSignalType</EnumType>
            <Tip>The supported output signal type.</Tip>
            <Value>3</Value>
        </Property>
        <Property ID="213">
            <Description>Frequency Measurement Method:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>FreqMeasureMethod</EnumType>
            <Tip>The supported frequency measurement method.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="306">
            <Description>Gate Control:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>Gets whether the gate pin control can be set by software setting.</Tip>
            <Value>0</Value>
        </Property>
        <Property ID="307">
            <Description>Gate Polarities:</Description>
            <Attribute>0</Attribute>
            <Type>65538</Type>
            <EnumType>SignalPolarity</EnumType>
            <Tip>The supported gate level types, it would be Positive or Negative,Positive means high level and Negative means low level.</Tip>
            <Value>1</Value>
        </Property>
        <Property ID="322">
            <Description>Up-Down Counter Initial Values:</Description>
            <Attribute>2</Attribute>
            <Type>65538</Type>
            <EnumType></EnumType>
            <Tip>The supported initial value which can be used for Up-Down counter value reset.</Tip>
            <Value>0</Value>
        </Property>
    </DaqCounter>
</DAQNavi>
