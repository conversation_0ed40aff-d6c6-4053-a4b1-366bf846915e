CREATE DATABASE IF NOT EXISTS aitestx_of_production;
USE aitestx_of_production;

CREATE TABLE IF NOT EXISTS tester (
    id SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Tester ID',
    name <PERSON><PERSON><PERSON><PERSON>(64) UNIQUE NOT NULL COMMENT 'Tester name',
    password VARCHAR(256) DEFAULT NULL COMMENT 'Tester password',
    status CHAR(1) NOT NULL COMMENT 'Tester status',
    email VARCHAR(64) DEFAULT NULL COMMENT 'Email address of tester',
    phone VARCHAR(50)  DEFAULT NULL COMMENT 'Phone number of tester',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS test_project (
    id SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Test project ID',
    name VARCHAR(64) NOT NULL COMMENT 'Test Project name',
    model VARCHAR(64) DEFAULT NULL COMMENT 'Test Project model name',
    customer_id TINYINT UNSIGNED DEFAULT NULL COMMENT 'Customer ID',
    department_id TINYINT UNSIGNED DEFAULT NULL COMMENT 'Department ID',
    project_info_id SMALLINT UNSIGNED DEFAULT NULL COMMENT 'Project info ID',
    communal TINYINT(1) DEFAULT 0  COMMENT 'Communal Project',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE (name, model)
);

CREATE TABLE IF NOT EXISTS department (
    id TINYINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Department ID',
    code TINYINT UNIQUE NOT NULL COMMENT 'Department code',
    name VARCHAR(64) NOT NULL COMMENT 'Department name',
    PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS tester_test_project (
    tester_id SMALLINT UNSIGNED NOT NULL COMMENT 'Tester ID',
    project_id SMALLINT UNSIGNED NOT NULL COMMENT 'Test project ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    UNIQUE KEY (tester_id, project_id)
);


CREATE TABLE IF NOT EXISTS test_machine (
    id SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Test machine ID',
    name VARCHAR(64) UNIQUE NOT NULL COMMENT 'Test machine name',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS template_picture (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Template picture ID',
    name VARCHAR(64) NOT NULL COMMENT 'Template picture name',
    roi_id BIGINT NOT NULL COMMENT 'Roi ID',
    device_id SMALLINT UNSIGNED NOT NULL COMMENT 'Device ID',
    original_picture_id BIGINT NULL COMMENT 'Original picture ID',
    project_id SMALLINT UNSIGNED COMMENT 'Test project ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE KEY (name, project_id)
);

CREATE TABLE IF NOT EXISTS original_picture (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Original picture ID',
    name VARCHAR(64) UNIQUE NOT NULL COMMENT 'Original picture name',
    width SMALLINT DEFAULT NULL COMMENT 'Original picture width',
    height SMALLINT DEFAULT NULL COMMENT 'Original picture height',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS test_device (
    id SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Device ID',
    name VARCHAR(64) NOT NULL COMMENT 'Device name',
    port TINYINT COMMENT 'Device port',
    baud_rate INT DEFAULT NULL COMMENT 'Device baud rate',
    type_id TINYINT UNSIGNED NULL COMMENT 'Device type ID',
    model_id TINYINT UNSIGNED NULL COMMENT 'Device model ID',
    alias_name VARCHAR(64) DEFAULT NULL COMMENT 'Device alias name',
    unique_code VARCHAR(256) UNIQUE NOT NULL COMMENT 'Device unique code',
    parameter TEXT NOT NULL COMMENT 'Device parameter',
    simulated CHAR(1) DEFAULT 0 COMMENT 'Device simulate status',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);

ALTER TABLE test_device MODIFY COLUMN parameter TEXT;


CREATE TABLE IF NOT EXISTS test_device_type (
    id TINYINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Device type ID',
    name VARCHAR(64) UNIQUE NOT NULL COMMENT 'Device type name',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS test_device_model (
    id TINYINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Device model ID',
    name VARCHAR(64) UNIQUE NOT NULL COMMENT 'Device model name',
    PRIMARY KEY (id)
);

-- CREATE TABLE IF NOT EXISTS template_roi (
--    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Roi ID',
--    type_id TINYINT NOT NULL COMMENT 'Roi type',
--    start_x SMALLINT NULL COMMENT 'Roi start x',
--    start_y SMALLINT NULL COMMENT 'Roi start y',
--    end_x SMALLINT NULL COMMENT 'Roi end x',
--    end_y SMALLINT NULL COMMENT 'Roi end y',
--    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
--    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
--    PRIMARY KEY (id)
-- );

-- 百分比ROI
CREATE TABLE IF NOT EXISTS percent_template_roi (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Roi ID',
    type_id TINYINT NOT NULL COMMENT 'Roi type',
    start_x DOUBLE DEFAULT NULL COMMENT 'Roi start x',
    start_y DOUBLE DEFAULT NULL COMMENT 'Roi start y',
    end_x DOUBLE DEFAULT NULL COMMENT 'Roi end x',
    end_y DOUBLE DEFAULT NULL COMMENT 'Roi end y',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS roi_type (
    id TINYINT NOT NULL AUTO_INCREMENT COMMENT 'Roi type ID',
    name VARCHAR(24) UNIQUE NOT NULL COMMENT 'Roi type',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS robot_coordinates (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Coordinate ID',
    name VARCHAR(64) NOT NULL COMMENT 'Coordinate name',
    x FLOAT NOT NULL COMMENT 'x',
    y FLOAT NOT NULL COMMENT 'y',
    z FLOAT NOT NULL COMMENT 'z',
    r FLOAT NOT NULL COMMENT 'r',
    slide_rail FLOAT DEFAULT NULL comment 'slideRail',
    project_id SMALLINT UNSIGNED COMMENT 'Test project ID',
    device_model_id TINYINT UNSIGNED NOT NULL COMMENT 'Device model ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE KEY (name, project_id, device_model_id)
);

CREATE TABLE IF NOT EXISTS test_client (
    id TINYINT NOT NULL AUTO_INCREMENT COMMENT 'Test client ID',
    name VARCHAR(64) NOT NULL UNIQUE COMMENT 'Test client name',
    PRIMARY KEY (id)
);

-- case_detail_id
CREATE TABLE IF NOT EXISTS testcase_file (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Testcase File ID',
    module_name VARCHAR(64) DEFAULT '' COMMENT 'Testcase moduleName',
    case_name VARCHAR(64) NOT NULL COMMENT 'Testcase caseName',
    comment VARCHAR(64) DEFAULT '' COMMENT 'Testcase comment',
    project_id SMALLINT UNSIGNED COMMENT 'Test project ID',
    client_id TINYINT UNSIGNED COMMENT 'Test client ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE KEY (module_name, case_name, project_id, client_id)
);

-- 机械臂功能坐标
CREATE TABLE IF NOT EXISTS functional_robot_coordinates (
    id TINYINT NOT NULL AUTO_INCREMENT COMMENT 'Specified coordinate ID',
    name VARCHAR(64) NOT NULL COMMENT 'Specified coordinate name',
    alias VARCHAR(64) DEFAULT '' COMMENT 'Specified coordinate alias name',
    func TINYINT NOT NULL COMMENT 'Specified coordinate usage',
    enable CHAR(1) DEFAULT 0 COMMENT 'Specified coordinate enabled',
    x FLOAT NOT NULL COMMENT 'x',
    y FLOAT NOT NULL COMMENT 'y',
    z FLOAT NOT NULL COMMENT 'z',
    r FLOAT NOT NULL COMMENT 'r',
    slide_rail FLOAT DEFAULT NULL comment 'slideRail',
    project_id SMALLINT UNSIGNED COMMENT 'Test project ID',
    device_model_id TINYINT UNSIGNED NOT NULL COMMENT 'Device model ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE KEY (name, project_id, device_model_id)
);

CREATE TABLE IF NOT EXISTS coordinates_roi (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'Roi ID',
    device_type_id TINYINT UNSIGNED NOT NULL COMMENT 'Device type ID',
    coordinates_id BIGINT NOT NULL COMMENT 'Coordinates ID',
    type_id TINYINT DEFAULT 1 COMMENT 'Roi type',
    start_x DOUBLE DEFAULT NULL COMMENT 'Roi start x',
    start_y DOUBLE DEFAULT NULL COMMENT 'Roi start y',
    end_x DOUBLE DEFAULT NULL COMMENT 'Roi end x',
    end_y DOUBLE DEFAULT NULL COMMENT 'Roi end y',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id),
    UNIQUE KEY (device_type_id, coordinates_id)
);

CREATE TABLE IF NOT EXISTS test_case (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT 'Testcase ID',
    module_name VARCHAR(64) DEFAULT NULL COMMENT 'Test project',
    testcase_name VARCHAR(64) NOT NULL COMMENT 'Testcase name',
    precondition TEXT DEFAULT NULL COMMENT "Pre condition",
    operational_step TEXT DEFAULT NULL COMMENT "Operational step",
    expectation_result TEXT DEFAULT NULL COMMENT "Expectation result",
    testsuite_uuid VARCHAR(48) NOT NULL COMMENT 'Testsuite uuid',
    test_result_id int(11) UNIQUE NOT NULL  COMMENT 'TestResult ID',
    is_pass TINYINT NOT NULL COMMENT 'Pass or fail',
    testing TINYINT NOT NULL COMMENT 'is testing',
    begin_test_time datetime(0) NOT NULL COMMENT 'Begin test time',
    end_test_time datetime(0) NOT NULL COMMENT 'End test time',
    create_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
    update_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS test_result (
   id int(11) NOT NULL AUTO_INCREMENT COMMENT 'TestResult ID',
   sum_cycle int(11) NOT NULL COMMENT 'Sum cycle',
   test_cycle int(11) NOT NULL COMMENT 'Test cycle',
   fail_cycle int(11) NOT NULL COMMENT 'Fail cycle',
   summary TEXT DEFAULT NULL COMMENT 'summary of testResult',
   create_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
   update_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
   PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS test_suite (
   id int(11) NOT NULL AUTO_INCREMENT COMMENT 'Testsuite ID',
   uuid VARCHAR(48) UNIQUE NOT NULL COMMENT 'Testsuite uuid',
   test_project_name VARCHAR(64) NOT NULL COMMENT 'Test project name',
   testsuite_name VARCHAR(128) NOT NULL COMMENT 'Testsuite name',
   hardware_version VARCHAR(150) DEFAULT "" COMMENT "Hardware version",
   software_version VARCHAR(150) DEFAULT "" COMMENT "Software version",
   begin_test_time datetime(0) NOT NULL COMMENT 'Begin test time',
   end_test_time datetime(0) NOT NULL COMMENT 'End test time',
   create_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create timestamp',
   update_time datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Update timestamp',
   PRIMARY KEY (id),
   UNIQUE KEY (test_project_name, testsuite_name)
);





