INSERT INTO roi_type (name) VALUES ("rect");
INSERT INTO roi_type (name) VALUES ("circle");

INSERT INTO test_device_type (name) VALUES ("powerType");
INSERT INTO test_device_type (name) VALUES ("canType");
INSERT INTO test_device_type (name) VALUES ("plugType");
INSERT INTO test_device_type (name) VALUES ("serialType");
INSERT INTO test_device_type (name) VALUES ("robotType");
INSERT INTO test_device_type (name) VALUES ("cameraType");
INSERT INTO test_device_type (name) VALUES ("resistorType");
INSERT INTO test_device_type (name) VALUES ("soundCardType");
INSERT INTO test_device_type (name) VALUES ("daqType");
INSERT INTO test_device_type (name) VALUES ("linType");
INSERT INTO test_device_type (name) VALUES ("relayType");
INSERT INTO test_device_type (name) VALUES ("androidType");
INSERT INTO test_device_type (name) VALUES ("iosType");
INSERT INTO test_device_type (name) VALUES ("instrumentType");

INSERT INTO test_project (name,communal) VALUES ("__PUBLIC_PROJECT__",1);

INSERT INTO test_device_model (name) VALUES ("IT6322");
INSERT INTO test_device_model (name) VALUES ("IT6831");
INSERT INTO test_device_model (name) VALUES ("IT6832");
INSERT INTO test_device_model (name) VALUES ("IT6862");
INSERT INTO test_device_model (name) VALUES ("IT6932");
INSERT INTO test_device_model (name) VALUES ("BaslerCamera");
INSERT INTO test_device_model (name) VALUES ("USB2Camera");
INSERT INTO test_device_model (name) VALUES ("SerialPort")