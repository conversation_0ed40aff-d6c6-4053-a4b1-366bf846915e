package com.desaysv.workserver.action_sequence;

import com.desaysv.workserver.utils.StrUtils;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ActualExpectedResult {
    private static final String DEFAULT_KEY = "default";

    @Getter(AccessLevel.NONE)
    private final Map<String, ActualResult> resultMap;

    public ActualExpectedResult() {
        resultMap = new LinkedHashMap<>();
    }

    public void put(String key, boolean isPass) {
        put(key, isPass, null);
    }

    public void put(String key, Object value) {
        put(key, null, value);
    }

    public boolean containsKey(String key) {
        return resultMap.containsKey(key);
    }

    public ActualResult get(String key) {
        return resultMap.get(key);
    }

    public void putDefault(Boolean pass, Object value) {
        put(DEFAULT_KEY, pass, value);
    }

    public ActualResult getDefault() {
        return resultMap.get(DEFAULT_KEY);
    }

    public void put(String key, Boolean isPass, Object value) {
        ActualResult actualResult = resultMap.computeIfAbsent(key, s -> new ActualResult());
        if (isPass != null) {
            actualResult.setPass(isPass);
        }
        if (value != null) {
            actualResult.setValue(value);
        }
    }

    public void putResult(String operation, boolean passVal, double valueVal) {
    }

    public void markComplete(boolean success) {
    }

    public boolean isPass() {
        return !resultMap.isEmpty() && resultMap.values().stream().allMatch(ActualResult::isPass);
    }

    public static ActualExpectedResult defaultResult(Object value) {
        return defaultResult(true, value);
    }

    public static ActualExpectedResult defaultResult(boolean isPass, Object value) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        actualExpectedResult.resultMap.put(DEFAULT_KEY, new ActualResult(isPass, value));
        return actualExpectedResult;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (ActualResult result : resultMap.values()) {
            if (ObjectUtils.isEmpty(result.getValue())) {
                sb.append("NA");
            } else {
                sb.append(result.getValue());
            }
            sb.append("-");
        }
        String expr = sb.toString();
        if (expr.endsWith("-")) {
            expr = StrUtils.trimEnd(expr, "-");
        }
        return expr;
    }

    public static void main(String[] args) {
        System.out.println(new ActualExpectedResult().isPass());
    }

}
