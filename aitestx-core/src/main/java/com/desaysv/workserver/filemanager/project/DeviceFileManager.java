package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 管理项目中设备配置的文件操作。
 * 继承自ProjectFileManager，提供专门的设备配置文件处理功能。
 */
@Slf4j
public class DeviceFileManager extends ProjectFileManager {

    /**
     * 构造函数，为特定项目初始化DeviceFileManager。
     *
     * @param projectName 将管理设备配置的项目名称
     */
    public DeviceFileManager(String projectName) {
        super(projectName);
    }

    /**
     * 初始化设备配置的子路径。
     * 在此实现中未执行任何操作。
     *
     * @param dynamicFolderName
     */
    @Override
    protected void initSubPaths(String dynamicFolderName) {

    }

    /**
     * 将设备配置写入JSON文件。
     *
     * @param deviceType 设备类型，用于创建对应的子文件夹
     * @param jsonConfig 要写入文件的设备配置JSON字符串
     * @param configName 配置文件的名称（不包含扩展名）
     */
    public void writeDeviceConfig(String deviceType, String jsonConfig, String configName) {
        createFolder(deviceConfigPath, deviceType);
        File configFile = new File(new File(deviceConfigPath, deviceType), String.format("%s.json", configName));
        log.info("写入设备配置文件:{}", configFile);
        ThreadSafeFileUtils.writeFileFromString(configFile, jsonConfig, false);
    }

    /**
     * 读取指定设备类型和配置名称的设备配置文件。
     *
     * @param deviceType 设备类型，对应子文件夹名称
     * @param configName 配置文件的名称（不包含扩展名）
     * @return 配置文件的内容字符串
     */
    public String readDeviceConfig(String deviceType, String configName) {
        File configFile = new File(new File(deviceConfigPath, deviceType),
                String.format("%s.json", configName));
        log.info("读取设备配置文件:{}", configFile);
        return ThreadSafeFileUtils.readFileToString(configFile);
    }

}
