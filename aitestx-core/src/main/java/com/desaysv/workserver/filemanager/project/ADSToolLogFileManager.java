package com.desaysv.workserver.filemanager.project;

import java.io.File;

public class ADSToolLogFileManager  extends ProjectFileManager {
    private File adsToolLogFileFolder; // 测试日志文件夹

    public ADSToolLogFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        File logFolder = createFolder(fileDbPath, "logs");
        adsToolLogFileFolder = createFolder(logFolder, "ADSToolLogs");
    }

    public File getFolder(String folder) {
        return new File(adsToolLogFileFolder, folder);
    }

}
