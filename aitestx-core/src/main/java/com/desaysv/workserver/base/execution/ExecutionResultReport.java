package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.operation.base.OperationsReport;
import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 执行脚本测试结果
 */
@Data
public class ExecutionResultReport {

    //case整体测试结果
    private TestResultReport testResultReport;

    //case步骤测试结果
    private Map<Integer, OperationsReport> operationsReportMap = new ConcurrentHashMap<>(); //行号，测试结果 TODO：改成uuid：测试结果

}
