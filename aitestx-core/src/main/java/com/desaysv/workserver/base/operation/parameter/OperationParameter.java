package com.desaysv.workserver.base.operation.parameter;

import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.NumberUtils;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 11:43
 * @description : 操作参数
 * @modified By :
 * @since : 2022-3-16
 */
@Slf4j
public class OperationParameter extends HashmapParameter {

    public String getString(Object key) {
        return (String) get(key);
    }

    public boolean getBoolean(Object key) {
        return (boolean) getOrDefault(key, false);
    }

    public <T extends Number> T getValue(Object key, int defaultValue, Class<T> targetClass)
            throws OperationParameterExtractException {
        Object value = getOrDefault(key, defaultValue);
        return getValueToTargetClass(value, targetClass);
    }

    public <T extends Number> T getValue(Object key, Class<T> targetClass) throws OperationParameterExtractException {
        Object value = get(key);
        if (value == null) {
            return null;
        }
        return getValueToTargetClass(value, targetClass);
    }


    private <T extends Number> T getValueToTargetClass(Object value, Class<T> targetClass) throws OperationParameterExtractException {
        T tValue;
        try {
            tValue = NumberUtils.convertNumberToTargetClass((Number) value, targetClass);
        } catch (ClassCastException e) {
            tValue = NumberUtils.parseNumber(String.valueOf(value), targetClass);
        } catch (NumberFormatException ex) {
            throw new OperationParameterExtractException(ex);
        }
        return tValue;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + super.toString();
    }

    public static void main(String[] args) {
        OperationParameter operationParameter = new OperationParameter();
        try {
            operationParameter.put("a", "b");
            int value = operationParameter.getValue("a", 1, Integer.class);
            System.out.println("value=" + value);
        } catch (OperationParameterExtractException e) {
            log.error(e.getMessage(), e);
        }
    }
}
