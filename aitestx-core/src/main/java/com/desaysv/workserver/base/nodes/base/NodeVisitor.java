package com.desaysv.workserver.base.nodes.base;

public interface NodeVisitor {

    /**
     * 访问节点
     *
     * @param node        节点
     * @param nodeContext 节点上下文
     * @return 是否继续访问
     */
    boolean visit(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification;

    /**
     * 访问节点完成
     *
     * @param node        节点
     * @param nodeContext 节点上下文
     */
    void visitCompleted(LoopNode node, NodeContext nodeContext);

}
