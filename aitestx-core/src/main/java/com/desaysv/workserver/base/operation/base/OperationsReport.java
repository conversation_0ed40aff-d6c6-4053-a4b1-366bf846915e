package com.desaysv.workserver.base.operation.base;

import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.TestResultReport;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-29 13:37
 * @description :
 * @modified By :
 * @since : 2022-7-29
 */
@Getter
@Setter
public class OperationsReport extends TestResultReport {

    private Object testResult;

    public static OperationsReport report(int row, Map<Integer, ExecuteResult> executeResults) {
        List<OperationResult> operationResultList = new ArrayList<>();
        for (Map.Entry<Integer, ExecuteResult> entry : executeResults.entrySet()) {
            ExecuteResult executeResult = entry.getValue();
            if (executeResult.getOperationResults() == null) {
                continue;
            }
            List<OperationResult> operationResult = executeResult.getOperationResults().get(row);
            if (operationResult == null) {
                continue;
            }
            operationResultList.addAll(operationResult);
        }
        return new OperationsReport().report(operationResultList);
    }

    private OperationsReport report(List<OperationResult> operationResultList) {
        sumCount = operationResultList.size();
        passCount = (int) operationResultList.stream().filter(Objects::nonNull).filter(OperationResult::isOk).count();
        failCount = sumCount - passCount;
        return this;
    }

}