package com.desaysv.workserver.base.operation.base;

import com.desaysv.workserver.base.operation.method.CommonOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.CommonOperationParameter;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.utils.MapToObj;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 11:30
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
@Component
@Lazy
@Slf4j
public class CommonOperationFactory implements OperationAbstractFactory {

    @Override
    public OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        if (operationTarget.isEmpty()) {
            return null;
        }
        OperationTarget commonOperationTarget;
        try {
            commonOperationTarget = MapToObj.mapToObj(operationTarget, CommonOperationTarget.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            commonOperationTarget = new CommonOperationTarget();
        }
        return commonOperationTarget;
    }

    @Override
    public OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        OperationMethod commonOperationMethod;
        try {
            commonOperationMethod = MapToObj.mapToObj(operationMethod, CommonOperationMethod.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            commonOperationMethod = new CommonOperationMethod();
        }
        return commonOperationMethod;
    }

    @Override
    public OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        OperationParameter commonOperationParameter;
        try {
            commonOperationParameter = MapToObj.mapToObj(operationParameter, CommonOperationParameter.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            commonOperationParameter = new CommonOperationParameter();
            commonOperationParameter.putAll(operationParameter);
        }
        return commonOperationParameter;
    }
}
