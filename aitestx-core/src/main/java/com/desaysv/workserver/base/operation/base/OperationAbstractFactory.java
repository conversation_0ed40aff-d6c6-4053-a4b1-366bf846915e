package com.desaysv.workserver.base.operation.base;

import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.utils.MapToObj;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 14:20
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
public interface OperationAbstractFactory {
    Logger log = LogManager.getLogger(OperationAbstractFactory.class.getSimpleName());

    default OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        OperationTarget theOperationTarget;
        try {
            theOperationTarget = MapToObj.mapToObj(operationTarget, OperationTarget.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            theOperationTarget = new OperationTarget();
        }
        return theOperationTarget;
    }

    default OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        OperationMethod theOperationMethod;
        try {
            theOperationMethod = MapToObj.mapToObj(operationMethod, OperationMethod.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            theOperationMethod = new OperationMethod();
        }
        return theOperationMethod;
    }

    default OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        OperationParameter theOperationParameter;
        try {
            theOperationParameter = MapToObj.mapToObj(operationParameter, OperationParameter.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            theOperationParameter = new OperationParameter();
            theOperationParameter.putAll(operationParameter);
        }
        return theOperationParameter;
    }

    //TODO:后续考虑不展开为Device/Image/Common/OperationTarget，而是以OperationTarget为基础
    default Operation createOperation(OperationJsonObject operationJSONObject) {
        //FIXME：使用Operation代替OperationJsonObject
        Operation operation = Operation.newInstance();
        operation.setOperationType(operationJSONObject.getOperationType());
        operation.setRetry(operationJSONObject.getRetry());
        operation.setOperationObject(operationJSONObject.getOperationObject());
        operation.setUuid(operationJSONObject.getUuid());
        operation.setOperationTarget(createOperationTarget(operationJSONObject.getOperationTarget()));
        operation.setOperationMethod(createOperationMethod(operationJSONObject.getOperationMethod()));
        operation.setOperationParameter(createOperationParameter(operationJSONObject.getOperationParameter()));
        operation.setFriendlyOperationObject(operationJSONObject.getFriendlyOperationObject());
        operation.setAnnotated(operationJSONObject.isAnnotated());
        operation.setBackground(operationJSONObject.isBackground());
        operation.setLineNo(operationJSONObject.getLineNo());
        operation.setLabel(operationJSONObject.getLabel());
        return operation;
    }

}
