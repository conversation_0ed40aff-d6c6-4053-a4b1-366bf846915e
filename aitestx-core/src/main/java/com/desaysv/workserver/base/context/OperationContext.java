package com.desaysv.workserver.base.context;

import com.desaysv.workserver.base.operation.base.CaseInfo;
import lombok.Data;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-14 14:07
 * @description :
 * @modified By :
 * @since : 2022-5-14
 */
@Data
public class OperationContext {

    private CaseInfo caseInfo;

    private int timeout;

    private int beforeWait;

    private int afterWait;

    public String getCaseName() {
        return caseInfo != null ? caseInfo.getCaseName() : "";
    }

}
