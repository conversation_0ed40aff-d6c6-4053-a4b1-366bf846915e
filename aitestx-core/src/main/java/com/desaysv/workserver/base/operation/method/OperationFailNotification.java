package com.desaysv.workserver.base.operation.method;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import lombok.Getter;

@Getter
public class OperationFailNotification extends Exception {

    private final OperationResult operationResult;

    public OperationFailNotification(OperationResult operationResult) {
        super(operationResult.getMessage());
        this.operationResult = operationResult;
    }

    public OperationFailNotification(String format, Object... args) {
        super(String.format(format.replace("{}", "%s"), args));
        operationResult = new OperationResult();
        operationResult.setOk(false);
        operationResult.setMessage(String.format(format.replace("{}", "%s"), args));
    }

    public OperationFailNotification(String message) {
        super(message);
        operationResult = new OperationResult();
        operationResult.setOk(false);
        operationResult.setMessage(message);
    }

    public OperationFailNotification(Exception e) {
        super(e.getMessage());
        operationResult = new OperationResult();
        operationResult.setOk(false);
        operationResult.setMessage(e.getMessage());
        operationResult.setData(e);
    }
}
