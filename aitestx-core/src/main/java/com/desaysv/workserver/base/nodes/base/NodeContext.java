package com.desaysv.workserver.base.nodes.base;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.operation.base.Operation;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class NodeContext {

    private NodeListener nodeListener;
    private ExecutionContext executionContext;
    private CycleChangeContext cycleChangeContext;
    private Map<Integer, ExecuteResult> executeResults; //cycle：ExecuteResult
    private boolean failCallback = false;
    private LoopNode node;
    private List<Operation> operationList; // Add this line

    private final LayerCycle layerCycle = new LayerCycle();

    @Setter(AccessLevel.NONE)
    private Map<Integer, Integer> cycleMap = new ConcurrentHashMap<>();

    public int getFirstLayerTestCycle() {
        return cycleMap.get(0);
    }

    public void clear() {
        cycleMap.clear();
    }

}
