package com.desaysv.workserver.base.operation.base;

import com.desaysv.workserver.base.context.OperationContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

//FIXME：取消中间转换环节
@EqualsAndHashCode(callSuper = true)
@Data
public class ExecutionJsonObject extends ActionJsonObject {

    private int executionIndex;
    private int testCycle;
    private OperationContext operationContext;
    private List<OperationJsonObject> operationList;

}
