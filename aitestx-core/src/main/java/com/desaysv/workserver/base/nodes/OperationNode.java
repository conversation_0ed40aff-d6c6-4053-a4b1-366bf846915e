package com.desaysv.workserver.base.nodes;

import com.desaysv.workserver.base.nodes.base.*;
import com.desaysv.workserver.base.operation.base.Operation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 操作步骤节点
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Slf4j
public class OperationNode extends LoopNode {

    public OperationNode() {
        this(null);
    }

    public OperationNode(Operation operation) {
        super(operation);
        setNodeType(NodeType.OPERATION_NODE);
    }

    @Override
    public void accept(NodeVisitor visitor, LoopNode node, NodeContext nodeContext) throws NodeTerminateSignal, LoopBreakNotification {
        boolean isContinue = visitor.visit(node, nodeContext);
        if (!isContinue) {
            throw new NodeTerminateSignal();
        }
        if (node.hasChild()) {
            List<LoopNode> loopNodes;
            if (node.isRandomLoop()) {
                //随机循环
                List<LoopNode> originalNodes = node.getNodes();
                loopNodes = new ArrayList<>(originalNodes);
                Collections.shuffle(loopNodes);
            } else {
                loopNodes = node.getNodes();
            }
            int testCycle = node.getCycle();
            boolean infinite = testCycle == -1;
            for (int i = 0; infinite || i < testCycle; i++) {
                int currentCycle = i + 1;
                if (nodeContext.getNodeListener() != null) {
                    if (node.getLayer() == 0) {
                        nodeContext.setCycleChangeContext(nodeContext.getNodeListener().topLayerCycleChangeStarted(currentCycle));
                    }
                    nodeContext.getNodeListener().cycleChanged(node.getLayer(), currentCycle);
                }
                nodeContext.getLayerCycle().setLayer(node.getLayer());
                nodeContext.getLayerCycle().setCycle(currentCycle);
                nodeContext.getCycleMap().put(node.getLayer(), currentCycle);
                try {
                    for (LoopNode subNode : loopNodes) {
                        accept(visitor, subNode, nodeContext);
                    }
                } catch (LoopBreakNotification e) {
                    log.info("循环中断");
                    break;
                }
                if (node.getLayer() == 0) {
                    if (nodeContext.getNodeListener() != null) {
                        nodeContext.getNodeListener().topLayerCycleChangeEnded();
                    }
                }
            }
        }
    }

    @Override
    public void accept(NodeVisitor visitor, NodeContext nodeContext) throws LoopBreakNotification {
        try {
            if (nodeContext == null) {
                nodeContext = new NodeContext();
            }
            accept(visitor, this, nodeContext);
        } catch (NodeTerminateSignal ignored) {

        }
        visitor.visitCompleted(this, nodeContext);
        //TODO：产生节点结束回调
    }

}
