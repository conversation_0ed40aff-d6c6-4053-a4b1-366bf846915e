package com.desaysv.workserver.base.operation.method;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-15 19:26
 * @description : 图片操作方法
 * @modified By : lwj
 * @since : 2022-3-16
 */
public class ImageOperationMethod extends UIOperationMethod {
    public static OperationMethod Keyword(String keyword) {
        OperationMethod operationMethod = new ImageOperationMethod();
        operationMethod.setKeyword(keyword);
        registerMethod(keyword, UIOperationMethod.class);
        return operationMethod;
    }

    //智能校准点击
//    public static final OperationMethod SMART_CALIBRATION = Keyword("smartCalibration");
    //测试相似度
    public static final OperationMethod TEST_SIMILARITY = Keyword("testSimilarity");
    public static final OperationMethod ENABLE_BINARIZATION = Keyword("enableBinarization");
    //保存图片
    public static final OperationMethod SAVE_IMAGE = Keyword("saveImage");
    public static final OperationMethod CHECK_CHANGE = Keyword("touchAndCheckDisplayChange");
    //断言
    public static final OperationMethod MUST_APPEAR = Keyword("mustAppear");
    public static final OperationMethod MUST_DISAPPEAR = Keyword("mustDisappear");
    public static final OperationMethod WAIT_APPEAR = Keyword("waitAppear");
    public static final OperationMethod WAIT_DISAPPEAR = Keyword("waitDisappear");
    public static final OperationMethod VISION_GUIDE_CLICK = Keyword("visionGuideClick");
    public static final OperationMethod WAIT_CLICK = Keyword("waitClick");
    public static final OperationMethod RANDOM_CLICK = Keyword("randomClick");
    public static final OperationMethod RANDOM_SWIPE = Keyword("randomSwipe");
    public static final OperationMethod SET_EXPOSURE_AUTO_MODE = Keyword("setExposureAutoMode");
    public static final OperationMethod SET_CAMERA_PARAMETERS = Keyword("setCameraParameters");
    public static final OperationMethod SET_REVERSE_X = Keyword("setReverseX");
    public static final OperationMethod SET_REVERSE_Y = Keyword("setReverseY");
    public static final OperationMethod START_GRABBING = Keyword("startGrabbing");
    public static final OperationMethod STOP_GRABBING = Keyword("stopGrabbing");
    public static final OperationMethod SET_CAMERA_FRAME_RATE = Keyword("setCameraFrameRate");
    public static final OperationMethod SET_CAMERA_CALIBRATION = Keyword("setCameraCalibration");
    public static final OperationMethod GET_CAMERA_PARAMETERS = Keyword("getCameraParameters");
    public static final OperationMethod UPDATE_CONFIG = Keyword("updateConfig");
    public static final OperationMethod LOAD_CONFIG = Keyword("loadConfig");
    public static final OperationMethod FAIL_VIDEO = Keyword("startRecording");
    public static final OperationMethod START_BACKTRACK_VIDEO = Keyword("startBacktrackVideo");
    public static final OperationMethod STOP_BACKTRACK_VIDEO = Keyword("stopBacktrackVideo");
    public static final OperationMethod LIGHT_BLINKING = Keyword("lightBlinking");
    public static final OperationMethod CAMERA_SCREEN_SHOOT = Keyword("cameraScreenShoot");

}
