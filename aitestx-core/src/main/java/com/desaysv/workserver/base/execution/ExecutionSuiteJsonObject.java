package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.operation.base.ActionJsonObject;
import com.desaysv.workserver.base.operation.base.ClientInfo;
import com.desaysv.workserver.base.operation.base.ExecutionJsonObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-14 15:24
 * @description :
 * @modified By :
 * @since : 2022-5-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExecutionSuiteJsonObject extends ActionJsonObject {
    private int testCycle;
    private boolean debugModeEnabled;
    private ClientInfo clientInfo;
    private List<ExecutionJsonObject> executionList;
}
