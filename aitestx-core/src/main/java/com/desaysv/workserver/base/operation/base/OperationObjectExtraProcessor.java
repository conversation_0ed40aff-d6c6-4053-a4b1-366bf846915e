package com.desaysv.workserver.base.operation.base;

import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.filter.ExtraProcessor;
import com.desaysv.workserver.utils.ReflectUtils;

import java.util.Arrays;

public class OperationObjectExtraProcessor implements ExtraProcessor {

    private volatile static OperationObjectExtraProcessor processor;

    public static OperationObjectExtraProcessor getInstance() {
        if (processor == null) {
            synchronized (OperationObjectExtraProcessor.class) {
                if (processor == null) {
                    processor = new OperationObjectExtraProcessor();
                }
            }
        }
        return processor;
    }

    @Override
    public void processExtra(Object o, String s, Object o1) {
        String[] fieldNames = ReflectUtils.getFiledNames(o);
        if (!Arrays.asList(fieldNames).contains(s)) {
            throw new JSONException("JSON解析错误!");
        }
    }
}
