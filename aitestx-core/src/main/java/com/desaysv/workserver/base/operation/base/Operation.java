package com.desaysv.workserver.base.operation.base;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.operation.method.CommonOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethodType;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.utils.StrUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 13:33
 * @description :
 * @modified By :
 * @since : 2022-3-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class Operation extends JsonAction {

    public final static String FRIENDLY_DISPLAY_FIELD_NAME = "friendlyString";
    public final static String DEVICE_CHANNEL_OFFICIAL_NAME = "deviceChannel";

    @JSONField(ordinal = 1)
    private Integer operationType;

    @JSONField(ordinal = 2)
    private OperationTarget operationTarget; //目标设备

    @JSONField(ordinal = 3)
    private OperationMethod operationMethod; //关键字操作方法

    @JSONField(ordinal = 4)
    private Object operationObject; //参数

    @JSONField(ordinal = 5)
    private OperationParameter operationParameter;

    @JSONField(ordinal = 6)
    private int retry = 0;

    @JSONField(ordinal = 7)
    private Object friendlyOperationObject;

    @JSONField(ordinal = 8)
    private boolean annotated; //注释

    @JSONField(ordinal = 9)
    private boolean background = false; //是否后台运行

    @JSONField(ordinal = 10)
    private OperationLabel label; //标签

    @JSONField(serialize = false)
    private int lineNo; //所属行，前端（serialize = false 不存储到文件）

    private int parentLineNo; //所属关联行（后端专属）

    @JSONField(serialize = false)
    private String uuid; //步骤UUID

    @JSONField(serialize = false)
    private String pairedUuid;

    @JSONField(serialize = false)
    private int level; // 添加层次属性


    public void setOperationObject(Object operationObject) {
        if (operationObject instanceof BigDecimal) {
            this.operationObject = ((BigDecimal) operationObject).doubleValue();
        } else {
            this.operationObject = operationObject;
        }
    }

    @JSONField(serialize = false)
    public boolean isSubOperationCollection() {
        return operationMethod.getMethodType().equals(OperationMethodType.CUSTOMIZE_FUNCTION_BEGIN.getValue());
    }

    /**
     * 静态实例工厂
     *
     * @return
     */
    @JSONField(serialize = false)
    public static Operation newInstance() {
        return new Operation();
    }

    @Override
    public String toString() {
        String target = "";
        if (operationTarget != null) {
            target = StrUtils.isEmpty(operationTarget.getAliasName()) ? "" : String.format("%s | ", operationTarget.getAliasName());
        }
        String method = String.format("%s>>%s",
                operationMethod.getKeyword(),
                operationObject);
        return String.format("%s%s", target, method);
    }

    public static void main(String[] args) {
        Operation operation = new Operation();
        operation.setOperationType(3);
        operation.setOperationMethod(CommonOperationMethod.BEGIN_LOOP);
        System.out.println(JSON.toJSONString(operation));
    }
}
