package com.desaysv.workserver.base.suite;

import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.operation.base.ClientInfo;
import com.desaysv.workserver.base.operation.base.JsonAction;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ExecutionSuite extends JsonAction {

    private int testCycle; //总循环次数
    private ClientInfo clientInfo; //附带信息
    private List<Execution> executionList; //每一行的脚本案例
    private boolean debugModeEnabled; //是否为调试

}
