package com.desaysv.workserver.constants;

import com.desaysv.workserver.utils.ReflectUtils;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备型号常量
 */
public class DeviceModel {

    /**
     * Android设备
     */
    public static final class Android {
        private static final String type = DeviceType.DEVICE_ANDROID;
        public static final String USB_ANDROID = "UsbAndroid";
        public static final String WIFI_ANDROID = "WifiAndroid";
        public static final String ADB_HUD = "AdbHud";
    }

    /**
     * QNX设备
     */
    public static final class Qnx {
        private static final String type = DeviceType.DEVICE_QNX;
        public static final String QNX_INSTRUMENT = "QnxInstrument";
    }

    /**
     * DAQ设备
     */
    public static final class Daq {
        private static final String type = DeviceType.DEVICE_DAQ;
        public static final String KEYSIGHT_34461A = "Keysight_34461A";
        public static final String USB3200N = "USB3200N";
        public static final String UT8806  = "Ut8806";
    }

    /**
     * 声音采集
     */
    public static final class SoundCard {
        private static final String type = DeviceType.DEVICE_SOUND_CARD;
        public static final String AUXIN_SOUND_DEVICE = "AuxInSoundDevice";
        public static final String USB4704_DEVICE = "USB4704";
    }

    /**
     * 电阻
     */
    public static final class Resistance {
        private static final String type = DeviceType.DEVICE_RESISTANCE;
        public static final String INSTRUMENT_RESISTANCE = "Instrument_Resistance";
        public static final String REMOTE_RESISTANCE = "Remote_Resistance";
        public static final String QR10X = "QR10X";
    }

    /**
     * 机械臂
     */
    public static final class Robot {
        private static final String type = DeviceType.DEVICE_ROBOT;
        public static final String DOBOT_MG400 = "Dobot_MG400";
        public static final String DOBOT_MAGICIAN = "Dobot_Magician";
    }

    /**
     * 电源
     */
    public static final class Power {
        private static final String type = DeviceType.DEVICE_POWER;
        public static final String IT68xx = "IT68xx";
        public static final String IT6322 = "IT6322";
        public static final String IT69xx = "IT69xx";
        public static final String IT63xx = "IT63xx";
        public static final String POWER_BOX = "PowerBox"; //电源控制盒
        public static final String KIKUSUI = "Kikusui"; //菊水电源
        public static final String N6700 = "N6700"; //N6700高精度电源
        public static final String MPS3610H = "MPS3610H";
        public static final String IT65xx = "IT65xx";
        public static final String IT67xx = "IT67xx";
        public static final String PVD8V50E = "PVD8V50E";
    }

    public static final class Speaker {
        private static final String type = DeviceType.DEVICE_SPEAKER;
        public static final String SPEAKER = "Speaker";
    }

    /**
     * 相机
     */
    public static final class Camera {
        private static final String type = DeviceType.DEVICE_CAMERA;
        public static final String USB_CAMERA = "USBCamera";
        //        public static final String BASLER_CAMERA = "BaslerCamera";
        public static final String HIK_CAMERA = "HikCamera";
    }

    /**
     * 串口
     */
    public static final class Serial {
        private static final String type = DeviceType.DEVICE_SERIAL;
        public static final String PORT_SERIAL = "SerialPort";
        public static final String PORT_PARALLEL = "ParallelPort";
        public static final String PORT_I2C = "I2CPort";
        public static final String PORT_RS485 = "RS485Port";
        public static final String PORT_RAW = "RawPort";
    }

    /**
     * USB切换器
     */
    public static final class UsbSwitch {
        private static final String type = DeviceType.DEVICE_USB_SWITCH;
        public static final String USB_SWITCH = "UsbSwitch";
    }

    /**
     * USB机械插拔设备类型
     */
    public static final class UsbPlug {
        private static final String type = DeviceType.DEVICE_PLUG;
        public static final String USB_PLUG = "UsbPlug";
    }

    /**
     * 继电器
     */
    public static final class ElectricRelay {
        private static final String type = DeviceType.DEVICE_ELECTRIC_RELAY;
        public static final String JYDAM1600C = "JY-DAM1600C";
        public static final String ZQWL_IO_1BXRC32 = "ZQWL-IO-1BXRC32";
        public static final String ZQWL_IO_1CX5R4 = "ZQWL-IO-1CX5R4";
        public static final String ZQWL_IO_1BX3C8 = "ZQWL-IO-1BX3C8";
        public static final String ZQWL_IO_1CNRR4 = "ZQWL-IO-1CNRR4";
    }

    /**
     * CAN总线
     */
    public static final class Bus {
        private static final String type = DeviceType.DEVICE_CAN;
        public static final String ZLG_USBCANFD_200U = "ZCAN_USBCANFD_200U";
        public static final String ZLG_CANFDDTU_400UEWGR = "ZLG_CANFDDTU_400UEWGR";
        public static final String ZLG_USBCAN_I_MINI = "ZCAN_USBCAN_I_MINI";
        public static final String VECTOR_CAN = "VECTOR_CAN";
        public static final String VECTOR_LIN = "VECTOR_LIN";
        public static final String VECTOR_ETHERNET = "VECTOR_ETHERNET";
        public static final String NI_CAN_8502 = "NI_CAN_8502";
        public static final String TC1013 = "TC1013";
        public static final String TC1016 = "TC1016";
        public static final String TC1026 = "TC1026";
        public static final String TC1034 = "TC1034";
        public static final String TOSUN_LIN = "TOSUN_LIN";
        public static final String TOSUN_ETHERNET = "TOSUN_ETHERNET";

        public static void main(String[] args) {
            System.out.println(Utils.contains(Bus.class, "ZLG_USBCANFD_MINI_I"));
        }
    }

    /**
     * 测试箱
     */
    public static final class TestBox {
        private static final String type = DeviceType.DEVICE_TEST_BOX;
        public static final String TEST_BOX = "TestBox";
        public static final String LIGHT_TEST_BOX = "lightTestBox";
        public static final String RZCU_TEST_BOX = "RZCUTestBox";
    }

    /**
     * 自动点击器
     */
    public static final class AutoClicker {
        private static final String type = DeviceType.DEVICE_AUTO_CLICKER;
        public static final String AUTO_CLICKER = "AutoClicker";
    }

    /**
     * USB I2C
     */
    public static final class UsbI2C {
        public static final String USB_I2C = "UsbI2C";
    }

    /**
     * TCP服务器
     */
    public static final class TcpServer {
        private static final String type = DeviceType.DEVICE_TCP_SERVER;
        public static final String TCP_SERVER = "TcpServer";
    }

    public static final class Oscilloscope {
        public static final String RIGOL_800 = "RIGOL_800";
        public static final String KEYSIGHT_4000A = "KEYSIGHT_4000A";
        public static final String SDS_5000X = "SDS5000X"; //BIC示波器
        public static final String SDS_6000X = "SDS6000X";
    }

    public static final class SignalGenerator {
        public static final String TK_AFG_1022 = "TK_AFG_1022"; //BIC信号发生器
    }

    public static final class ElectronicLoad {
        public static final String N68000 = "N68000";
    }

    public static final class VideoCapture {
        public static final String VIDEO_CAPTURE = "VideoCapture";

    }

    public static final class UdpDevice {
        public static final String UDP_DEVICE = "UdpTarget";
    }

    public static final class TcpClient {
        public static final String TCP_CLIENT = "TcpClient";
    }

    public static final class DcCollector {
        public static final String DC_COLLECTOR_24_DEVICE = "ZH4424DcCollector";
    }

    /**
     * 工具类
     */
    public static class Utils {
        public static boolean contains(Class<?> clazz, String deviceModel) {
            return ReflectUtils.getFieldValues(clazz).contains(deviceModel);
        }
    }

    /**
     * 用于重命名Model
     */
    @Data
    @AllArgsConstructor
    public static class DeviceModelConverter {
        private String oldName;
        private String newName;
    }

    public static List<String> getAllDeprecatedDeviceModels() {
        return new ArrayList<>(Arrays.asList("IT6831", "IT6300AtoB", "IT6932A", "powerControlBox"));
    }

    public static List<DeviceModelConverter> getAllDeviceModelConverterList() {
        List<DeviceModelConverter> converters = new ArrayList<>();
        DeviceModelConverter converter1 = new DeviceModelConverter("DOBOT_MG400", Robot.DOBOT_MG400);
        DeviceModelConverter converter2 = new DeviceModelConverter("KEYSIGHT_34461A", Daq.KEYSIGHT_34461A);
        DeviceModelConverter converter3 = new DeviceModelConverter("USB2Camera", Camera.USB_CAMERA);
        DeviceModelConverter converter4 = new DeviceModelConverter("AdbAndroid", Android.USB_ANDROID);
        DeviceModelConverter converter5 = new DeviceModelConverter("ZlgCan", Bus.ZLG_USBCAN_I_MINI);
        DeviceModelConverter converter6 = new DeviceModelConverter("powerBox", Power.POWER_BOX);
        converters.add(converter1);
        converters.add(converter2);
        converters.add(converter3);
        converters.add(converter4);
        converters.add(converter5);
        converters.add(converter6);
        return converters;
    }

    public static List<String> getAllDeviceModels() throws IllegalAccessException {
        List<String> deviceModels = new ArrayList<>();
        Class<?>[] innerClazz = DeviceModel.class.getDeclaredClasses();
        for (Class<?> clazz : innerClazz) {
            Field[] fields = clazz.getFields();
            for (Field field : fields) {
                if (Modifier.isStatic(field.getModifiers())) {
                    String value = (String) field.get(clazz);
                    deviceModels.add(value);
                }
            }
        }
        return deviceModels;
    }

    public static List<String> getAllDeviceModelsByType(String deviceType) throws IllegalAccessException {
        List<String> deviceModels = new ArrayList<>();
        Class<?>[] innerClazz = DeviceModel.class.getDeclaredClasses();
        for (Class<?> clazz : innerClazz) {
            Field typeField = ReflectUtils.getField(clazz, "type");
            if (typeField != null) {
                typeField.setAccessible(true);
                if (typeField.get(clazz).equals(deviceType)) {
                    Field[] fields = clazz.getFields();
                    for (Field field : fields) {
                        if (Modifier.isStatic(field.getModifiers())) {
                            String value = (String) field.get(clazz);
                            deviceModels.add(value);
                        }
                    }
                }
            }
        }
        return deviceModels;
    }

    public static String getDeviceTypeByModel(String modelName) throws IllegalAccessException {
        Class<?>[] innerClazz = DeviceModel.class.getDeclaredClasses();
        for (Class<?> clazz : innerClazz) {
            Field typeField = ReflectUtils.getField(clazz, "type");
            if (typeField != null) {
                typeField.setAccessible(true);
                String type = (String) typeField.get(null); // 静态字段不需要实例
                Field[] fields = clazz.getFields();
                for (Field field : fields) {
                    if (Modifier.isStatic(field.getModifiers()) && field.getType() == String.class) {
                        String fieldValue = (String) field.get(null);
                        if (fieldValue.equals(modelName)) {
                            return type;
                        }
                    }
                }
            }
        }
        return null; // 如果没有找到对应的模型，返回null
    }


    public static void main(String[] args) {
        try {
            System.out.println(DeviceModel.getAllDeviceModelsByType(DeviceType.DEVICE_ANDROID));
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

}
