package com.desaysv.workserver.constants;

import org.springframework.lang.Nullable;

public class SystemEnv {

    private static final String USB_PLUG_ADDRESS = "FLYTEST_USB_PLUG_ADDRESS";
    private static final String IMAGE_ACQ_INTERVAL = "FLYTEST_IMAGE_ACQ_INTERVAL";
    private static final String CAMERA_EXPOSURE_TIME = "FLYTEST_CAMERA_EXPOSURE_TIME";
    private static final String SAVE_ALL_CAPTURE_IMAGE = "FLYTEST_SAVE_ALL_IMAGE";

    //是否保存失败图片
    private static final String SAVE_FAIL_IMAGE = "FLYTEST_SAVE_FAIL_IMAGE";

    //模型地址
    private static final String LOCAL_LLM_URL = "LOCAL_DESAYSV_LLM_URL";

    public static boolean isEmpty(@Nullable Object str) {
        return str == null || "".equals(str);
    }

    public static String getEnvValue(String envName, Object defaultValue) {
        String value = System.getenv(envName);
        return isEmpty(value) ? String.valueOf(defaultValue) : value;
    }

    public static boolean isEnvValueExist(String envName) {
        return !isEmpty(System.getenv(envName));
    }


    public static int getUsbPlugAddress() {
        return Integer.parseInt(getEnvValue(SystemEnv.USB_PLUG_ADDRESS, 0));
    }

    public static int getImageAcqInterval() {
        return Integer.parseInt(getEnvValue(SystemEnv.IMAGE_ACQ_INTERVAL, 100));
    }

    public static int getCameraExposureTime() {
        return Integer.parseInt(getEnvValue(SystemEnv.CAMERA_EXPOSURE_TIME, 8000));
    }

    public static boolean isExposureTimeSet() {
        return isEnvValueExist(SystemEnv.CAMERA_EXPOSURE_TIME);
    }

    public static boolean isSaveFailImage() {
        return Boolean.parseBoolean(getEnvValue(SystemEnv.SAVE_FAIL_IMAGE, "true"));
    }

    public static boolean isSaveALlCaptureImage() {
        return Boolean.parseBoolean(getEnvValue(SystemEnv.SAVE_ALL_CAPTURE_IMAGE, "false"));
    }

    public static String getLocalLlmUrl() {
        return getEnvValue(SystemEnv.LOCAL_LLM_URL, "");
    }

}
