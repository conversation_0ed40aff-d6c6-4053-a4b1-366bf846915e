package com.desaysv.workserver.aspect;


import com.desaysv.workserver.annotation.MockReturnBoolean;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
//no lazy
public class MockAop {

    @Pointcut("@annotation(com.desaysv.workserver.annotation.MockReturnBoolean)")
    public void mockReturnBoolean() {

    }

    @Around("mockReturnBoolean()")
    public Object aroundMockReturnBoolean(ProceedingJoinPoint joinPoint) {
        Class<?> targetCls = joinPoint.getTarget().getClass();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method;
        try {
            method = targetCls.getDeclaredMethod(methodSignature.getName(), methodSignature.getParameterTypes());
            MockReturnBoolean mock = method.getAnnotation(MockReturnBoolean.class);
            return mock.returnValue();
        } catch (NoSuchMethodException e) {
            log.error(e.getMessage(), e);
            try {
                return joinPoint.proceed(joinPoint.getArgs());
            } catch (Throwable ex) {
                log.error(e.getMessage(), ex);
                return false;
            }
        }
    }
}
