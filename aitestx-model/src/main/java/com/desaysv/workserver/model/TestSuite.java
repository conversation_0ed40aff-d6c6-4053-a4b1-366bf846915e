package com.desaysv.workserver.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-10 13:30
 * @description :
 * @modified By :
 * @since : 2022-7-10
 */
@Data
public class TestSuite {

    private Integer id;

    private String uuid;

    private String testProjectName;

    private String testSuiteName;

//    private Integer endPointId;

    private String hardwareVersion;

    private String softwareVersion;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTestTime;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTestTime;

    @Setter(AccessLevel.NONE)
    private Date createTime;

    @Setter(AccessLevel.NONE)
    private Date updateTime;

}
