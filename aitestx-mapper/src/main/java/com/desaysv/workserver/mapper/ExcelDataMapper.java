package com.desaysv.workserver.mapper;

import com.desaysv.workserver.excel.ExcelDataEntity;
import com.desaysv.workserver.excel.ExcelFileEntity;
import com.desaysv.workserver.excel.ExcelSheetEntity;
import com.desaysv.workserver.excel.TestResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ExcelDataMapper {

    void insertExcelFile(ExcelFileEntity excelFile);

    void truncateExcelFileTable();

    void insertExcelSheet(ExcelSheetEntity sheet);

    ExcelSheetEntity findSheetDataBySheetName(String sheetName);

    void insertExcelData(ExcelDataEntity data);

    // 批量插入
    void insertExcelDataBatch(List<ExcelDataEntity> batchData);

    void updateExcelData(ExcelDataEntity data);

    List<ExcelDataEntity> findExcelDataBySheetId(@Param("sheetId") Integer sheetId);

    Integer deleteDataById(@Param("sheetId") Integer sheetId);

    void clearMapColumnData(Map<String, Object> columnDataMap);

    void insertTestResult(TestResult testResult);

    void truncateExcelSheetTable();

    void truncateExcelDataTable();

    void batchInsertExcelData(@Param("list") List<ExcelDataEntity> dataModels);
}