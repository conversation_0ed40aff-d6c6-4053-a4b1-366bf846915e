package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestMachine;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-27 11:10
 * @description : 测试机器mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface TestMachineMapper {
    /**
     * 清空测试机器
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试机器
     *
     * @param testMachineId 测试机器id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer testMachineId);

    /**
     * 插入测试机器
     *
     * @param testMachine 测试机器
     * @return 受影响的行数
     */
    Integer insert(TestMachine testMachine);

    /**
     * 根据id查询测试机器
     *
     * @param testMachineId 测试机器id
     * @return 指定id的测试机器
     */
    TestMachine selectByPrimaryKey(@Param("id") Integer testMachineId);

    /**
     * 查询所有测试机器
     *
     * @return 测试机器列表
     */
    List<TestMachine> selectAll();

    /**
     * 查询记录总数量
     *
     * @param testMachine 测试机器
     * @return 受影响的行数
     */
    Integer selectTotalPage(TestMachine testMachine);

    /**
     * 更新测试机器
     *
     * @param testMachine 测试机器
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(TestMachine testMachine);

    /**
     * 条件查询
     *
     * @param testMachine 测试机器
     * @return 测试机器列表
     */
    List<TestMachine> selectByCondition(TestMachine testMachine);
}
