package com.desaysv.workserver.mapper;


import com.desaysv.workserver.model.Department;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-20 13:37
 * @description : 事业单元Mapper接口
 * @modified By :
 * @since : 2022-4-20
 */
public interface DepartmentMapper {

    /**
     * 清空事业单元
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除事业单元
     *
     * @param departmentId 事业单元id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer departmentId);

    /**
     * 插入事业单元
     *
     * @param department 事业单元
     * @return 受影响的行数
     */
    Integer insert(Department department);

    /**
     * 根据id查询事业单元
     *
     * @param departmentId 事业单元id
     * @return 指定id的事业单元
     */
    Department selectByPrimaryKey(@Param("id") Integer departmentId);

    /**
     * 根据代号查询事业单元
     *
     * @param departmentCode 事业单元代号
     * @return 指定代号的事业单元
     */
    Department selectByCode(@Param("code") Integer departmentCode);

    /**
     * 查询所有事业单元
     *
     * @return 事业单元列表
     */
    List<Department> selectAll();

    /**
     * 查询记录总数量
     *
     * @param department 事业单元
     * @return 受影响的行数
     */
    Integer selectTotalPage(Department department);

    /**
     * 更新事业单元
     *
     * @param department 事业单元
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(Department department);

    /**
     * 条件查询
     *
     * @param department 事业单元
     * @return 事业单元列表
     */
    List<Department> selectByCondition(Department department);
}
