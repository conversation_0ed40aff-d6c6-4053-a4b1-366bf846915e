package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestDeviceModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 13:49
 * @description : 测试设备型号Mapper接口
 * @modified By :
 * @since : 2022-5-6
 */
public interface TestDeviceModelMapper {

    /**
     * 清空测试设备型号
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试设备型号
     *
     * @param deviceModelId 测试设备型号id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer deviceModelId);

    /**
     * 插入测试设备型号
     *
     * @param deviceModel 测试设备型号
     * @return 受影响的行数
     */
    Integer insert(TestDeviceModel deviceModel);

    /**
     * 根据id查询测试设备型号
     *
     * @param deviceModelId 测试设备型号id
     * @return 指定id的测试设备型号
     */
    TestDeviceModel selectByPrimaryKey(@Param("id") Integer deviceModelId);


    /**
     * 根据name查询测试设备型号
     *
     * @param deviceModelName 测试设备型号名
     * @return 指定name的测试设备型号
     */
    TestDeviceModel selectByName(@Param("name") String deviceModelName);

    /**
     * 查询所有测试设备型号
     *
     * @return 测试设备型号列表
     */
    List<TestDeviceModel> selectAll();

    /**
     * 查询记录总数量
     *
     * @param deviceModel 测试设备型号
     * @return 受影响的行数
     */
    Integer selectTotalPage(TestDeviceModel deviceModel);

    /**
     * 更新测试设备型号
     *
     * @param deviceModel 测试设备型号
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(TestDeviceModel deviceModel);

    /**
     * 条件查询
     *
     * @param deviceModel 测试设备型号
     * @return 测试设备型号列表
     */
    List<TestDeviceModel> selectByCondition(TestDeviceModel deviceModel);

}
