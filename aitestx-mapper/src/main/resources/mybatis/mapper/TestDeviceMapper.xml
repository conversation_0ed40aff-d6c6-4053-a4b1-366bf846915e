<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestDeviceMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestDevice">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="port" jdbcType="INTEGER" property="port"/>
        <result column="baud_rate" jdbcType="INTEGER" property="baudRate"/>
        <result column="type_id" jdbcType="INTEGER" property="typeId"/>
        <result column="model_id" jdbcType="INTEGER" property="modelId"/>
        <result column="alias_name" jdbcType="VARCHAR" property="aliasName"/>
        <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode"
                javaType="java.lang.String"
                typeHandler="com.desaysv.workserver.typehandler.SpecialSQLStringTypeHandler"/>
        <result column="parameter" jdbcType="LONGVARCHAR" property="operationParameter"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        test_device
    </sql>

    <sql id="all_columns">
        id, name, port, baud_rate, type_id, model_id, alias_name, unique_code, parameter, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="name!=null and name!=''">
                AND name LIKE '%' || #{name} || '%'
            </if>
            <if test="port!=null">
                AND port=#{port}
            </if>
            <if test="baudRate!=null">
                AND baud_rate=#{baudRate}
            </if>
            <if test="typeId!=null">
                AND type_id=#{typeId}
            </if>
            <if test="modelId!=null">
                AND model_id=#{modelId}
            </if>
            <if test="aliasName!=null and aliasName!=''">
                AND alias_name=#{aliasName}
            </if>
            <if test="uniqueCode!=null and uniqueCode!=''">
                AND unique_code=#{uniqueCode,typeHandler=com.desaysv.workserver.typehandler.SpecialSQLStringTypeHandler}
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="table_name"/>
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.TestDevice"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, name, port, baud_rate, type_id, model_id, alias_name, unique_code, parameter, create_time, update_time)
        values (#{id}, #{name}, #{port}, #{baudRate}, #{typeId}, ${modelId}, #{aliasName},
        #{uniqueCode,typeHandler=com.desaysv.workserver.typehandler.SpecialSQLStringTypeHandler},
        #{operationParameter}, datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.TestDevice">
        update
        <include refid="table_name"/>
        set name = #{name},
        port = #{port},
        baud_rate = #{baudRate},
        type_id = #{typeId},
        model_id = #{modelId},
        alias_name = #{aliasName},
        unique_code = #{uniqueCode,typeHandler=com.desaysv.workserver.typehandler.SpecialSQLStringTypeHandler},
        parameter = #{operationParameter},
        update_time = datetime('now','localtime')
        where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = #{name}
    </select>

    <select id="selectByUniqueCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where unique_code = #{uniqueCode,typeHandler=com.desaysv.workserver.typehandler.SpecialSQLStringTypeHandler}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.TestDevice"
            resultType="java.lang.Integer">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.TestDeviceType"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>

</mapper>