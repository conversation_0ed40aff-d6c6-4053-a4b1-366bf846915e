<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.roi.CoordinatesRoiMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.roi.CoordinatesRoi">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="device_type_id" jdbcType="INTEGER" property="deviceTypeId"/>
        <result column="coordinates_uuid" jdbcType="VARCHAR" property="coordinatesUUID"/>
        <result column="type_id" jdbcType="TINYINT" property="typeId"/>
        <result column="start_x" jdbcType="DOUBLE" property="startX"/>
        <result column="start_y" jdbcType="DOUBLE" property="startY"/>
        <result column="end_x" jdbcType="DOUBLE" property="endX"/>
        <result column="end_y" jdbcType="DOUBLE" property="endY"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        coordinates_roi
    </sql>

    <sql id="all_columns">
        id, device_type_id, coordinates_uuid, type_id, start_x, start_y, end_x, end_y, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="deviceTypeId!=null">
                AND device_type_id=#{deviceTypeId}
            </if>
            <if test="coordinates_uuid!=null and coordinates_uuid!=''">
                AND coordinates_uuid LIKE '%' || #{coordinatesUUID} || '%'
            </if>
            <if test="typeId!=null">
                AND type_id=#{typeId}
            </if>
            <if test="startX!=null">
                AND start_x=#{startX}
            </if>
            <if test="startY!=null">
                AND start_y=#{startY}
            </if>
            <if test="endX!=null">
                AND end_x=#{endX}
            </if>
            <if test="endY!=null">
                AND end_y=#{endY}
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByCoordinatesUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where coordinates_uuid = #{coordinatesUUID}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.roi.CoordinatesRoi"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, device_type_id, coordinates_uuid, type_id, start_x, start_y, end_x, end_y, create_time, update_time)
        values (#{id}, #{deviceTypeId}, #{coordinatesUUID}, #{typeId}, #{startX}, #{startY}, #{endX}, #{endY},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByCoordinatesUUID" parameterType="com.desaysv.workserver.model.roi.CoordinatesRoi">
        update
        <include refid="table_name"/>
        set
        type_id = #{typeId},
        start_x = #{startX},
        start_y = #{startY},
        end_x = #{endX},
        end_y = #{endY},
        update_time = datetime('now','localtime')
        where coordinates_uuid = #{coordinatesUUID}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="selectByCoordinatesUUID" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where coordinates_uuid = #{coordinatesUUID}
    </select>

    <select id="selectByStrictCondition" parameterType="com.desaysv.workserver.model.roi.CoordinatesRoi"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where device_type_id = #{deviceTypeId}
        AND coordinates_uuid = #{coordinatesUUID}
        AND type_id = #{typeId}
        AND start_x = #{startX}
        AND start_y = #{startY}
        AND end_x = #{endX}
        AND end_y = #{endY}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.roi.CoordinatesRoi"
            resultType="java.lang.Long">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.roi.CoordinatesRoi"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>

</mapper>