<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TemplatePictureMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TemplatePicture">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="template_picture_uuid" jdbcType="VARCHAR" property="templatePictureUUID"/>
        <result column="original_picture_uuid" jdbcType="VARCHAR" property="originalPictureUUID"/>
        <result column="device_type_id" jdbcType="INTEGER" property="deviceTypeId"/>
        <result column="device_index" jdbcType="INTEGER" property="deviceIndex"/>
        <result column="device_id" jdbcType="INTEGER" property="deviceId"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        template_picture
    </sql>

    <sql id="all_columns">
        id, name, template_picture_uuid, original_picture_uuid, device_type_id, device_index, device_id, project_id,
        create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="name!=null and name!=''">
                AND name LIKE '%' || #{name} || '%'
            </if>
            <if test="template_picture_uuid!=null and template_picture_uuid!=''">
                AND template_picture_uuid LIKE '%' || #{templatePictureUUID} || '%'
            </if>
            <if test="original_picture_uuid!=null and original_picture_uuid!=''">
                AND original_picture_uuid LIKE '%' || #{originalPictureUUID} || '%'
            </if>
            <if test="deviceTypeId!=null">
                AND device_type_id=#{deviceTypeId}
            </if>
            <if test="deviceIndex!=null">
                AND device_index=#{deviceIndex}
            </if>
            <if test="deviceId!=null">
                AND device_id=#{deviceId}
            </if>
            <if test="projectId!=null">
                AND project_id=#{projectId}
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByPictureTemplateUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where template_picture_uuid = #{templatePictureUUID}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.TemplatePicture"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, name, template_picture_uuid, original_picture_uuid, device_type_id, device_index, device_id, project_id,
        create_time, update_time)
        values (#{id}, #{name}, #{templatePictureUUID}, #{originalPictureUUID}, #{deviceTypeId}, #{deviceIndex},
        #{deviceId}, #{projectId},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByTemplatePictureUUID" parameterType="com.desaysv.workserver.model.TemplatePicture">
        update
        <include refid="table_name"/>
        set name = #{name},
        original_picture_uuid = #{originalPictureUUID},
        device_type_id = #{deviceTypeId},
        device_index = #{deviceIndex},
        device_id = #{deviceId},
        project_id = #{projectId},
        update_time = datetime('now','localtime')
        where template_picture_uuid = #{templatePictureUUID}
    </update>

    <select id="selectByTemplatePictureUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where template_picture_uuid = #{templatePictureUUID}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = #{pictureTemplateName}
        <!--        and device_id = (select id from test_device where unique_code = #{deviceUniqueCode})-->
        and project_id = (select id from test_project where name = #{projectName})
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.TemplatePicture"
            resultType="java.lang.Long">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.TemplatePicture"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>
</mapper>