<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TesterMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.Tester">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        tester
    </sql>

    <sql id="all_columns">
        id, name, password, status, email, phone, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="name!=null and name!=''">
                AND name LIKE '%' || #{name} || '%'
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="table_name"/>
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.Tester"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, name, password, status, email, phone, create_time, update_time)
        values (#{id}, #{name}, #{password}, #{status}, #{email}, #{phone},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.Tester">
        update
        <include refid="table_name"/>
        set name = #{name},
        password = #{password},
        status = #{status},
        email = #{email},
        phone = #{phone},
        update_time = datetime('now','localtime')
        where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = #{name}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.Tester"
            resultType="java.lang.Integer">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.Tester"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>

</mapper>